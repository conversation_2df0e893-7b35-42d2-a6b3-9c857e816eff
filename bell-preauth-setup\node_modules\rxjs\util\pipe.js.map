{"version": 3, "file": "pipe.js", "sourceRoot": "", "sources": ["../../src/util/pipe.ts"], "names": [], "mappings": ";AAAA,qBAAqB,QAAQ,CAAC,CAAA;AAc9B,mCAAmC;AAEnC;IAA2B,aAAkC;SAAlC,WAAkC,CAAlC,sBAAkC,CAAlC,IAAkC;QAAlC,4BAAkC;;IAC3D,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAC5B,CAAC;AAFe,YAAI,OAEnB,CAAA;AAED,eAAe;AACf,uBAAoC,GAA+B;IACjE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACT,MAAM,CAAC,WAA+B,CAAC;IACzC,CAAC;IAED,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;QACrB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,eAAe,KAAQ;QAC5B,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,UAAC,IAAS,EAAE,EAAuB,IAAK,OAAA,EAAE,CAAC,IAAI,CAAC,EAAR,CAAQ,EAAE,KAAK,CAAC,CAAC;IAC7E,CAAC,CAAC;AACJ,CAAC;AAZe,qBAAa,gBAY5B,CAAA", "sourcesContent": ["import { noop } from './noop';\nimport { UnaryFunction } from '../interfaces';\n\n/* tslint:disable:max-line-length */\nexport function pipe<T>(): UnaryFunction<T, T>;\nexport function pipe<T, A>(op1: UnaryFunction<T, A>): UnaryFunction<T, A>;\nexport function pipe<T, A, B>(op1: UnaryFunction<T, A>, op2: UnaryFunction<A, B>): UnaryFunction<T, B>;\nexport function pipe<T, A, B, C>(op1: UnaryFunction<T, A>, op2: UnaryFunction<A, B>, op3: UnaryFunction<B, C>): UnaryFunction<T, C>;\nexport function pipe<T, A, B, C, D>(op1: UnaryFunction<T, A>, op2: UnaryFunction<A, B>, op3: UnaryFunction<B, C>, op4: UnaryFunction<C, D>): UnaryFunction<T, D>;\nexport function pipe<T, A, B, C, D, E>(op1: UnaryFunction<T, A>, op2: UnaryFunction<A, B>, op3: UnaryFunction<B, C>, op4: UnaryFunction<C, D>, op5: UnaryFunction<D, E>): UnaryFunction<T, E>;\nexport function pipe<T, A, B, C, D, E, F>(op1: UnaryFunction<T, A>, op2: UnaryFunction<A, B>, op3: UnaryFunction<B, C>, op4: UnaryFunction<C, D>, op5: UnaryFunction<D, E>, op6: UnaryFunction<E, F>): UnaryFunction<T, F>;\nexport function pipe<T, A, B, C, D, E, F, G>(op1: UnaryFunction<T, A>, op2: UnaryFunction<A, B>, op3: UnaryFunction<B, C>, op4: UnaryFunction<C, D>, op5: UnaryFunction<D, E>, op6: UnaryFunction<E, F>, op7: UnaryFunction<F, G>): UnaryFunction<T, G>;\nexport function pipe<T, A, B, C, D, E, F, G, H>(op1: UnaryFunction<T, A>, op2: UnaryFunction<A, B>, op3: UnaryFunction<B, C>, op4: UnaryFunction<C, D>, op5: UnaryFunction<D, E>, op6: UnaryFunction<E, F>, op7: UnaryFunction<F, G>, op8: UnaryFunction<G, H>): UnaryFunction<T, H>;\nexport function pipe<T, A, B, C, D, E, F, G, H, I>(op1: UnaryFunction<T, A>, op2: UnaryFunction<A, B>, op3: UnaryFunction<B, C>, op4: UnaryFunction<C, D>, op5: UnaryFunction<D, E>, op6: UnaryFunction<E, F>, op7: UnaryFunction<F, G>, op8: UnaryFunction<G, H>, op9: UnaryFunction<H, I>): UnaryFunction<T, I>;\n/* tslint:enable:max-line-length */\n\nexport function pipe<T, R>(...fns: Array<UnaryFunction<T, R>>): UnaryFunction<T, R> {\n  return pipeFromArray(fns);\n}\n\n/* @internal */\nexport function pipeFromArray<T, R>(fns: Array<UnaryFunction<T, R>>): UnaryFunction<T, R> {\n  if (!fns) {\n    return noop as UnaryFunction<any, any>;\n  }\n\n  if (fns.length === 1) {\n    return fns[0];\n  }\n\n  return function piped(input: T): R {\n    return fns.reduce((prev: any, fn: UnaryFunction<T, R>) => fn(prev), input);\n  };\n}\n"]}