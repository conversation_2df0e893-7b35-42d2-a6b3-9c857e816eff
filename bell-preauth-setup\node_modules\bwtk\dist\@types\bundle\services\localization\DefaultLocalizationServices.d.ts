import { AjaxServices, ConfigServices, StoreServices, Store, Localization, LocalizationServices, LoggerServices, LocaleValue, EventStreamServices, FullLocaleValue } from "../../../@types";
import { LocaleCache } from "./LocaleCache";
export declare class DefaultLocalizationServices implements LocalizationServices {
    private ajax;
    private config;
    private loggerServices;
    private stream;
    private get id();
    private readonly store;
    private localizedWidgets;
    private disableNetwork;
    private brand;
    private channel;
    private baseUrl;
    private timeout;
    constructor(ajax: AjaxServices, config: ConfigServices, storeServices: StoreServices, loggerServices: LoggerServices, stream: EventStreamServices);
    init(): void;
    private client;
    get locale(): LocaleValue;
    get fullLocale(): FullLocaleValue;
    get formats(): {
        [key: string]: any;
    };
    createChildLocalization(path: string, widgetStore: Store, namespace: string): Localization;
    unsubscribe(localization: Localization): void;
    loadMessages(path: string): Promise<unknown>;
    setLocale(locale: LocaleValue): void;
    get cache(): typeof LocaleCache;
    getLocalizedString(widgetName: string, stringId: string, locale?: LocaleValue): string;
    addLocaleDataMapping(config: {
        [widgetName: string]: string | string[];
    }): void;
    preloadLocaleData(localeDataConfig: {
        [widgetName: string]: string;
    }, widgetConfig?: any[]): Promise<{}>;
    destroyAll(): void;
}
