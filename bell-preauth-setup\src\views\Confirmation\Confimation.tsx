import * as React from "react";
import { <PERSON><PERSON>, <PERSON>ing, <PERSON>, I<PERSON> } from "@bell/bell-ui-library";
import { FormattedMessage, injectIntl } from "react-intl";
import { SingleRowInformation } from "../SingleRowInformation";
import { MultiBanInformation, SummaryInformationHeading } from "../SummaryInformationHeading";
import { AlertConfirmationSuccess, AlertErrorOneTimePayment } from "../Alert";
import {AlertWarningWithSomeBalancesPaidMulti} from "../Alert/AlertWarningWithSomeBalance";
import { PaymentItem, InputBankAccountDetail, SelectListItem, PaymentItemAccountTypeName, PaymentItemAccountType, AccountInputValues, CurrentSection, SubscriberOffersWithBan} from "../../models";
import { PaymentSummary } from "../PaymentSummary";
import { State } from "../../store";
import { connect } from "react-redux";
import { getItemAccountTypeName } from "../../utils";
import { AlertConfirmationInfo } from "../Alert/AlertConfirmationInfo";
import { IRequestStatus } from "../../models/App";
import Loader from "../Loader/Loader";
import { OmnitureOnConfirmation, OmnitureOnConfirmationFailure, OmnitureOnOneTimePaymentFailure } from "../../store/Actions";
import { getConfirmationOmniture, getOmnitureOnConfirmationFailure } from "../../utils/Omniture";

interface ConfirmationComponentProps {
  intl: any;
  paymentItem: PaymentItem[];
  checkedBillItems: PaymentItem[];
  checkedCurrentBalanceItems: PaymentItem[];
  showPaymentSummary: boolean;
  isPreauth: boolean;
  isNewbank: boolean;
  inputValue: any;
  inputBankValue?: InputBankAccountDetail;
  isShow: boolean; // to be cofirm with dave
  isBankPaymentSelected: boolean;
  submitMultiOrderPayment: any;
  submitMultiOrderFormStatus: IRequestStatus;
  BankList: SelectListItem[];
  showCurrentBalance: boolean;
  accountInputValues: AccountInputValues[];
  currentSection: CurrentSection;
  language: "en" | "fr";
  notOptedBalanceItems: PaymentItem[];
  setApiSatusIsFailed: (isFailed: boolean) => void;
  creditCardAutopayOffers: SubscriberOffersWithBan[];
  debitCardAutopayOffers: SubscriberOffersWithBan[];
  bankitems: any[];
  setOmnitureOnConfirmation: Function,
  apiSatusIsFailed: boolean,
  backCTAURL: string,
  setOmnitureOnConfirmationFailure: Function,
  setOmnitureOnOneTimePaymentFailure: Function
}

const ConfirmationComponent = ({ intl,
  paymentItem,
  checkedBillItems,
  checkedCurrentBalanceItems,
  showPaymentSummary,
  isPreauth,
  isNewbank,inputValue,
  inputBankValue, 
  isShow, 
  isBankPaymentSelected, 
  submitMultiOrderPayment,
  submitMultiOrderFormStatus,
  accountInputValues,
  BankList, 
  showCurrentBalance,
  currentSection, 
  language,
  notOptedBalanceItems,
  setApiSatusIsFailed,
  creditCardAutopayOffers,
  debitCardAutopayOffers,
  bankitems,
  setOmnitureOnConfirmation,
  apiSatusIsFailed,
  backCTAURL,
  setOmnitureOnConfirmationFailure,
  setOmnitureOnOneTimePaymentFailure
}: ConfirmationComponentProps) => {
  const accountTypename : PaymentItemAccountTypeName = {
    MyBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MYBILL"}),
    Mobility: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY"}),
    OneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_ONEBILL"}),
    TV: intl.formatMessage({id: "ACCOUNT_TYPENAME_TV"}),
    Internet: intl.formatMessage({id: "ACCOUNT_TYPENAME_INTERNET"}),
    HomePhone: intl.formatMessage({id: "ACCOUNT_TYPENAME_HOMEPHONE"}),
    MobilityAndOneBill: intl.formatMessage({id: "ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),
    SingleBan: intl.formatMessage({id: "ACCOUNT_TYPENAME_SINGLEBAN"})
  };
  const checkeddebitOffers = () => {
    const filteredOffer: any = [];
    debitCardAutopayOffers && debitCardAutopayOffers?.map((item) => {
      checkedBillItems && checkedBillItems.map((billItem) => {
        if (item.Ban === billItem.Ban) {
          filteredOffer.push(item);
        }
      });
    });

    return filteredOffer;
  };

  const checkedcreditOffers = () => {
    const filteredOffer: any = [];
    creditCardAutopayOffers && creditCardAutopayOffers?.map((item) => {
      checkedBillItems && checkedBillItems.map((billItem) => {
        if (item.Ban === billItem.Ban) {
          filteredOffer.push(item);
        }
      });
    });

    return filteredOffer;
  };
  const autopayOffers =
    {
      label: intl.formatMessage({ id: "PAYMENT_METHOD" }),
      debits: isBankPaymentSelected ? paymentItem && paymentItem.length > 1 ? checkeddebitOffers() : debitCardAutopayOffers : null,
      credits: !isBankPaymentSelected ? paymentItem && paymentItem.length > 1 ? checkedcreditOffers() : creditCardAutopayOffers : null,
    };
  const note =      
    {
      
      label: intl.formatMessage({id: "AUTOPAY_TRANSACTION_NOTE_DESC"})         
      
    };
  const note1 =      
    {
      
      label: intl.formatMessage({id: "AUTOPAY_TRANSACTION_NOTE_HEADING"})         
      
    };

  const isshowAutopaysuccess = 
    {
      show: autopayOffers && autopayOffers.credits && autopayOffers.credits.length > 0 && autopayOffers.credits[0].AutopayEligibleSubscribers &&  autopayOffers.credits[0].AutopayEligibleSubscribers.length > 0 && autopayOffers.credits[0].AutopayEligibleSubscribers[0].autopayOffers && autopayOffers.credits[0].AutopayEligibleSubscribers[0].autopayOffers.length >0 ||
                                  autopayOffers && autopayOffers.debits &&  autopayOffers.debits.length > 0 && autopayOffers.debits[0].AutopayEligibleSubscribers &&  autopayOffers.debits[0].AutopayEligibleSubscribers.length >0 && autopayOffers.debits[0].AutopayEligibleSubscribers[0].autopayOffers && autopayOffers.debits[0].AutopayEligibleSubscribers[0].autopayOffers.length > 0
    };
  const PaymentInformationItems = checkedBillItems.map((item) => ({ 
    label: <FormattedMessage 
      id="SELECT_BILLS_ACCOUNT_TITLE"
      values={{
        accounttype: getItemAccountTypeName(item.AccountType,item.IsNM1Account,accountTypename)
      }} />,
    value: item.NickName,
    key: getItemAccountTypeName(item.AccountType,item.IsNM1Account,accountTypename),
  })
  );
  const notPaidCurrentBalance: PaymentItem[] = checkedBillItems.filter((item) => !checkedCurrentBalanceItems.includes(item) && item.Due > 0);
   


  const containerRef = React.useRef<HTMLDivElement | null>(null);


  const handleBackClick = () => {
    window.location.href = backCTAURL + "#billinginformation" ;
  };

  const PAY_CURRENT_BALANCE_OPTED_IN = (isBankPaymentSelected ? intl.formatMessage(
    { id: "PAY_CURRENT_BALANCE_OPTED_IN" },
    { balance: new Intl.NumberFormat(language, { style: "currency", currency: "USD", currencyDisplay: "narrowSymbol", }).format(0.00) }) 
    :
    (currentSection === CurrentSection.Confirmation ? "" :intl.formatMessage(
      { id: "PAY_CURRENT_BALANCE_OPTED_IN_PACC" })
    )
  );
  const PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR = intl.formatMessage({ id: "PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_SINGULAR" });
  const PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL = intl.formatMessage({ id: "PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_PLURAL" });
    
  const currentBalancesisOpted  = () => {
    if (checkedCurrentBalanceItems.length === 0) {
      if (checkedBillItems.length - checkedCurrentBalanceItems.length > 1){ // none opted
        return PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL;
      } else {
        return PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR;
      }
    }
    else if(checkedCurrentBalanceItems.every(item => checkedBillItems.includes(item))){ // all opted in
      return PAY_CURRENT_BALANCE_OPTED_IN;
    } else if (checkedCurrentBalanceItems.some(item => checkedBillItems.includes(item))) { // some opted in
      return PAY_CURRENT_BALANCE_OPTED_IN; // update erd for some opted in
    }
  };

  const [showAlertErrorOneTimePayment, setShowAlertErrorOneTimePayment] = React.useState(false);
    
  const getOmnitureOnConfirmFailure = () => {
    if(currentSection === CurrentSection.Confirmation)
    {
      if(isBankPaymentSelected)
      {
        setOmnitureOnConfirmationFailure(getOmnitureOnConfirmationFailure(true ,showCurrentBalance ,checkedCurrentBalanceItems));
      }
      else{
        setOmnitureOnConfirmationFailure(getOmnitureOnConfirmationFailure(false ,showCurrentBalance ,checkedCurrentBalanceItems ,inputValue.cardType));
      }
    }
  };

  React.useEffect(() => {
    if (submitMultiOrderFormStatus === IRequestStatus.FAILED){
      setApiSatusIsFailed(true);
      getOmnitureOnConfirmFailure();
    } else if (paymentItem.length === 1){
      if(submitMultiOrderFormStatus === IRequestStatus.COMPLETED && Object.values(submitMultiOrderPayment).length > 0){
        for (const item of Object.values(submitMultiOrderPayment) as any) {
          if (item.ErrorCodeID.trim() !== "") {
            setApiSatusIsFailed(true);
            getOmnitureOnConfirmFailure();
            break; 
          }
        }
      }
    }
    if(submitMultiOrderFormStatus === IRequestStatus.COMPLETED && Object.values(submitMultiOrderPayment).length > 0){
      if(isShow && !apiSatusIsFailed){
        if(isBankPaymentSelected){
          setOmnitureOnConfirmation(getConfirmationOmniture(isBankPaymentSelected,showCurrentBalance,checkedCurrentBalanceItems,paymentItem,language, Object.values(submitMultiOrderPayment)));
        }
        else{
          setOmnitureOnConfirmation(getConfirmationOmniture(isBankPaymentSelected,showCurrentBalance,checkedCurrentBalanceItems,paymentItem, language,Object.values(submitMultiOrderPayment), inputValue.cardType));
        }
      }
    }
    
        
    if (submitMultiOrderFormStatus === IRequestStatus.COMPLETED && containerRef.current){
      // For reading heading and title in confirmation in pageload only accessibility
      const firstFocusableElement = containerRef.current.querySelector('.payment-focus-sr');
      if (firstFocusableElement) {
        firstFocusableElement.scrollIntoView({ behavior: 'smooth' });
        (firstFocusableElement as HTMLElement).focus();
      }

      setTimeout(()=>{
        const headingFocusableElement = containerRef?.current?.querySelector('.payment-focus-heading');
        if (headingFocusableElement) {
          headingFocusableElement.scrollIntoView({ behavior: 'smooth' });
          (headingFocusableElement as HTMLElement).focus();
        }           
      },100);

      setTimeout(()=>{
        const firstFocusableElement = containerRef?.current?.querySelector('.payment-focus-sr') as HTMLElement;
        if (firstFocusableElement) {
          firstFocusableElement.style.display = 'none';
        }
      },1000);

      setTimeout(()=>{
        setShowAlertErrorOneTimePayment(true);
      },200);
    }
  }, [submitMultiOrderFormStatus]); 
    
  const confirmationHeadingTitle = () => {
    if (Object.values(submitMultiOrderPayment).some((item:any) => item?.otp && !item?.otp.isSuccess)){
      return   intl.formatMessage({ id: "TRANSACTION_SUBMITTED_HEADING" });
    }else{
      return   intl.formatMessage({ id: "CONFIRMATION_HEADING" });
    }   
  };
  const confirmationPayAmountLabel = () => {
    if (Object.values(submitMultiOrderPayment).some((item:any) => item?.otp && !item?.otp.isSuccess)){
      return   intl.formatMessage({ id: "UNPAID_BALANCE" });
    }else{
      return   intl.formatMessage({ id: "PAYMENT_AMOUNT" });
    }   
  };
    
  const [hasSomeOtpErrors, setSomeOtpErrors] = React.useState(false);
  const [failedOtpItems, setFailedOtpItems] = React.useState([]);
  React.useEffect(() => {
    if (Object.values(failedOtpItems).length === 0) {
      const OrderItemsFailed = Object.values(submitMultiOrderPayment).filter((item:any) => item?.otp && !item?.otp.isSuccess);
      const failedOrderFormIdList = OrderItemsFailed.map((item:any) => item?.OrderFormId);
      const failedOrderItems = accountInputValues
        .filter((x) => failedOrderFormIdList.includes(x.transactionID))
        .map((x) => x.accountNumber);
      const failedOtpItems:any = checkedCurrentBalanceItems.filter((item) => failedOrderItems.includes(item.Ban));

      if (checkedCurrentBalanceItems.length > failedOtpItems.length && checkedCurrentBalanceItems.length !== 1) {
        setSomeOtpErrors(true);

      } else {
        setSomeOtpErrors(false);
      }

      setFailedOtpItems(failedOtpItems);
    }
  },[failedOtpItems]);

  return (
    <>
      {submitMultiOrderFormStatus === IRequestStatus.PENDING &&
                <Loader variant="submitOrder"/>
      }

      {(Object.values(submitMultiOrderPayment).length > 0 && submitMultiOrderFormStatus === IRequestStatus.COMPLETED) &&
        
            <div className="brui-border-gray-4" ref={containerRef}>
              <div className="payment-mb-15 brui-flex brui-items-center brui-justify-between payment-mt-30 sm:payment-mt-45" id="ConfirmationDivID">
                {/* Additional tag for SR only reading*/}
                <span className="payment-focus-sr payment-sr-only" id="pageConfirmationId"  tabIndex={-1}>{intl.formatMessage({id: "PAGE_TITLE_CONFIRMATON"})}</span>
                <Heading 
                  className="payment-focus-heading" 
                  level="h2" 
                  variant="lg" 
                  tabIndex={-1}
                  id={isShow ? "payment-setup-heading" : undefined}
                  aria-hidden={isShow ? "true" : undefined}>
                  {confirmationHeadingTitle()}
                </Heading>
              </div>

              {(!isBankPaymentSelected && Object.values(submitMultiOrderPayment).length > 0 && Object.values(submitMultiOrderPayment).some((item:any) => item?.otp && !item?.otp.isSuccess) && showAlertErrorOneTimePayment) && (
                <div id= "OtpFailure" className="payment-pt-15 payment-pb-0">
                  <AlertErrorOneTimePayment 
                    checkedCurrentBalanceItems={checkedCurrentBalanceItems}
                    submitMultiOrderPayment={Object.values(submitMultiOrderPayment)} 
                    accountInputValue={accountInputValues} 
                    language={language}
                    notOptedBalanceItems={notOptedBalanceItems} 
                    setOmnitureOnOneTimePaymentFailure={setOmnitureOnOneTimePaymentFailure}
                  />
                </div>
              )}

              <div id="ConfirmationSuccess" className="payment-pt-15 payment-pb-15">
                <AlertConfirmationSuccess 
                  submitMultiOrderPayment={Object.values(submitMultiOrderPayment)} 
                  accountInputValue={accountInputValues} 
                  isBankPayment={isBankPaymentSelected}
                  checkedBillItems={checkedBillItems}
                  language={language}
                  paymentItem = {paymentItem}
                  creditCardAutopayOffers = {creditCardAutopayOffers}
                  debitCardAutopayOffers={debitCardAutopayOffers}/>
              </div>
                
                
            
              {(paymentItem.find(item => item.AccountType !== PaymentItemAccountType.OneBill) && notPaidCurrentBalance.length > 0 && Object.values(submitMultiOrderPayment).some((item:any) => item?.otp && !item?.otp.isSuccess) === false)  && (
                <div id = "AvoidLatePayment" className="payment-pb-15">
                  <AlertWarningWithSomeBalancesPaidMulti
                    multiban={paymentItem.length > 1 ? true : false}
                    submitMultiOrderPayment={Object.values(submitMultiOrderPayment)}
                    accountInputValue={accountInputValues} 
                    paymentItem={paymentItem}
                    language={language}
                  />
                </div>
              )}

                 
              {paymentItem.find(item => item.AccountType === PaymentItemAccountType.OneBill) && (
                <div  id="AvoidLatePayment" className="payment-pb-15">
                  <AlertConfirmationInfo
                  />
                </div>
                
              )}
              <div className="payment-block payment-border payment-rounded-20 payment-relative payment-px-15 sm:payment-px-30 payment-py-30 sm:payment-pb-45">
                <div>
                  <Heading level="h3" variant="md">
                    {intl.formatMessage({ id: "PAYMENT_SUMMARY_TITLE" })}
                  </Heading>
                </div>
                <div className="payment-mt-30">
                  <SummaryInformationHeading
                    title={intl.formatMessage({ id: "BILL_INFORMATION_TITLE" })}
                  >
                    {PaymentInformationItems.map((item, index) => (
                      <SingleRowInformation
                        className={index > 0 ? "payment-mt-5" : ""}
                        label={item.label}
                        value={item.value}
                      />
                    ))}
                  </SummaryInformationHeading>
                </div>
                <div className="payment-mt-45">
                  <SummaryInformationHeading title={intl.formatMessage({ id: "PAYMENT_INFORMATION_TITLE" })}>
                    <PaymentSummary paymentItem={paymentItem} className={showPaymentSummary ? "" : "payment-hidden" } inputValue={inputValue} isNewbank={isNewbank} isPreauth={isPreauth} inputBankValue={inputBankValue} showHeading={false} isBankPaymentSelected={isBankPaymentSelected} bankList={BankList}creditCardAutopayOffers={creditCardAutopayOffers}
                      debitCardAutopayOffers={debitCardAutopayOffers}
                      checkedBillItems={checkedBillItems}bankitems={bankitems}isConfirmation={true}></PaymentSummary>
                    {/* {PaymentInformationItems.map((item, index) => (
                                <SingleRowInformation
                                    className={index > 0 ? "payment-mt-5" : ""}
                                    label={item.label}
                                    value={item.value}
                                    role="listitem"
                                />
                            ))} */}
                  </SummaryInformationHeading>
                </div>
                {isshowAutopaysuccess.show ?
                  isBankPaymentSelected ? 
                    <div className="payment-mt-45">
                      <SummaryInformationHeading title={intl.formatMessage({ id: "AUTOPAY_CREDITS_TITLE" })} role="list">
                        {autopayOffers?.debits?.map((debit: any, debitIdx: number) => (
                          <React.Fragment key={debit.Ban || debitIdx}>
                            {checkedBillItems && checkedBillItems.length > 1 && (
                              <p className="payment-text-14 payment-font-bold">
                                {debit.banInfo && debit.banInfo.nickName}
                              </p>
                            )}
                            {debit.AutopayEligibleSubscribers?.map((item: any, itemIdx: number) =>
                              item?.autopayOffers?.map((offer: any, offerIdx: number) => (
                                <SingleRowInformation
                                  key={`${item.subscriberTelephoneNumber}-${offerIdx}`}
                                  label={item.subscriberTelephoneNumber}
                                  value={
                                    language === "en"
                                      ? "$" + offer.discountAmount.toFixed(2) + "/mo."
                                      : offer.discountAmount.toFixed(2) + " $/mois"
                                  }
                                  needSRText
                                  srText={
                                    language === "en"
                                      ? offer.discountAmount.toFixed(2) + " dollars per month"
                                      : offer.discountAmount.toFixed(2) + " dollars par mois"
                                  }
                                  role="listitem"
                                  isMultiBan={checkedBillItems.length > 1}
                                  className={checkedBillItems.length > 1 ? "payment-text-gray" : "payment-text-black"}
                                />
                              ))
                            )}
                          </React.Fragment>
                        ))}
                        <p className="payment-text-gray payment-text-14">
                          <strong>{note1.label}</strong>
                          {note.label}
                        </p>
                      </SummaryInformationHeading>
                    </div>
                    :<div className="payment-mt-45">
                      <SummaryInformationHeading title={intl.formatMessage({ id: "AUTOPAY_CREDITS_TITLE" })} role="list">
                        {autopayOffers?.credits?.map((credit: any, creditIdx: number) => (
                          <React.Fragment key={credit.Ban || creditIdx}>
                            {checkedBillItems && checkedBillItems.length > 1 && (
                              <p className="payment-text-14 payment-font-bold">
                                {credit.banInfo && credit.banInfo.nickName}
                              </p>
                            )}
                            {credit.AutopayEligibleSubscribers?.map((item: any, itemIdx: number) =>
                              item?.autopayOffers?.map((offer: any, offerIdx: number) => (
                                <SingleRowInformation
                                  key={`${item.subscriberTelephoneNumber}-${offerIdx}`}
                                  label={item.subscriberTelephoneNumber}
                                  value={
                                    language === "en"
                                      ? "$" + offer.discountAmount.toFixed(2) + "/mo."
                                      : offer.discountAmount.toFixed(2) + " $/mois"
                                  }
                                  needSRText
                                  srText={
                                    language === "en"
                                      ? offer.discountAmount.toFixed(2) + " dollars per month"
                                      : offer.discountAmount.toFixed(2) + " dollars par mois"
                                  }
                                  role="listitem"
                                  isMultiBan={checkedBillItems.length > 1}
                                  className={checkedBillItems.length > 1 ? "payment-text-gray" : "payment-text-black"}
                                />
                              ))
                            )}
                          </React.Fragment>
                        ))}
                        <p className="payment-text-gray payment-text-14">
                          <strong>{note1.label}</strong>
                          {note.label}
                        </p>
                      </SummaryInformationHeading>
                    </div>
                  : ""}
                {showCurrentBalance &&
                        <div className="payment-mt-45">
                          <SummaryInformationHeading title={intl.formatMessage({ id: "CURRENT_BALANCE_TITLE" })}>
                                
                            {(!isBankPaymentSelected && Object.values(submitMultiOrderPayment).length > 0 && Object.values(submitMultiOrderPayment).some((item:any) =>  item?.otp && !item?.otp.isSuccess) && !hasSomeOtpErrors)  && (
                              <div className="payment-flex payment-items-start payment-mb-15">
                                <Icon className="brui-text-15 brui-text-red brui-mr-10 payment-mt-2" iconClass="bi_brui" iconName="bi_error_bl_bg_cf"></Icon>
                                <p className="brui-text-red brui-text-14">{intl.formatMessage({ id: "SUMMARY_CURRENT_BAL_OTP_FAILURE" })}</p>
                              </div>
                            )}
                            <div className="payment-text-gray payment-text-14 payment-mt-5">
                              <p className="payment-leading-18" dangerouslySetInnerHTML={{ __html: currentBalancesisOpted() }}/>
                            </div>
                            <div>
                              {paymentItem.length > 1 ?
                                <>
                                  {checkedCurrentBalanceItems.map((item) => (
                                    <MultiBanInformation accountinfo={item.NickName} role="list" childrole="listitem" 
                                      className="first:payment-mt-15 payment-mb-15 last:payment-mb-0"
                                      isLabelOnError={(hasSomeOtpErrors && failedOtpItems.filter((x:any) => x.Ban === item.Ban).length > 0) ? true : false}
                                    >
                                      <>
                                        {(hasSomeOtpErrors && failedOtpItems.filter((x:any) => x.Ban === item.Ban).length > 0) && (
                                          <div className="payment-flex payment-items-start payment-mb-5">
                                            <Icon className="brui-text-15 brui-text-red brui-mr-10 payment-mt-2" iconClass="bi_brui" iconName="bi_error_bl_bg_cf"></Icon>
                                            <p className="brui-text-red brui-text-14">{intl.formatMessage({ id: "SUMMARY_CURRENT_BAL_OTP_FAILURE" })}</p>
                                          </div>
                                        )}
                                        <SingleRowInformation
                                          label={failedOtpItems.filter((x:any) => x.Ban === item.Ban).length > 0
                                            ? intl.formatMessage({ id: "UNPAID_BALANCE" })
                                            : intl.formatMessage({ id: "PAYMENT_AMOUNT" })
                                          }
                                          value={
                                            <Price
                                              language={language}
                                              price={item.Due}
                                              variant="ordinaryPrice"
                                              className="!brui-text-14 brui-leading-18"/>
                                          }
                                          needSRText
                                          srText={`${item.Due} dollars`}
                                          className="payment-text-black" 
                                          isMultiBan={true}/>
                                      </>
                                    </MultiBanInformation>
                                  ))} 
                                </> :
                                <>
                                  {checkedCurrentBalanceItems.map((item) => (
                                    <SingleRowInformation
                                      label={confirmationPayAmountLabel()}
                                      value={
                                        <Price
                                          language={language}
                                          price={item.Due}
                                          variant="ordinaryPrice"
                                          className="!brui-text-14 brui-leading-18"/>
                                      }
                                      needSRText
                                      srText={`${item.Due} dollars`}
                                      className="payment-text-black first:payment-mt-15 payment-mb-15 last:payment-mb-0"
                                    />
                                  ))} 
                                </>
                              }
                            </div>
                          </SummaryInformationHeading>
                        </div>
                }
              </div>
              <div className="payment-mt-30 payment-mb-45 sm:payment-mb-60">
                <Button size="regular" variant="secondary" onClick={handleBackClick} >
                  {intl.formatMessage({ id: "BACK_TO_MY_BELL" })}
                </Button>
              </div>
            </div>
      }
    </>
  );
};

const mapStateToProps = (state: State) => ({
  submitMultiOrderPayment: state.submitMultiOrderPayment,
  submitMultiOrderFormStatus: state.submitMultiOrderFormStatus,
});
const mapDispatchToProps = (dispatch: React.Dispatch<any>) => ({ 
  setOmnitureOnConfirmation: (data?: any) => dispatch(OmnitureOnConfirmation({data})),
  setOmnitureOnConfirmationFailure: (data? : any) => dispatch(OmnitureOnConfirmationFailure({data})),
  setOmnitureOnOneTimePaymentFailure: (data? : any) => dispatch(OmnitureOnOneTimePaymentFailure({data})),
});
 

export const Confirmation = connect(mapStateToProps, mapDispatchToProps)(injectIntl(ConfirmationComponent));
