import * as React from "react";
import { createRoot } from "react-dom/client";
import { WidgetLoader, Init } from "bwtk";


export function initialize(config: any, root: string, debug: any) {
  const updatedConfig = {
    ...config, "loader.staticWidgetMappings": {
      "bell-preauth-setup": {
        factory: () => require("bell-preauth-setup"),
        namespace: "Preauth/Setup"
      }
    }
  };
  Init(updatedConfig);

  const container = document.getElementById(root);
  if (container) {
    const root = createRoot(container);
    root.render(<WidgetLoader widget="bell-preauth-setup" />);
  }
}
