import * as React from "react";
import { Heading } from "@bell/bell-ui-library";

export interface SummaryInformationHeadingProps {
  title: string,
  role?: string,
  children: React.ReactNode
}

export const SummaryInformationHeading = ({ title, role, children }: SummaryInformationHeadingProps) => (
  <>
    <div className="payment-border-b-1 payment-border-b-lightgray payment-pb-15 payment-mb-15">
      <Heading level="h4" variant="default" className="payment-block payment-font-sans payment-text-black payment-text-18 payment-leading-22">
        {title}
      </Heading>
    </div>
    <div role={role}>
      {children}
    </div>
  </>
);

export default SummaryInformationHeading;
