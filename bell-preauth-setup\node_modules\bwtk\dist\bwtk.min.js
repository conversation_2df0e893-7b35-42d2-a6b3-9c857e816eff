/*! For license information please see bwtk.min.js.LICENSE.txt */
var e,t;e=self,t=(e,t,r,n,i,o,a,s)=>(()=>{var c={873:e=>{e.exports=function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={exports:{},id:n,loaded:!1};return e[n].call(i.exports,i,i.exports,r),i.loaded=!0,i.exports}return r.m=e,r.c=t,r.p="",r(0)}([function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(1);Object.defineProperty(t,"Injector",{enumerable:!0,get:function(){return n.Injector}});var i=r(2);Object.defineProperty(t,"annotate",{enumerable:!0,get:function(){return i.annotate}}),Object.defineProperty(t,"Inject",{enumerable:!0,get:function(){return i.Inject}}),Object.defineProperty(t,"InjectLazy",{enumerable:!0,get:function(){return i.InjectLazy}}),Object.defineProperty(t,"InjectPromise",{enumerable:!0,get:function(){return i.InjectPromise}}),Object.defineProperty(t,"Provide",{enumerable:!0,get:function(){return i.Provide}}),Object.defineProperty(t,"ProvidePromise",{enumerable:!0,get:function(){return i.ProvidePromise}}),Object.defineProperty(t,"SuperConstructor",{enumerable:!0,get:function(){return i.SuperConstructor}}),Object.defineProperty(t,"TransientScope",{enumerable:!0,get:function(){return i.TransientScope}}),Object.defineProperty(t,"ClassProvider",{enumerable:!0,get:function(){return i.ClassProvider}}),Object.defineProperty(t,"FactoryProvider",{enumerable:!0,get:function(){return i.FactoryProvider}})},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Injector=void 0;var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),i=r(2),o=r(3),a=r(4),s=r(6);function c(e,t){return arguments.length>1&&e.push(t),e.length>1?" ("+e.map(o.toString).join(" -> ")+")":""}var u=function(){function e(){var t=arguments.length<=0||void 0===arguments[0]?[]:arguments[0],r=arguments.length<=1||void 0===arguments[1]?null:arguments[1],n=arguments.length<=2||void 0===arguments[2]?new Map:arguments[2],i=arguments.length<=3||void 0===arguments[3]?[]:arguments[3];!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._cache=new Map,this._providers=n,this._parent=r,this._scopes=i,this._loadModules(t),(0,a.profileInjector)(this,e)}return n(e,[{key:"_collectProvidersWithAnnotation",value:function(e,t){this._providers.forEach((function(r,n){!t.has(n)&&(0,i.hasAnnotation)(r.provider,e)&&t.set(n,r)})),this._parent&&this._parent._collectProvidersWithAnnotation(e,t)}},{key:"_loadModules",value:function(e){var t=!0,r=!1,n=void 0;try{for(var i,a=e[Symbol.iterator]();!(t=(i=a.next()).done);t=!0){var s=i.value;if(!(0,o.isFunction)(s))throw new Error("Invalid module!");this._loadFnOrClass(s)}}catch(e){r=!0,n=e}finally{try{!t&&a.return&&a.return()}finally{if(r)throw n}}}},{key:"_loadFnOrClass",value:function(e){var t=(0,i.readAnnotations)(e),r=t.provide.token||e,n=(0,s.createProviderFromFnOrClass)(e,t);this._providers.set(r,n)}},{key:"_hasProviderFor",value:function(e){return!!this._providers.has(e)||!!this._parent&&this._parent._hasProviderFor(e)}},{key:"_instantiateDefaultProvider",value:function(e,t,r,n,o){if(!this._parent)return this._providers.set(t,e),this.get(t,r,n,o);var a=!0,s=!1,c=void 0;try{for(var u,f=this._scopes[Symbol.iterator]();!(a=(u=f.next()).done);a=!0){var l=u.value;if((0,i.hasAnnotation)(e.provider,l))return this._providers.set(t,e),this.get(t,r,n,o)}}catch(e){s=!0,c=e}finally{try{!a&&f.return&&f.return()}finally{if(s)throw c}}return this._parent._instantiateDefaultProvider(e,t,r,n,o)}},{key:"get",value:function(t){var r,n,a=arguments.length<=1||void 0===arguments[1]?[]:arguments[1],u=this,f=!(arguments.length<=2||void 0===arguments[2])&&arguments[2],l=!(arguments.length<=3||void 0===arguments[3])&&arguments[3],d="",p=this;if(null==t)throw d=c(a,t),new Error('Invalid token "'+t+'" requested!'+d);if(t===e)return f?Promise.resolve(this):this;if(l)return function(){var e=p;if(arguments.length){for(var r=[],n=arguments,o=0;o<n.length;o+=2)r.push(function(e){var t=function(){return n[e+1]};return(0,i.annotate)(t,new i.Provide(n[e])),t}(o));e=p.createChild(r)}return e.get(t,a,f,!1)};if(this._cache.has(t)){if(n=this._cache.get(t),(r=this._providers.get(t)).isPromise&&!f)throw d=c(a,t),new Error("Cannot instantiate "+(0,o.toString)(t)+" synchronously. It is provided as a promise!"+d);return!r.isPromise&&f?Promise.resolve(n):n}if(!(r=this._providers.get(t))&&(0,o.isFunction)(t)&&!this._hasProviderFor(t))return r=(0,s.createProviderFromFnOrClass)(t,(0,i.readAnnotations)(t)),this._instantiateDefaultProvider(r,t,a,f,l);if(!r){if(!this._parent)throw d=c(a,t),new Error("No provider for "+(0,o.toString)(t)+"!"+d);return this._parent.get(t,a,f,l)}if(-1!==a.indexOf(t))throw d=c(a,t),new Error("Cannot instantiate cyclic dependency!"+d);a.push(t);var h=f&&r.params.some((function(e){return!e.isPromise})),y=r.params.map((function(e){return h?u.get(e.token,a,!0,e.isLazy):u.get(e.token,a,e.isPromise,e.isLazy)}));if(h){var g=a.slice();return a.pop(),Promise.all(y).then((function(e){try{n=r.create(e)}catch(e){d=c(g);var a="ORIGINAL ERROR: "+e.message;throw e.message="Error during instantiation of "+(0,o.toString)(t)+"!"+d+"\n"+a,e}return(0,i.hasAnnotation)(r.provider,i.TransientScope)||p._cache.set(t,n),n}))}try{n=r.create(y)}catch(e){d=c(a);var v="ORIGINAL ERROR: "+e.message;throw e.message="Error during instantiation of "+(0,o.toString)(t)+"!"+d+"\n"+v,e}if((0,i.hasAnnotation)(r.provider,i.TransientScope)||this._cache.set(t,n),!f&&r.isPromise)throw d=c(a),new Error("Cannot instantiate "+(0,o.toString)(t)+" synchronously. It is provided as a promise!"+d);return f&&!r.isPromise&&(n=Promise.resolve(n)),a.pop(),n}},{key:"getPromise",value:function(e){return this.get(e,[],!0)}},{key:"createChild",value:function(){var t=arguments.length<=0||void 0===arguments[0]?[]:arguments[0],r=arguments.length<=1||void 0===arguments[1]?[]:arguments[1],n=new Map;r.push(i.TransientScope);var o=!0,a=!1,s=void 0;try{for(var c,u=r[Symbol.iterator]();!(o=(c=u.next()).done);o=!0){var f=c.value;this._collectProvidersWithAnnotation(f,n)}}catch(e){a=!0,s=e}finally{try{!o&&u.return&&u.return()}finally{if(a)throw s}}return new e(t,this,n,r)}}]),e}();t.Injector=u},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.FactoryProvider=t.ClassProvider=t.ProvidePromise=t.Provide=t.InjectLazy=t.InjectPromise=t.Inject=t.TransientScope=t.SuperConstructor=t.readAnnotations=t.hasAnnotation=t.annotate=void 0;var n=r(3);function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function o(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var s=function e(){a(this,e);for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];this.tokens=r,this.isPromise=!1,this.isLazy=!1},c=function(e){function t(){a(this,t);for(var e=i(this,Object.getPrototypeOf(t).call(this)),r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e.tokens=n,e.isPromise=!0,e.isLazy=!1,e}return o(t,e),t}(s),u=function(e){function t(){a(this,t);for(var e=i(this,Object.getPrototypeOf(t).call(this)),r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return e.tokens=n,e.isPromise=!1,e.isLazy=!0,e}return o(t,e),t}(s),f=function e(t){a(this,e),this.token=t,this.isPromise=!1},l=function(e){function t(e){a(this,t);var r=i(this,Object.getPrototypeOf(t).call(this));return r.token=e,r.isPromise=!0,r}return o(t,e),t}(f);t.annotate=function(e,t){e.annotations=e.annotations||[],e.annotations.push(t)},t.hasAnnotation=function(e,t){if(!e.annotations||0===e.annotations.length)return!1;var r=!0,n=!1,i=void 0;try{for(var o,a=e.annotations[Symbol.iterator]();!(r=(o=a.next()).done);r=!0)if(o.value instanceof t)return!0}catch(e){n=!0,i=e}finally{try{!r&&a.return&&a.return()}finally{if(n)throw i}}return!1},t.readAnnotations=function(e){var t={provide:{token:null,isPromise:!1},params:[]};if(e.annotations&&e.annotations.length){var r=!0,i=!1,o=void 0;try{for(var a,c=e.annotations[Symbol.iterator]();!(r=(a=c.next()).done);r=!0){var u=a.value;u instanceof s&&u.tokens.forEach((function(e){t.params.push({token:e,isPromise:u.isPromise,isLazy:u.isLazy})})),u instanceof f&&(t.provide.token=u.token,t.provide.isPromise=u.isPromise)}}catch(e){i=!0,o=e}finally{try{!r&&c.return&&c.return()}finally{if(i)throw o}}}return e.parameters&&e.parameters.forEach((function(e,r){var i=!0,o=!1,a=void 0;try{for(var c,u=e[Symbol.iterator]();!(i=(c=u.next()).done);i=!0){var f=c.value;(0,n.isFunction)(f)&&!t.params[r]?t.params[r]={token:f,isPromise:!1,isLazy:!1}:f instanceof s&&(t.params[r]={token:f.tokens[0],isPromise:f.isPromise,isLazy:f.isLazy})}}catch(e){o=!0,a=e}finally{try{!i&&u.return&&u.return()}finally{if(o)throw a}}})),t},t.SuperConstructor=function e(){a(this,e)},t.TransientScope=function e(){a(this,e)},t.Inject=s,t.InjectPromise=c,t.InjectLazy=u,t.Provide=f,t.ProvidePromise=l,t.ClassProvider=function e(){a(this,e)},t.FactoryProvider=function e(){a(this,e)}},function(e,t){(function(e){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e},n=e.Reflect&&Reflect.ownKeys?Reflect.ownKeys:function(e){var t=Object.getOwnPropertyNames(e);return Object.getOwnPropertySymbols?t.concat(Object.getOwnPropertySymbols(e)):t};t.isUpperCase=function(e){return e.toUpperCase()===e},t.isFunction=function(e){return"function"==typeof e},t.isObject=function(e){return"object"===(void 0===e?"undefined":r(e))},t.toString=function(e){return"string"==typeof e?e:null==e?""+e:e.name?e.name:e.toString()},t.ownKeys=n}).call(t,function(){return this}())},function(e,t,r){(function(e,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e};t.profileInjector=function(e,t){a&&(s.__di_dump__||(s.__di_dump__={injectors:[],tokens:new Map}),s.__di_dump__.injectors.push(function(e,t,r){var n={id:u(e,t),parent_id:e._parent?u(e._parent,t):null,providers:{}},i=u(r,t);return n.providers[i]={id:i,name:(0,o.toString)(r),isPromise:!1,dependencies:[]},e._providers.forEach((function(e,r){var i=function(e,t,r){return{id:u(t,r),name:(0,o.toString)(t),isPromise:e.isPromise,dependencies:e.params.map((function(e){return{token:u(e.token,r),isPromise:e.isPromise,isLazy:e.isLazy}}))}}(e,r,t);n.providers[i.id]=i})),n}(e,s.__di_dump__.tokens,t)))};var o=r(3),a=!1,s=null;"object"===(void 0===e?"undefined":i(e))&&e.env?(a=!!e.env.DEBUG,s=n):"object"===("undefined"==typeof location?"undefined":i(location))&&location.search&&(a=/di_debug/.test(location.search),s=window);var c=0;function u(e,t){return t.has(e)||t.set(e,(++c).toString()),t.get(e)}}).call(t,r(5),function(){return this}())},function(e,t){var r,n=e.exports={},i=[],o=!1,a=-1;function s(){o=!1,r.length?i=r.concat(i):a=-1,i.length&&c()}function c(){if(!o){var e=setTimeout(s);o=!0;for(var t=i.length;t;){for(r=i,i=[];++a<t;)r&&r[a].run();a=-1,t=i.length}r=null,o=!1,clearTimeout(e)}}function u(e,t){this.fun=e,this.array=t}function f(){}n.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];i.push(new u(e,t)),1!==i.length||o||setTimeout(c,0)},u.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={},n.on=f,n.addListener=f,n.once=f,n.off=f,n.removeListener=f,n.removeAllListeners=f,n.emit=f,n.binding=function(e){throw new Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(e){throw new Error("process.chdir is not supported")},n.umask=function(){return 0}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}();t.createProviderFromFnOrClass=function(e,t){return r=e,(0,i.hasAnnotation)(r,i.ClassProvider)||!(0,i.hasAnnotation)(r,i.FactoryProvider)&&(r.name?(0,o.isUpperCase)(r.name.charAt(0)):(0,o.ownKeys)(r.prototype).length>0)?new c(e,t.params,t.provide.isPromise):new u(e,t.params,t.provide.isPromise);var r};var i=r(2),o=r(3);function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var s=Object.getPrototypeOf(Function),c=function(){function e(t,r,n){a(this,e),this.provider=t,this.isPromise=n,this.params=[],this._constructors=[],this._flattenParams(t,r),this._constructors.unshift([t,0,this.params.length-1])}return n(e,[{key:"_flattenParams",value:function(e,t){var r,n,a=!0,c=!1,u=void 0;try{for(var f,l=t[Symbol.iterator]();!(a=(f=l.next()).done);a=!0){var d=f.value;if(d.token===i.SuperConstructor){if((r=Object.getPrototypeOf(e))===s)throw new Error((0,o.toString)(e)+" does not have a parent constructor. Only classes with a parent can ask for SuperConstructor!");n=[r,this.params.length],this._constructors.push(n),this._flattenParams(r,(0,i.readAnnotations)(r).params),n.push(this.params.length-1)}else this.params.push(d)}}catch(e){c=!0,u=e}finally{try{!a&&l.return&&l.return()}finally{if(c)throw u}}}},{key:"_createConstructor",value:function(e,t,r){var n,i=this._constructors[e],o=this._constructors[e+1];return n=o?r.slice(i[1],o[1]).concat([this._createConstructor(e+1,t,r)]).concat(r.slice(o[2]+1,i[2]+1)):r.slice(i[1],i[2]+1),function(){return i[0].apply(t,n)}}},{key:"create",value:function(e){var t=Object.create(this.provider.prototype),r=this._createConstructor(0,t,e)();return(0,o.isFunction)(r)||(0,o.isObject)(r)?r:t}}]),e}(),u=function(){function e(t,r,n){a(this,e),this.provider=t,this.params=r,this.isPromise=n;var s=!0,c=!1,u=void 0;try{for(var f,l=r[Symbol.iterator]();!(s=(f=l.next()).done);s=!0)if(f.value.token===i.SuperConstructor)throw new Error((0,o.toString)(t)+" is not a class. Only classes with a parent can ask for SuperConstructor!")}catch(e){c=!0,u=e}finally{try{!s&&l.return&&l.return()}finally{if(c)throw u}}}return n(e,[{key:"create",value:function(e){return this.provider.apply(void 0,e)}}]),e}()}])},406:e=>{"use strict";var t,r="object"==typeof Reflect?Reflect:null,n=r&&"function"==typeof r.apply?r.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};t=r&&"function"==typeof r.ownKeys?r.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var i=Number.isNaN||function(e){return e!=e};function o(){o.init.call(this)}e.exports=o,e.exports.once=function(e,t){return new Promise((function(r,n){function i(r){e.removeListener(t,o),n(r)}function o(){"function"==typeof e.removeListener&&e.removeListener("error",i),r([].slice.call(arguments))}y(e,t,o,{once:!0}),"error"!==t&&function(e,t,r){"function"==typeof e.on&&y(e,"error",t,r)}(e,i,{once:!0})}))},o.EventEmitter=o,o.prototype._events=void 0,o.prototype._eventsCount=0,o.prototype._maxListeners=void 0;var a=10;function s(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function c(e){return void 0===e._maxListeners?o.defaultMaxListeners:e._maxListeners}function u(e,t,r,n){var i,o,a;if(s(r),void 0===(o=e._events)?(o=e._events=Object.create(null),e._eventsCount=0):(void 0!==o.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),o=e._events),a=o[t]),void 0===a)a=o[t]=r,++e._eventsCount;else if("function"==typeof a?a=o[t]=n?[r,a]:[a,r]:n?a.unshift(r):a.push(r),(i=c(e))>0&&a.length>i&&!a.warned){a.warned=!0;var u=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");u.name="MaxListenersExceededWarning",u.emitter=e,u.type=t,u.count=a.length,console&&console.warn}return e}function f(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function l(e,t,r){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},i=f.bind(n);return i.listener=r,n.wrapFn=i,i}function d(e,t,r){var n=e._events;if(void 0===n)return[];var i=n[t];return void 0===i?[]:"function"==typeof i?r?[i.listener||i]:[i]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(i):h(i,i.length)}function p(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function h(e,t){for(var r=new Array(t),n=0;n<t;++n)r[n]=e[n];return r}function y(e,t,r,n){if("function"==typeof e.on)n.once?e.once(t,r):e.on(t,r);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function i(o){n.once&&e.removeEventListener(t,i),r(o)}))}}Object.defineProperty(o,"defaultMaxListeners",{enumerable:!0,get:function(){return a},set:function(e){if("number"!=typeof e||e<0||i(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");a=e}}),o.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},o.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||i(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},o.prototype.getMaxListeners=function(){return c(this)},o.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var i="error"===e,o=this._events;if(void 0!==o)i=i&&void 0===o.error;else if(!i)return!1;if(i){var a;if(t.length>0&&(a=t[0]),a instanceof Error)throw a;var s=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw s.context=a,s}var c=o[e];if(void 0===c)return!1;if("function"==typeof c)n(c,this,t);else{var u=c.length,f=h(c,u);for(r=0;r<u;++r)n(f[r],this,t)}return!0},o.prototype.addListener=function(e,t){return u(this,e,t,!1)},o.prototype.on=o.prototype.addListener,o.prototype.prependListener=function(e,t){return u(this,e,t,!0)},o.prototype.once=function(e,t){return s(t),this.on(e,l(this,e,t)),this},o.prototype.prependOnceListener=function(e,t){return s(t),this.prependListener(e,l(this,e,t)),this},o.prototype.removeListener=function(e,t){var r,n,i,o,a;if(s(t),void 0===(n=this._events))return this;if(void 0===(r=n[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(i=-1,o=r.length-1;o>=0;o--)if(r[o]===t||r[o].listener===t){a=r[o].listener,i=o;break}if(i<0)return this;0===i?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,i),1===r.length&&(n[e]=r[0]),void 0!==n.removeListener&&this.emit("removeListener",e,a||t)}return this},o.prototype.off=o.prototype.removeListener,o.prototype.removeAllListeners=function(e){var t,r,n;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0===arguments.length){var i,o=Object.keys(r);for(n=0;n<o.length;++n)"removeListener"!==(i=o[n])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},o.prototype.listeners=function(e){return d(this,e,!0)},o.prototype.rawListeners=function(e){return d(this,e,!1)},o.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):p.call(e,t)},o.prototype.listenerCount=p,o.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]}},489:(e,t,r)=>{"use strict";var n=r(3);t.H=n.createRoot,n.hydrateRoot},663:e=>{e.exports=function(e){switch(void 0===e&&(e=""),e.toLowerCase()){case"bwtk":return"bwtk";case"jquery":return"$";case"rxjs":return"rxjs";default:return e.split("-").map((function(e){return e.length>3?e[0].toUpperCase()+e.slice(1):e.toUpperCase()})).join("")}}},442:e=>{"use strict";e.exports=i},3:e=>{"use strict";e.exports=o},419:e=>{"use strict";e.exports=t},999:e=>{"use strict";e.exports=r},750:e=>{"use strict";e.exports=s},541:e=>{"use strict";e.exports=n},769:t=>{"use strict";t.exports=e},418:e=>{"use strict";e.exports=a}},u={};function f(e){var t=u[e];if(void 0!==t)return t.exports;var r=u[e]={exports:{}};return c[e](r,r.exports,f),r.exports}f.d=(e,t)=>{for(var r in t)f.o(t,r)&&!f.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},f.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),f.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var l={};return(()=>{"use strict";f.r(l),f.d(l,{AjaxServices:()=>R,ApplicationError:()=>ie,CallbackExecutionError:()=>C,CommonFeatures:()=>Re,CommonServices:()=>q,ConfigActions:()=>k,ConfigServices:()=>I,DestroyAll:()=>pr,DestroyWidget:()=>it,DiServices:()=>M,ErrorBoundary:()=>Ie,ErrorStreamErrors:()=>y,EventStream:()=>_,EventStreamServices:()=>j,Id:()=>se,Init:()=>fr,Injectable:()=>J,InternalError:()=>ne,LoaderConfigKeys:()=>fe,Localization:()=>x,LocalizationConfigKeys:()=>le,LocalizationServices:()=>D,Logger:()=>F,LoggerConfigKeys:()=>N,LoggerServices:()=>T,LoggerSeverityLevel:()=>E,Name:()=>ce,Namespace:()=>ue,ParamsProvider:()=>ge,ParamsServices:()=>ye,Provide:()=>Q,ReduxError:()=>ae,RenderWidget:()=>rt,ServiceLocator:()=>S,Store:()=>W,StoreServices:()=>z,ValidationError:()=>oe,ViewWidget:()=>re,Widget:()=>$,WidgetLoader:()=>he,WidgetLoaderServices:()=>A,factory:()=>U,injectWithFactory:()=>X,loadMessages:()=>O,localeChanged:()=>w,messagesLoaded:()=>P,raiseError:()=>b,setLocale:()=>m});var e=f(769),t=f(419),r=f(999),n={window:"undefined"!=typeof window?window:{}},i=function(e,t){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},i(e,t)};function o(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var a=function(){return a=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},a.apply(this,arguments)};function s(e,t,r,n){var i,o=arguments.length,a=o<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(o<3?i(a):o>3?i(t,r,a):i(t,r))||a);return o>3&&a&&Object.defineProperty(t,r,a),a}function c(e,t){return function(r,n){t(r,n,e)}}function u(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function d(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function p(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,i,o=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=o.next()).done;)a.push(n.value)}catch(e){i={error:e}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(i)throw i.error}}return a}function h(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError;var y,g=f(406),v=f(541),b=(0,v.createAction)("INTERNAL_ERROR"),m=(0,v.createAction)("SET_LOCALE"),w=(0,v.createAction)("LOCALE_CHANGED"),O=(0,v.createAction)("LOAD_MESSAGES"),P=(0,v.createAction)("MESSAGES_LOADED"),j=function(){},_=function(){};!function(e){e.GENERIC="ERROR_GENERIC",e.STREAM="ERROR_EVENT_STREAM",e.AJAX="ERROR_AJAX",e.REDUX="ERROR_REDUX",e.LOCALIZATION="ERROR_LOCALIZATION",e.REACT_BOUNDARY="ERROR_REACT_BOUNDARY"}(y||(y={}));var E,S=function(){function e(){}return Object.defineProperty(e.prototype,"di",{get:function(){return this.getService("di")},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"loader",{get:function(){return this.getService("loader")},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"store",{get:function(){return this.getService("store")},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"stream",{get:function(){return this.getService("stream")},enumerable:!1,configurable:!0}),e.prototype.getWidget=function(e,t,r){var n=this;this.createWidgetInstances(e).then((function(r){return new Promise((function(i,o){try{t.apply(null,r),i(void 0)}catch(t){n.stream.sendError(y.GENERIC,t,"string"==typeof e?e:void 0),n.store.dispatch(b(t)),o(new C(t))}}))})).catch((function(e){r&&r.apply(null,[e])}))},e.prototype.createWidgetInstances=function(e){var t="string"==typeof e?[e]:e;return Array.isArray(t)?this.doCreateWidgetInstances(t):Promise.resolve([])},e.prototype.doCreateWidgetInstances=function(e){var t=this;return this.loadWidgets(e).then((function(e){return Promise.resolve(e.map((function(e){return t.di.createInstance(e.id,e.type)})))}))},e.prototype.loadWidgets=function(e){return Promise.all(this.getLoadWidgetPromises(e))},e.prototype.getLoadWidgetPromises=function(e){var t=this;return e.map((function(e){return new Promise((function(r,n){t.loader.load(e).then((function(t){return r({id:e,type:t})})).catch(n)}))}))},Object.defineProperty(e,"instance",{get:function(){return e.instanceProvider()},enumerable:!1,configurable:!0}),e.setInstanceProvider=function(t){e.instanceProvider=t,this.emitOnReady()},e.emitOnReady=function(){this.events.emit("ready")},e.onReady=function(e){this.events.once("ready",e),this.instance&&this.emitOnReady()},Object.defineProperty(e,"instanceProvider",{get:function(){return this._instanceProvider},set:function(e){this._instanceProvider=e},enumerable:!1,configurable:!0}),e.events=new g.EventEmitter,e._instanceProvider=function(){return null},e}(),C=function(e){function t(t){var r=e.call(this)||this;return r.innerException=t,r.name="CallbackExecutionError",r.message="Error trying to execute callback function.",r}return o(t,e),t}(Error),L=function(){function e(){this._value=function(){function e(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)}return"".concat(e()).concat(e(),"-").concat(e(),"-").concat(e(),"-").concat(e(),"-").concat(e()).concat(e()).concat(e())}()}return Object.defineProperty(e.prototype,"value",{get:function(){return this._value},enumerable:!1,configurable:!0}),e}(),R=function(){},I=function(){},k=function(){function e(){}return Object.defineProperty(e,"SET_DEFAULT_CONFIG",{get:function(){return"SET_DEFAULT_CONFIG"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"SET_CONFIG",{get:function(){return"SET_CONFIG"},enumerable:!1,configurable:!0}),e}(),M=function(){},A=function(){},x=function(){},D=function(){},T=function(){},F=function(){};!function(e){e[e.None=0]="None",e[e.Debug=2]="Debug",e[e.Errors=4]="Errors",e[e.Info=8]="Info",e[e.Logs=16]="Logs",e[e.Warnings=32]="Warnings",e[e.All=62]="All"}(E||(E={}));var N=function(){function e(){}return Object.defineProperty(e,"SeverityLevel",{get:function(){return"bwtk/services/logger/severityLevel"},enumerable:!1,configurable:!0}),e}(),z=function(){function e(){}return Object.defineProperty(e.prototype,"id",{get:function(){return this._id},set:function(e){this._id=e},enumerable:!1,configurable:!0}),e}(),W=function(){},q=function(){function e(){}return Object.defineProperty(e,"Ajax",{get:function(){return"AJAX"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Config",{get:function(){return"CONFIG"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Di",{get:function(){return"DI"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Loader",{get:function(){return"LOADER"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Localization",{get:function(){return"LOCALIZATION"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Logger",{get:function(){return"LOGGER"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Store",{get:function(){return"STORE"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"EventStream",{get:function(){return"EVENT_STREAM"},enumerable:!1,configurable:!0}),e.AsServiceName=function(t){return t===R?e.Ajax:t===I?e.Config:t===M?e.Di:t===A?e.Loader:t===D?e.Localization:t===T?e.Logger:t===z?e.Store:t===j?e.EventStream:(new L).value},e}(),K="design:factory",B=function(e,t,r){var n=r.value;"function"==typeof n&&(r.value=n.bind(e),Reflect.defineMetadata(K,{target:e,propertyKey:t},r.value))};Object.defineProperty(B,"MetadataKey",{value:K});var U=B,G="design:injectableFactories",V=function(e){return function(t,r,n){var i=Reflect.getMetadata(G,t)||[];Reflect.defineMetadata(G,h(h([],p(i),!1),[{propertyKey:r,parameterIndex:n,factory:e}],!1),t)}};V.MetadataKey=G;var X=V;function J(e){}var Y="provide:options",H=function(e,t){return function(r){Reflect.defineMetadata(Y,a({provides:e},t||{}),r.prototype)}};H.MetadataKey=Y;var Z,Q=H,$=function(e){return function(e){return"function"==typeof e}(e)?ee(e):te(e)},ee=function(e){return e},te=function(e){return function(t){return t.prototype.namespace=e.namespace,t}},re=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t}((function(){})),ne=function(e){function t(t){var r=e.call(this,t)||this;return r.message=t,r}return o(t,e),t}(Error),ie=function(e){function t(t){var r=e.call(this,t)||this;return r.message=t,r}return o(t,e),t}(ne),oe=function(e){function t(t){var r=e.call(this,t)||this;return r.message=t,r}return o(t,e),t}(ne),ae=function(e){function t(t){void 0===t&&(t={});var r=e.call(this,"Redux Error")||this;return Object.keys(t).forEach((function(e){return r[e]=t[e]})),r}return o(t,e),t}(Error),se=function(){function e(e){this._value=e}return e.create=function(){return(new L).value},Object.defineProperty(e.prototype,"value",{get:function(){return(this._widget?this._widget+":":"")+this._value},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"widget",{get:function(){return this._widget},set:function(e){this._widget=e},enumerable:!1,configurable:!0}),s([U,u("design:type",Function),u("design:paramtypes",[]),u("design:returntype",void 0)],e,"create",null),e=s([Q(e),c(0,X(e.create)),u("design:paramtypes",[String])],e)}(),ce=function(e){this.value=e},ue=function(){function e(e){this.widget=e}return Object.defineProperty(e.prototype,"value",{get:function(){var e=this.widget;return e&&e.prototype&&e.prototype.namespace},enumerable:!1,configurable:!0}),e=s([Q(e),u("design:paramtypes",[re])],e)}(),fe=function(){function e(){}return Object.defineProperty(e,"RequireContext",{get:function(){return"bwtk/services/widgetLoader/RequireContext"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"RegistryPath",{get:function(){return"bwtk/services/widgetLoader/RegistryPath"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"VersionDelimiter",{get:function(){return"bwtk/services/widgetLoader/VersionDelimiter"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"WidgetBundleName",{get:function(){return"bwtk/services/widgetLoader/WidgetBundleName"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"StaticWidgetMappings",{get:function(){return"bwtk/services/widgetLoader/StaticWidgetMappings"},enumerable:!1,configurable:!0}),e}(),le=function(){function e(){}return Object.defineProperty(e,"DefaultLocale",{get:function(){return"bwtk/services/localization/defaultLocale"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"LocalizationServicesPath",{get:function(){return"bwtk/services/localization/Url"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Brand",{get:function(){return"bwtk/services/localization/brand"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Channel",{get:function(){return"bwtk/services/localization/channel"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Timeout",{get:function(){return"bwtk/services/localization/timeout"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"DebugDisableNetwork",{get:function(){return"bwtk/services/localization/disableNetwork"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"DebugShowKeys",{get:function(){return"bwtk/services/localization/showKeys"},enumerable:!1,configurable:!0}),e}(),de=f(442),pe=f(489),he=function(e){function t(t){var r=e.call(this,t)||this;return r.destroyed=!1,r.delayInit=!1,r._widget=null,r.loaded=!1,r.delayInitCallback=null,r.widgetRoot=null,r.updateWidgetProps=function(e){var t=["onLoad","onLoadError","widget"],n=Object.keys(e).filter((function(e){return t.indexOf(e)<0})).reduce((function(t,r){var n;return a(a({},t),((n={})[r]=e[r],n))}),{});r.widget&&Object.keys(n).forEach((function(e){return r.widget[e]=n[e]}))},r.state={widget:null},r}return o(t,e),Object.defineProperty(t.prototype,"widget",{get:function(){return this.destroyed?null:this.state.widget||this._widget},enumerable:!1,configurable:!0}),t.prototype.componentDidMount=function(){var e=this.props.onLoadError?this.props.onLoadError:function(){};S.instance.getWidget(this.props.widget,this.onWidgetLoaded.bind(this),e.bind(this))},t.prototype.onWidgetLoaded=function(e){var t=this;if(!this.destroyed){this._widget=e;var r=e.store||!1;r&&("localizationConfig"in this.props&&"object"==typeof this.props.localizationConfig&&"waitBeforeRender"in this.props.localizationConfig&&this.props.localizationConfig.waitBeforeRender&&(this.delayInit=!0),r.store?r.store.id.widget=this.props.widget:r.id.widget=this.props.widget),this.updateWidgetProps(this.props),this.loaded=!0;var n=function(r){void 0===r&&(r=[]),r.forEach((function(e){var r=Object.keys(e)[0];if("props"===r){var n=a(a({},t.props),e[r]);t.updateWidgetProps(n)}})),e.init(),t.setState({widget:e},(function(){"onLoad"in t.props&&"function"==typeof t.props.onLoad&&t.props.onLoad(t.widget)}))};e.delayInit?e.delayInit(n):this.delayInit?this.delayInitCallback=n:n()}},t.prototype.componentWillUnmount=function(){this.destroy()},t.prototype.destroy=function(){var e=this;if(!this.destroyed){if(this.destroyed=!0,this.widgetRoot)try{this.widgetRoot.render(de.createElement("div")),setTimeout((function(){e.widgetRoot&&(e.widgetRoot.unmount(),e.widgetRoot=null)}),0)}catch(e){this.widgetRoot=null}if(this._widget){var t=this._widget.store;if(t){var r=void 0;r=t.store?t.store.id:t.id;var n=t.localization;n&&("localization"in n&&(n=n.localization),n.cache&&n.cache.remove(r))}this._widget.destroy()}this.setState({widget:null})}},t.prototype.tryDelayedRender=function(e,t,r){var n=this;void 0===e&&(e=0),void 0===t&&(t=-1),this.loaded?(this.delayInit=!1,this.delayInitCallback&&this.delayInitCallback(),r&&r()):(-1===t||t>0)&&window.setTimeout((function(){return n.tryDelayedRender(e,-1===t?-1:t-1,r)}),e)},t.prototype.render=function(){var e=this;return this.destroyed||!this.widget||this.delayInit?null:de.createElement("div",{className:this.props.widget,ref:function(t){if(t&&!e.destroyed&&(e.widgetRoot||(e.widgetRoot=(0,pe.H)(t)),e.widget)){e.updateWidgetProps(e.props);try{e.widget.render(e.widgetRoot)}catch(e){}}}})},t}(de.Component),ye=function(){},ge=function(){},ve=f(418),be=function(){function e(e){this.ajax=e}return e.prototype.init=function(){this._client||(this._client=this.ajax.createClient(this.options))},Object.defineProperty(e.prototype,"client",{get:function(){return this._client||this.init(),this._client},enumerable:!1,configurable:!0}),e.prototype.get=function(e,t,r){var n=this;return this.createObservableRequest((function(){return n.client.get(e,t,r)}))},e.prototype.post=function(e,t,r){var n=this;return this.createObservableRequest((function(){return n.client.post(e,t,r)}))},e.prototype.put=function(e,t,r){var n=this;return this.createObservableRequest((function(){return n.client.put(e,t,r)}))},e.prototype.patch=function(e,t,r){var n=this;return this.createObservableRequest((function(){return n.client.patch(e,t,r)}))},e.prototype.del=function(e,t){var r=this;return this.createObservableRequest((function(){return r.client.del(e,t)}))},e.prototype.createObservableRequest=function(e){return new ve.Observable((function(t){e().then((function(e){t.next(e)})).catch((function(e){t.error(e)})).finally((function(){t.complete()}))}))},e=s([J,u("design:paramtypes",[R])],e)}(),me=function(){function e(e,t,r,n,i){var o=this;this.id=e,this.name=t,this.namespace=r,this.config=n,this.paramsProvider=i,this.id.widget=this.name.value,void 0!==this.defaultValues&&Object.keys(this.defaultValues).forEach((function(e){return o.setDefaultConfig(e,o.defaultValues[e])}))}return e.prototype.getConfig=function(e){return void 0!==this.paramsProvider.props[e]?this.paramsProvider.props[e]:this.config.getConfig(this.configKeyOf(e))},e.prototype.configKeyOf=function(e){return this.config.createKey(this.configKeyFormat(e),this.id.value)},e.prototype.configKeyFormat=function(e){return[this.namespace.value,this.name.value,e].filter((function(e){return e&&e.length>0})).join("/")},e.prototype.setConfig=function(e,t){this.config.setConfig(e,t)},e.prototype.setDefaultConfig=function(e,t){void 0===this.config?this.setDelayedDefaultConfig(e,t):this.config.setDefaultConfig(this.configKeyOf(e),t)},e.prototype.setDelayedDefaultConfig=function(e,t){void 0===this.defaultValues&&(this.defaultValues={}),this.defaultValues[e]=t},e=s([J,u("design:paramtypes",[se,ce,ue,I,ge])],e)}(),we=function(e){return function(t,r){return t.setDefaultConfig(r,e.defaultValue),Oe(t,r)}},Oe=function(e,t){return Object.defineProperty(e,t,{get:function(){return this.getConfig(t)},enumerable:!0,configurable:!0}),e},Pe=function(){function e(e,t){this.localization=e,this.config=t,this.disableNetwork=!1,this.disableNetworkGlobal=this.config.getConfig(le.DebugDisableNetwork,!1),this.showKeys=this.config.getConfig(le.DebugShowKeys,!1)}return Object.defineProperty(e.prototype,"defaultMessages",{get:function(){return{}},enumerable:!1,configurable:!0}),e.prototype.createReducer=function(){var e=this;this.localization.disableNetwork(this.disableNetworkGlobal||this.disableNetwork),this.localization.loadMessagesOnce();var t=this.localization.createReducer(),r=t(void 0,{}),n=r.loaded?r:a(a({},r),{messages:this.defaultMessages[r.locale]});return this.localization.loadMessages.bind(this.localization)(!0),function(r,i){var o;void 0===r&&(r=n);var s=t(r,i);return a(a({},s),{messages:e.showKeys?(o={},o[s.locale]={},o):a(a({},e.defaultMessages[s.locale]),s.messages)})}},e=s([J,u("design:paramtypes",[x,I])],e)}(),je=Object.prototype.hasOwnProperty,_e=function(e){return function(t){return function(t){return function(r){var n=Ee(r);return n.meta=a(a({},n.meta),{source:e.id.value}),e.notifyActionListener(n),t(r)}}}},Ee=function(e){var t=a({},e);return je.call(t,"data")&&!je.call(t,"payload")&&(t.payload=t.data,delete t.data),je.call(t,"meta")||(t.meta={}),t},Se=f(750),Ce=function(){function t(t){this.store=t,this.store.createStore=this.createStore.bind(this),this.epicMiddleware=(0,e.createEpicMiddleware)()}return t.prototype[Symbol.observable]=function(){throw new Error("Method not implemented.")},t.prototype.init=function(){},t.prototype.destroy=function(){},t.prototype.createStore=function(){var e=Se.compose,t=this.epicMiddleware;"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__&&(e=window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({name:this.id.value}));var r=(0,Se.legacy_createStore)(this.reducer,e(Se.applyMiddleware.apply(void 0,h(h([],p(this.additionalMiddlewares),!1),[t,_e(this.store)],!1))));return this.epicMiddleware.run(this.middlewares),r},Object.defineProperty(t.prototype,"id",{get:function(){return this.store.id},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"listenToAll",{get:function(){return this.store.listenToAll},set:function(e){this.store.listenToAll=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"middlewares",{get:function(){return function(e,t){return e.pipe()}},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"additionalMiddlewares",{get:function(){return[]},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"getState",{get:function(){return this.store.getState.bind(this.store)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dispatch",{get:function(){return this.store.dispatch.bind(this.store)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"subscribe",{get:function(){return this.store.subscribe.bind(this.store)},enumerable:!1,configurable:!0}),t.prototype.notifyActionListener=function(e){},t.prototype.replaceReducer=function(){throw new Error("Use 'reducer' property instead!")},t=s([J,u("design:paramtypes",[W])],t)}(),Le=function(){function e(e){this.stream=e,this.subscriptions=[]}return e.prototype.subscribe=function(e,t){var r=this.stream.subscribe(e,t);return this.subscriptions.push(r),r},e.prototype.subscribeAll=function(e){var t=this.stream.subscribeAll(e);return this.subscriptions.push(t),t},e.prototype.subscribeError=function(e){var t=this.stream.subscribeError(e);return this.subscriptions.push(t),t},e.prototype.send=function(e,t){this.stream.send(e,t)},e.prototype.sendError=function(e,t,r){this.stream.sendError(e,t,r)},e.prototype.unsubscribe=function(){this.subscriptions.forEach((function(e){return e()})),this.subscriptions=[]},Object.defineProperty(e.prototype,"errors",{get:function(){var e=this;return{subscribe:function(t){return e.subscribeError(t)},send:function(t,r){return e.sendError(t,r)}}},enumerable:!1,configurable:!0}),e=s([J,u("design:paramtypes",[j])],e)}(),Re={BaseClient:be,BaseConfig:me,configProperty:function(e,t){return"object"==typeof e&&e instanceof me?Oe(e,t||""):we("object"==typeof(r=e)&&1===Object.keys(r).length&&void 0!==r.defaultValue?e:{defaultValue:e});var r},BaseLocalization:Pe,BaseStore:Ce,BasePipe:Le,actionsToComputedPropertyName:function(e){return Object.keys(e).reduce((function(t,r){var n;return a(a({},t),((n={})[r]=e[r].toString(),n))}),{})}},Ie=function(e){function t(r){var n=e.call(this,r)||this;return n.state={hasError:!1},"name"in r&&(t.displayName=r.name),n}return o(t,e),t.prototype.componentDidCatch=function(e,t){this.setState({hasError:!0});var r=S.instance.getService(q.EventStream);e.componentStack=t.componentStack,r.sendError(y.REACT_BOUNDARY,e)},t.prototype.render=function(){if(this.state.hasError){if(!1===this.props.displayErrorMessage)return null;var e=this.props.errorMessage?this.props.errorMessage:"{component failed}";return de.createElement("div",null,e)}return this.props.children},t.displayName="ErrorBoundary",t.contextTypes={store:Object},t}(de.Component),ke=f(873),Me=function(){function e(e){this._metadata=Reflect.getMetadata(Q.MetadataKey,e.prototype)}return Object.defineProperty(e.prototype,"provides",{get:function(){return this.metadata.provides},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"withFactory",{get:function(){return this.metadata.withFactory},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"metadata",{get:function(){if(void 0===this._metadata)throw new Ae;return this._metadata},enumerable:!1,configurable:!0}),e}(),Ae=function(e){function t(){return e.call(this,t.Message)||this}return o(t,e),t.Message="Can't find Provide decorator in type.",t}(Error),xe=function(){function e(e){this._metadata=Reflect.getMetadata(U.MetadataKey,e)}return Object.defineProperty(e.prototype,"target",{get:function(){return this.metadata.target},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"propertyKey",{get:function(){return this.metadata.propertyKey},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"metadata",{get:function(){if(void 0===this._metadata)throw new De;return this._metadata},enumerable:!1,configurable:!0}),e.isFactory=function(t){try{return void 0!==e.readFactoryMetadata(t).target}catch(e){return!1}},e.readFactoryMetadata=function(t){return new e(t)},e}(),De=function(e){function t(){return e.call(this,t.Message)||this}return o(t,e),t.Message="Can't find factory decorator in type.",t}(Error),Te=function(){function e(e){this._metadata=Reflect.getMetadata(X.MetadataKey,e)}return Object.defineProperty(e.prototype,"metadata",{get:function(){if(void 0===this._metadata)throw new Fe;return this._metadata},enumerable:!1,configurable:!0}),e}(),Fe=function(e){function t(){return e.call(this,t.Message)||this}return o(t,e),t.Message="Can't find injectWithFactory decorator in type.",t}(Error),Ne=function(){function e(e){this.reader=xe.isFactory(e)?new Ke(e):new We(e)}return Object.defineProperty(e.prototype,"dependencies",{get:function(){return this.reader.dependencies},enumerable:!1,configurable:!0}),e}(),ze=function(){function e(){}return Object.defineProperty(e.prototype,"dependencies",{get:function(){var e=this;return this.metadata.map((function(t,r){var n=e.findInjectWithFactoryMetadataAtIndex(r);return void 0!==n?n.factory:t}))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"customFactories",{get:function(){return this._customFactories||(this._customFactories=this.readCustomFactories())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"metadata",{get:function(){return this._metadata||(this._metadata=this.readMetadata())},enumerable:!1,configurable:!0}),e}(),We=function(e){function t(t){var r=e.call(this)||this;return r.ctor=t,r}return o(t,e),t.prototype.readMetadata=function(){var e=Reflect.getMetadata("design:paramtypes",this.ctor);if(void 0===e)throw new qe;return e},t.prototype.findInjectWithFactoryMetadataAtIndex=function(e){return this.customFactories.find((function(t){return t.parameterIndex===e}))},t.prototype.readCustomFactories=function(){var e=new Te(this.ctor);try{return e.metadata}catch(e){return[]}},t}(ze),qe=function(e){function t(){return e.call(this,t.Message)||this}return o(t,e),t.Message="Can't find metadata information.\nPossible cause: missing class decorators",t}(Error),Ke=function(e){function t(t){var r=e.call(this)||this;return r.ctor=t,r}return o(t,e),t.prototype.readMetadata=function(){return Reflect.getMetadata("design:paramtypes",this.factoryMetadata.target,this.factoryMetadata.propertyKey)},t.prototype.findInjectWithFactoryMetadataAtIndex=function(e){var t=this;return this.customFactories.find((function(r){return r.propertyKey===t.factoryMetadata.propertyKey&&r.parameterIndex===e}))},Object.defineProperty(t.prototype,"factoryMetadata",{get:function(){return this._factoryMetadata||(this._factoryMetadata=new xe(this.ctor))},enumerable:!1,configurable:!0}),t.prototype.readCustomFactories=function(){var e=new Te(this.factoryMetadata.target);try{return e.metadata}catch(e){return[]}},t}(ze),Be=function(){function e(e,t){this.ctor=e,this._dependencies=t;var r=new ke.Inject;r.tokens=this.dependencies,r.tokens.length>0&&(0,ke.annotate)(this.factory,r),(0,ke.annotate)(this.factory,new ke.FactoryProvider)}return Object.defineProperty(e.prototype,"factory",{get:function(){return this._factory||(this._factory=xe.isFactory(this.ctor)?e.createFactoryFunctionFromFactory(this.ctor):void 0!==this.readFactoryFromProvideMetadata()?e.createFactoryFunctionFromFactory(this.readFactoryFromProvideMetadata()):e.createFactoryFunctionFromConstructor(this.ctor))},enumerable:!1,configurable:!0}),e.prototype.readFactoryFromProvideMetadata=function(){try{return new Me(this.ctor).withFactory}catch(e){return}},Object.defineProperty(e.prototype,"dependencies",{get:function(){return this._dependencies||(this._dependencies=this.readDependenciesFromMetadata())},enumerable:!1,configurable:!0}),e.prototype.readDependenciesFromMetadata=function(){try{return this.dependenciesMetadataReader.dependencies}catch(e){return[]}},Object.defineProperty(e.prototype,"dependenciesMetadataReader",{get:function(){return new Ne(this.readFactoryFromProvideMetadata()||this.ctor)},enumerable:!1,configurable:!0}),e.createFactoryFunctionFromConstructor=function(t){return e.createFactoryFunction(t,(function(e){return new e}))},e.createFactoryFunctionFromFactory=function(t){return e.createFactoryFunction(t,(function(e){return e()}))},e.createFactoryFunction=function(e,t){return function(){var r=Array.prototype.slice.call(arguments),n=Function.prototype.bind.apply(e,h([null],p(r),!1)),i=t(n);if("object"==typeof i&&!Array.isArray(i)){var o=new Ue(i,r);o.overrideInit(),o.overrideDestroy()}return i}},e}(),Ue=function(){function e(e,t){void 0===t&&(t=[]),this.instance=e,this.dependencies=t}return e.prototype.overrideInit=function(){this.overrideInstaceMethod("init")},e.prototype.overrideDestroy=function(){this.overrideInstaceMethod("destroy")},e.prototype.overrideInstaceMethod=function(e){var t,r,n,i=this.instance[e]||new Function,o=(t=this.dependencies,function(e){t.forEach((function(t){var r=t[e];if(void 0!==r)try{r()}catch(e){}}))});"function"==typeof i?this.instance[e]=(r=i.bind(this.instance),n=!1,function(){n||(n=!0,o(e),r())}):o(e)},e}(),Ge=function(){function e(e){this.ctor=e,this.defaultFactory=new Be(this.ctor)}return Object.defineProperty(e.prototype,"providers",{get:function(){return h(h([e.provide(this.ctor,this.defaultFactory.factory)],p(this.tryReadFromProvideMetadata()),!1),p(this.dependenciesProviders),!1)},enumerable:!1,configurable:!0}),e.prototype.tryReadFromProvideMetadata=function(){try{var t=this.provideMetadataReader.provides;return[e.provide(t,new Be(this.ctor).factory,!0),e.provide(q.AsServiceName(t),new Be((function(e){return e}),[t]).factory,!0)]}catch(e){return[]}},Object.defineProperty(e.prototype,"provideMetadataReader",{get:function(){return new Me(this.ctor)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dependenciesProviders",{get:function(){var t=this.defaultFactory.dependencies.map((function(t){return new e(t).providers}));return Array.prototype.concat.apply([],t)},enumerable:!1,configurable:!0}),e.provide=function(e,t,r){void 0===r&&(r=!1);var n=t,i=new ke.Provide(e);return i.hasPriority=r,(0,ke.annotate)(n,i),n},e}(),Ve=function(){function e(){this.providers=new Map}return e.prototype.appendProviders=function(e){var t=this;return e.map((function(e){return new Xe(e)})).forEach((function(e){t.providers.has(e.provides)&&!e.hasPriority||t.providers.set(e.provides,e.provider)})),this},e.prototype.removeLowPriorityProvidersFrom=function(e){var t=this;return e.map((function(e){return new Xe(e)})).forEach((function(e){t.providers.has(e.provides)&&(new Xe(t.providers.get(e.provides)).hasPriority||t.providers.delete(e.provides))})),this},e.prototype.toArray=function(){var e=[];return this.providers.forEach((function(t){return e.push(t)})),e},e}(),Xe=function(){function e(e){this.provider=e,this.provideAnnotation=this.provider.annotations.find((function(e){return void 0!==e.token}))}return Object.defineProperty(e.prototype,"provides",{get:function(){return this.provideAnnotation.token},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"hasPriority",{get:function(){return Boolean(this.provideAnnotation.hasPriority)},enumerable:!1,configurable:!0}),e}(),Je=function(){function e(e){this.defaultOptions={method:"GET",headers:{},body:"",mode:"cors",credentials:"omit",cache:"default",redirect:"follow",referrer:"about:client",referrerPolicy:"",integrity:""},this.defaultCustomOptions={url:"",dataType:"json"},this.options=a({},this.defaultOptions),this.customOptions=a({},this.defaultCustomOptions);var t=this.processOptions(e),r=t.initOptions,n=t.customOptions;this.options=r,this.customOptions=n,this.options.body=""}return e.prototype.processOptions=function(e,t){var r=a({},this.options);Object.keys(r).forEach((function(t){e[t]&&(r[t]=e[t])})),"boolean"==typeof e.cache&&(r.cache=e.cache?"default":"reload"),"reload"===r.cache&&(r.headers=a(a({},r.headers),{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:0})),"boolean"==typeof e.credentials&&(r.credentials=e.credentials?"include":"omit"),void 0===e.body&&delete r.body;var i=a({},this.customOptions);if(Object.keys(i).forEach((function(t){e[t]&&(i[t]=e[t])})),n.window&&n.window.CsrfToken&&n.window.CsrfToken.length>0){var o=t||"",s=i.url?i.url:"";s.length>0&&(o=s.concat(o)),0===o.toLowerCase().indexOf("https://api")&&(r.headers=a(a({},r.headers),{"X-CSRF-TOKEN":n.window.CsrfToken}))}return{initOptions:r,customOptions:i}},e.prototype.mergeOptions=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=h([this.options],p(e),!1),n=r.map((function(e){return e.headers})).reduce((function(e,t){return a(a({},e),t)}),{});return a(a({},r.reduce((function(e,t){return a(a({},e),t)}),{})),{headers:n})},e.prototype.buildRequestConfig=function(e,t){return this.processOptions(e,t)},e.prototype.buildRequestPath=function(e,t){return t.url?t.url.concat(e):e},e.prototype.buildQueryParameters=function(e,t,r){if(void 0===r||!(r instanceof Object))return"";var n=e+"/"+t,i=Object.keys(r).map((function(e){return encodeURIComponent(e)+"="+encodeURIComponent(r[e])})).join("&");return n.indexOf("?")>-1?n.indexOf("?")===n.length-1?i:"&"+i:"?"+i},e.prototype.getOptions=function(){return a(a({},this.options),this.customOptions)},e.prototype.setOptions=function(e){var t=this.mergeOptions(e),r=this.processOptions(t),n=r.initOptions,i=r.customOptions;this.options=n,this.customOptions=i},e}(),Ye=function(){function e(e){this.stream=e}return e.prototype.get=function(e,t,r){return this.createClient(r).get(e,t)},e.prototype.post=function(e,t,r){return this.createClient(r).post(e,t)},e.prototype.head=function(e,t){return this.createClient(t).head(e)},e.prototype.put=function(e,t,r){return this.createClient(r).put(e,t)},e.prototype.patch=function(e,t,r){return this.createClient(r).patch(e,t)},e.prototype.del=function(e,t){return this.createClient(t).del(e)},e.prototype.createClient=function(e){return void 0===e&&(e={}),new He(e,this.stream)},e=s([Q(R),u("design:paramtypes",[j])],e)}(),He=function(){function e(e,t){var r=this;this.stream=t,this.buildRequest=function(e,t){var n=r.config.buildRequestConfig(t,e),i=n.initOptions,o=n.customOptions;return new Promise((function(n,a){var s,c=!1;t.timeout&&t.timeout>0&&(s=setTimeout((function(){c=!0;var e="Request timed out after "+Math.floor(t.timeout/1e3)+"s";r.stream.sendError(y.AJAX,e),a({error:e})}),t.timeout)),fetch(r.config.buildRequestPath(e,o),i).then((function(e){clearTimeout(s),c||(e.ok?r.processResponse(e,{resolve:n,reject:a},"resolve",o):(r.stream.sendError(y.AJAX,e),r.processResponse(e,{resolve:n,reject:a},"reject",o)))}),(function(e){c||a({error:e})}))}))},this.config=new Je(e),this.stream||(this.stream={sendError:function(){}})}return e.prototype.get=function(e,t,r){void 0===r&&(r={});var n=this.config.mergeOptions(r,{method:"GET",body:void 0});return this.buildRequest(e+this.config.buildQueryParameters(n.url||"",e,t),n)},e.prototype.post=function(e,t,r){void 0===r&&(r={});var n=this.config.mergeOptions(r,{method:"POST"});return n.body=this.prepBodyData(n,t),this.buildRequest(e,n)},e.prototype.head=function(e,t){void 0===t&&(t={});var r=this.config.mergeOptions(t,{method:"HEAD",body:void 0});return this.buildRequest(e,r)},e.prototype.put=function(e,t,r){void 0===r&&(r={});var n=this.config.mergeOptions(r,{method:"PUT"});return n.body=this.prepBodyData(n,t),this.buildRequest(e,n)},e.prototype.patch=function(e,t,r){void 0===r&&(r={});var n=this.config.mergeOptions(r,{method:"PATCH"});return n.body=this.prepBodyData(n,t),this.buildRequest(e,n)},e.prototype.del=function(e,t){void 0===t&&(t={});var r=this.config.mergeOptions(t,{method:"DELETE",body:void 0});return this.buildRequest(e,r)},e.prototype.toAjaxResponse=function(e){var t=e,r=t.headers,n=t.redirected,i=t.status,o=t.statusText,s=t.type,c=t.url,u={headers:Object.keys(r).reduce((function(e,t){var n;return a(a({},e),((n={})[t]=r[t],n))}),{}),redirected:n,status:i,statusText:o,type:s,url:c,dataType:"",data:void 0};return u},e.prototype.getResponseType=function(e,t,r){void 0===e&&(e={}),void 0===t&&(t={}),void 0===r&&(r=!1);var n="";switch(n=(n=!r&&t.dataType?t.dataType:e["Content-Type"]||e["content-type"]||"").toLowerCase()){case"application/json":case"json":n="json";break;case"text/plain":case"text":default:n="text";break;case"blob":n="blob";break;case"binary":n="binary";break;case"stream":n="stream"}return n},e.prototype.processResponse=function(e,t,r,n){var i=this.toAjaxResponse(e),o=t[r],s=this.getResponseType(i.headers,n,"reject"===r);if(1223!==e.status)if("reject"!==r)if(i.dataType=s,204!==e.status)switch(s){case"stream":o(a(a({},i),{data:e.body}));break;case"json":e.json().then((function(e){return o(a(a({},i),{data:e}))}),(function(r){e.text().then((function(e){return t.reject(a(a({},i),{dataType:"text",data:e}))}),(function(e){return t.reject(a(a({},i),{data:e}))}))}));break;case"blob":e.blob().then((function(e){return o(a(a({},i),{data:e}))}),(function(e){return t.reject(a(a({},i),{dataType:"error",data:e}))}));break;case"binary":e.arrayBuffer().then((function(e){return o(a(a({},i),{data:e}))}),(function(e){return t.reject(a(a({},i),{dataType:"error",data:e}))}));break;default:e.text().then((function(e){return o(a(a({},i),{data:e}))}),(function(e){return t.reject(a(a({},i),{dataType:"error",data:e}))}))}else o(a(a({},i),{data:""}));else e.text().then((function(e){return o(a(a({},i),{dataType:"error",data:e}))}),(function(e){return o(a(a({},i),{dataType:"error",data:e}))}));else t.resolve(a(a({},i),{data:"",status:204,statusText:"No Content",dataType:"text"}))},e.prototype.prepBodyData=function(e,t){var r=e.headers||{};return"application/json"===Object.keys(r).reduce((function(e,t){return/^content-type$/i.test(t)?r[t]:e}),"")?JSON.stringify(t):t},e.prototype.setOptions=function(e){this.config.setOptions(e)},e.prototype.getOptions=function(){return this.config.getOptions()},e}(),Ze=function(){function e(e){this.emitter=new g.EventEmitter,this.store=e.createStore(this.id)}return Object.defineProperty(e.prototype,"id",{get:function(){return"DefaultConfigServices"},enumerable:!1,configurable:!0}),e.prototype.init=function(){var e=this;this.store.init(),this.store.replaceReducer((function(t,r){var n,i;switch(void 0===t&&(t={configs:{},defaultConfigs:{}}),r.type){case k.SET_CONFIG:return e.validateConfig(r.payload),a(a({},t),{configs:a(a({},t.configs),e.emitChangesAsync(t.configs,(n={},n[r.payload.key]=r.payload.value,n)))});case k.SET_DEFAULT_CONFIG:return e.validateConfig(r.payload),a(a({},t),{defaultConfigs:a(a({},t.defaultConfigs),(i={},i[r.payload.key]=r.payload.value,i))});default:return t}}))},e.prototype.validateConfig=function(e){if(e.key===N.SeverityLevel&&!(e.value in E))throw new Error("Invalid log level.")},e.prototype.splitKey=function(e){var t=e.split("@");return{fullKey:e,key:t[0],target:t[1]}},e.prototype.emitChangesAsync=function(e,t){var r=this;return setTimeout((function(){r.emitChanges(e,t)}),0),t},e.prototype.emitChanges=function(e,t){for(var r in t)t.hasOwnProperty(r)&&this.emitter.emit(r,{oldValue:e[r],newValue:t[r]});return t},e.prototype.createKey=function(e,t){return t?e.concat("@",t):e},e.prototype.setDefaultConfig=function(e,t){this.dispatch(e,t,k.SET_DEFAULT_CONFIG)},e.prototype.setConfig=function(e,t){this.dispatch(e,t,k.SET_CONFIG)},e.prototype.dispatch=function(e,t,r){this.store.dispatch({type:r,payload:{key:e,value:t}})},e.prototype.getConfig=function(e,t){void 0===t&&(t=void 0);var r=this.splitKey(e),n=this.getConfigState()[r.fullKey]||this.getConfigState()[r.key]||this.getDefaultConfigState()[r.fullKey]||this.getDefaultConfigState()[r.key];return void 0===n?t:n},e.prototype.getConfigState=function(){return this.store.getState().configs},e.prototype.getDefaultConfigState=function(){return this.store.getState().defaultConfigs},e.prototype.onChange=function(e,t){var r=this;return this.emitter.on(e,(function(e){return t(e.oldValue,e.newValue)})),{dispose:function(){r.emitter.removeListener(e,t)}}},e=s([Q(I),u("design:paramtypes",[z])],e)}(),Qe=function(){function e(){}return e.initLocaleData=function(e){e in this.cache?this.cache[e]=a(a({},this.cache[e]),{state:Z.PENGING}):this.cache[e]={locale:{},state:Z.PENGING,usedBy:[]}},e.setLocaleData=function(e,t){var r=a(a({},this.cache[e].locale),t);this.cache[e]=a(a({},this.cache[e]),{locale:r,state:Z.READY})},e.tryUseLocaleData=function(e){var t=this.getLocaleData(e);t&&t.state===Z.READY&&this.useLocaleData(e)},e.useLocaleData=function(e){var t=this.normalizeId(e),r=this.getLocaleData(e);r&&(r.usedBy=$e(h(h([],p(r.usedBy),!1),[t.id],!1)),this.cache[t.widget]=r,this.runOnReadyCallbacks(t.widget))},e.addCallback=function(e,t){var r,n=this.normalizeId(e);if(!this.callbackExists(e)){var i=n.widget,o=((r={})[n.id]=t,r);i in this.callbacks?this.callbacks[i].push(o):this.callbacks[i]=[o]}},e.removeCallback=function(e){var t=this.normalizeId(e),r=t.widget;r in this.callbacks&&(this.callbacks[r]=this.callbacks[r].filter((function(e){return Object.keys(e)[0]!==t.id})))},e.runOnReadyCallbacks=function(e){var t=this;if(e in this.callbacks){var r=[];this.callbacks[e].forEach((function(e){e[Object.keys(e)[0]]()||r.push(e[Object.keys(e)[0]])})),0===r.length?delete this.callbacks[e]:this.callbacks[e]=r;var n=Object.keys(this.mappings).reduce((function(r,n){return t.mappings[n].indexOf(e)>-1?h(h([],p(r),!1),[n],!1):[]}),[]);n.forEach((function(e){return t.runOnReadyCallbacks(e)}))}},e.callbackExists=function(e){for(var t=this.normalizeId(e).id,r=Object.keys(this.callbacks),n=0;n<r.length;n++)for(var i=this.callbacks[r[n]],o=0;o<i.length;o++)if(Object.keys(i[o])[0]===t)return!0;return!1},e.getLocaleData=function(e){var t=this,r=this.normalizeId(e);return $e(h([r.widget],p(this.getMappings(r.widget)),!1)).reduce((function(e,r){return null!==e&&e.state===Z.READY||!(r in t.cache)?e:t.cache[r]}),null)},e.isLocaleDataReady=function(e,t){var r=this.getLocaleData(e);return!!r&&t in r.locale},e.isFinished=function(e,t){var r=this.getLocaleData(e);return null!==r&&r.state===Z.READY&&t in r.locale&&!this.callbackExists(e)},e.isPending=function(e){var t=this.getLocaleData(e);return null!==t&&t.state===Z.PENGING},e.setPermanentlyFailed=function(e){var t=this,r=this.normalizeId(e);$e(h([r.widget],p(this.getMappings(r.widget)),!1)).forEach((function(e){e in t.cache&&(t.cache[e]=a(a({},t.cache[e]),{locale:{},state:Z.FAILED}))}))},e.loadingFailed=function(e){var t=this,r=this.normalizeId(e);return $e(h([r.widget],p(this.getMappings(r.widget)),!1)).reduce((function(e,r){return r in t.cache&&t.cache[r].state===Z.FAILED||e}),!1)},e.remove=function(e,t){var r=this;void 0===t&&(t=!1);var n=this.normalizeId(e),i=n.id;""!==i&&(Object.keys(this.cache).forEach((function(e){r.cache[e].usedBy=r.cache[e].usedBy.filter((function(e){return i!==e}))})),t&&this.removeCacheData(n))},e.removeCacheData=function(e){var t=this,r=Object.keys(this.cache),n=r.filter((function(e){return t.cache[e].usedBy.length>0}));r.filter((function(e){return n.indexOf(e)<0})).forEach((function(e){return delete t.cache[e]}))},e.addMapping=function(e){var t=this;Object.keys(e).forEach((function(r){var n=Array.isArray(e[r])?e[r]:[e[r]];r in t.mappings?t.mappings[r]=$e(h(h([],p(t.mappings[r]),!1),p(n),!1)):t.mappings[r]=n}))},e.getMappings=function(e){var t=this.normalizeId(e);return t.widget in this.mappings?this.mappings[t.widget]:[]},e.normalizeId=function(e){return"string"==typeof e?{id:"",widget:e}:{id:e.value,widget:e.widget}},e.isMapped=function(e){return this.normalizeId(e).widget in this.mappings},e.cache={},e.callbacks={},e.mappings={},e}();!function(e){e[e.PENGING=1]="PENGING",e[e.READY=2]="READY",e[e.FAILED=3]="FAILED"}(Z||(Z={}));var $e=function(e){return e.filter((function(e,t,r){return r.indexOf(e)===t}))},et=function(e){function t(t,r,n,i,o,a){var s=e.call(this)||this;return s.widgetStore=n,s.localizationServices=i,s.logger=o,s.stream=a,s.skipLoadMessages=!1,s.networkDisabled=!1,s.fullPath=r?"".concat(r,"/").concat(t):t,s.widgetId=s.widgetStore.id,s}return o(t,e),t.create=function(e,t,r,n){return e.createChildLocalization(r.value,t,n&&n.value)},Object.defineProperty(t.prototype,"messagesDictionary",{get:function(){var e=this.localizationServices.cache.getLocaleData(this.widgetId);return e&&e.state===Z.READY?e.locale:{en:{},fr:{}}},enumerable:!1,configurable:!0}),t.prototype.dispatchLocaleChanged=function(){var e=this.getState().locale;this.localizationServices.cache.isLocaleDataReady(this.widgetId,e)?this.widgetStore.dispatch(w()):this.loadMessages(!0,!0)},t.prototype.dispatchMessagesLoaded=function(e){var t=this;if(this.widgetStore.initialized){var r=e?P(e):P();this.widgetStore.dispatch(r)}else setTimeout((function(){return t.dispatchMessagesLoaded(e)}))},t.prototype.createReducer=function(){var e=this;return this.loadMessages(),function(t,r){switch(void 0===t&&(t=e.getState()),r.type){case P.toString():case w.toString():return e.getState();case O.toString():return e.loadMessages(),t;default:return t}}},t.prototype.disableNetwork=function(e){void 0===e&&(e=!0),this.networkDisabled=e},t.prototype.loadMessagesOnce=function(){this.skipLoadMessages=!0},t.prototype.loadMessages=function(e,t){var r=this;if(void 0===e&&(e=!1),void 0===t&&(t=!1),!(this.networkDisabled||!e&&this.skipLoadMessages)){var n=this.getState().locale,i=this.localizationServices.cache;if(!i.loadingFailed(this.widgetId)){var o=function(){if(t&&r.widgetStore.dispatch(w()),i.loadingFailed(r.widgetId)){var e={message:"failed to pre-load locale data",id:r.widgetId.value};r.dispatchMessagesLoaded(new ae({error:e,widget:r.widgetId.widget})),r.logger.error(e),r.stream.sendError(y.LOCALIZATION,e)}else r.dispatchMessagesLoaded();return!0};i.isFinished(this.widgetId,n)?o():(i.addCallback(this.widgetId,o),i.isMapped(this.widgetId)||(i.isPending(this.widgetId)?i.tryUseLocaleData(this.widgetId):(i.initLocaleData(this.widgetId.widget),this.localizationServices.loadMessages(this.fullPath).then((function(e){var t=e.data;i.setLocaleData(r.widgetId.widget,t),i.useLocaleData(r.widgetId)})).catch((function(e){var t={message:"failed to load locale data",id:r.widgetId.value,response:e};i.setPermanentlyFailed(r.widgetId),i.removeCallback(r.widgetId),r.dispatchMessagesLoaded(new ae(t)),r.logger.error(t),r.stream.sendError(y.LOCALIZATION,t)})))))}}},t.prototype.getState=function(){var e=this.localizationServices.locale,t=this.localizationServices.fullLocale,r=this.messagesDictionary[e]||{};return{locale:e,fullLocale:t,messages:r,formats:this.localizationServices.formats[e],loaded:Object.keys(r).length>0}},t.prototype.destroy=function(){this.localizationServices.cache.remove(this.widgetId),this.localizationServices.unsubscribe(this)},s([U,u("design:type",Function),u("design:paramtypes",[D,W,ce,ue]),u("design:returntype",void 0)],t,"create",null),t=s([Q(x,{withFactory:t.create}),u("design:paramtypes",[String,String,W,st,F,j])],t)}(x),tt=new Map;function rt(e,t,r){try{if(tt.has(t))try{it(t),setTimeout((function(){nt(e,t,r)}),0)}catch(n){nt(e,t,r)}else nt(e,t,r)}catch(e){}}function nt(e,t,r){try{var n=(0,pe.H)(t),i=de.createElement(he,a(a({},r),{widget:e}));n.render(i),tt.set(t,n)}catch(e){}}function it(e){if(tt.has(e))try{tt.get(e).unmount(),tt.delete(e)}catch(t){tt.delete(e)}}var ot=function(){function e(){}return e.load=function(e,t){var r=this;void 0===t&&(t=function(){});var n=S.instance.getService(q.Localization),i=[];Object.keys(e).forEach((function(t){var o=e[t];i.push(new Promise((function(e){n.loadMessages(o).then((function(n){var i,s;n.data?(Qe.setLocaleData(t,n.data),Qe.runOnReadyCallbacks(t),t in r.widgets&&(r.widgets[t].forEach((function(e){return e.tryDelayedRender()})),delete r.widgets[t]),e(((i={})[t]=n.data,i))):e(a(((s={})["error"+t]={widget:t,path:o},s),n.error))}),(function(r){var n;e(a(((n={})["error"+t]={widget:t,path:o},n),r.error))}))})))})),Promise.all(i).then((function(e){var r=[];t(e.reduce((function(e,t){var n=Object.keys(t)[0];return n.startsWith("error")?r.push(t[n]):e[n]=t[n],e}),{}),r)}))},e.localize=function(e){var t=this,r=e.widgets,n=e.messages;return Object.keys(n).forEach((function(e){Qe.initLocaleData(e)})),r.forEach((function(e){var r=p(h([],p(e),!1),3),n=r[0],i=r[1],o=r[2],s=void 0===o?{}:o;s.localizationConfig=a(a({},s.localizationConfig||{}),{waitBeforeRender:!0});var c=rt(n,i,s);n in t.widgets?t.widgets[n]=h(h([],p(t.widgets[n]),!1),[c],!1):t.widgets[n]=[c]})),new Promise((function(e,r){t.load(n,(function(n,i){var o;i.length?(i.forEach((function(e){var t=e.widget;return Qe.setPermanentlyFailed(t)})),o=function(){return r(i)}):o=function(){return e(n)},Object.keys(t.widgets).forEach((function(e){return t.widgets[e].forEach((function(t){t.tryDelayedRender(100,10,(function(){return Qe.runOnReadyCallbacks(e)}))}))})),t.widgets={},o()}))}))},e.widgets={},e}();const at=JSON.parse('{"en":{"date":{"short":{"month":"long","day":"numeric","hour":"numeric","minute":"2-digit","hour12":true,"formatMatcher":"basic"},"long":{"weekday":"long","year":"numeric","month":"long","day":"numeric","formatMatcher":"basic"},"yMMMd":{"month":"short","day":"numeric","year":"numeric","formatMatcher":"basic"},"yMMMMd":{"month":"long","day":"numeric","year":"numeric","formatMatcher":"basic"}},"time":{"hours":{"hour":"numeric","hour12":true},"short":{"hour":"numeric","minute":"2-digit","hour12":true}},"number":{"CAD":{"style":"currency","currency":"CAD","currencyDisplay":"symbol"}}},"fr":{"date":{"short":{"month":"long","day":"numeric","hour":"2-digit","minute":"2-digit","formatMatcher":"basic"},"long":{"weekday":"long","year":"numeric","month":"long","day":"numeric","formatMatcher":"basic"},"yMMMd":{"month":"short","day":"numeric","year":"numeric","formatMatcher":"basic"},"yMMMMd":{"month":"long","day":"numeric","year":"numeric","formatMatcher":"basic"}},"time":{"hours":{"hour":"2-digit","formatMatcher":"basic"},"short":{"hour":"2-digit","minute":"2-digit","formatMatcher":"basic"}},"number":{"CAD":{"style":"currency","currency":"CAD","currencyDisplay":"symbol"}}}}');var st=function(){function e(e,t,r,n,i){this.ajax=e,this.config=t,this.loggerServices=n,this.stream=i,this.localizedWidgets=[],this.disableNetwork=!1,this.brand="",this.channel=null,this.baseUrl="",this.timeout=-1,this.store=r.createStore(this.id)}return Object.defineProperty(e.prototype,"id",{get:function(){return"DefaultLocalizationServices"},enumerable:!1,configurable:!0}),e.prototype.init=function(){var e=this;this.disableNetwork=this.config.getConfig(le.DebugDisableNetwork,!1),this.brand=this.config.getConfig(le.Brand,""),this.timeout=this.config.getConfig(le.Timeout,-1),this.channel=this.config.getConfig(le.Channel,null),this.store.init(),this.disableNetwork||(this.baseUrl=this.config.getConfig(le.LocalizationServicesPath,""),this.client=this.ajax.createClient({cache:"default"})),this.store.listenToAll=!0;var t=this.config.getConfig(le.DefaultLocale)||"en";this.store.replaceReducer((function(e,r){return void 0===e&&(e={locale:t}),r.type===m.toString()?t=a(a({},e),{locale:r.payload}):e})),this.store.subscribe((function(){var r=e.store.getState();r.locale!==t&&(t=r.locale,e.localizedWidgets.forEach((function(e){return e.dispatchLocaleChanged()})))}))},Object.defineProperty(e.prototype,"locale",{get:function(){return this.store.getState().locale},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fullLocale",{get:function(){return this.locale+"-CA"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"formats",{get:function(){return at},enumerable:!1,configurable:!0}),e.prototype.createChildLocalization=function(e,t,r){var n=new et(e,r,t,this,this.loggerServices.createLogger(q.Localization,"@widget=".concat(e),"@namespace=".concat(r)),this.stream);return this.localizedWidgets=h(h([],p(this.localizedWidgets),!1),[n],!1),n},e.prototype.unsubscribe=function(e){this.localizedWidgets=this.localizedWidgets.filter((function(t){return t!==e}))},e.prototype.loadMessages=function(e){if(this.disableNetwork)return Promise.resolve({data:{en:{},fr:{}}});var t={},r=this.baseUrl,n="";/\{WIDGET\}/.test(r)?r=r.replace(/\{WIDGET\}/g,e):n=(r.endsWith("/")?"":"/")+e,/\{BRAND\}/.test(r)?r=r.replace(/\{BRAND\}/g,this.brand):""!==this.brand&&(t=a(a({},t),{brand:this.brand}));var i={url:r,headers:{"Accept-Language":this.locale,Channel:this.channel}};return this.timeout>0&&(i.timeout=this.timeout),this.client.get(n,Object.keys(t).length>0?t:null,i)},e.prototype.setLocale=function(e){this.store.dispatch(m(e))},Object.defineProperty(e.prototype,"cache",{get:function(){return Qe},enumerable:!1,configurable:!0}),e.prototype.getLocalizedString=function(e,t,r){void 0===r&&(r=this.store.getState().locale);var n=this.cache.getLocaleData(e);return n&&n.state===Z.READY&&r in n.locale&&t in n.locale[r]?n.locale[r][t]:""},e.prototype.addLocaleDataMapping=function(e){Qe.addMapping(e)},e.prototype.preloadLocaleData=function(e,t){return void 0===t&&(t=[]),ot.localize({messages:e,widgets:t})},e.prototype.destroyAll=function(){for(var e in this.store.destroy(),this)delete this[e]},e=s([Q(D),u("design:paramtypes",[R,I,z,T,j])],e)}(),ct=function(){function e(e,t){this.config=e,this.context=t}return Object.defineProperty(e.prototype,"severityLevel",{get:function(){return this.config.getConfig(this.severityLevelConfigKey)||E.None},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"severityLevelConfigKey",{get:function(){return this.config.createKey(N.SeverityLevel,this.context)},enumerable:!1,configurable:!0}),e}(),ut=function(e){function t(t,r,n){var i=e.call(this)||this;return i.context=t,i.config=r,i.labels=n,i}return o(t,e),t.create=function(e,t,r,n){return e.createLogger(r.value,"@id=".concat(t.value),"@namespace=".concat(n&&n.value))},t.prototype.debug=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];this.hasSeverityLevel(E.Debug)&&this.call.apply(this,h(["debug",e],p(t),!1))},t.prototype.error=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];this.hasSeverityLevel(E.Errors)&&this.call.apply(this,h(["error",e],p(t),!1))},t.prototype.info=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];this.hasSeverityLevel(E.Info)&&this.call.apply(this,h(["info",e],p(t),!1))},t.prototype.log=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];this.hasSeverityLevel(E.Logs)&&this.call.apply(this,h(["log",e],p(t),!1))},t.prototype.warn=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];this.hasSeverityLevel(E.Warnings)&&this.call.apply(this,h(["warn",e],p(t),!1))},t.prototype.clear=function(){n.window.console.clear()},t.prototype.hasSeverityLevel=function(e){return(this.config.severityLevel&e)===e},t.prototype.call=function(e,t){for(var r,i=[],o=2;o<arguments.length;o++)i[o-2]=arguments[o];var a=0===this.labels.length?["[%s]\n",this.context]:["[%s], labels=[%O]\n",this.context,this.labels];(r=n.window.console)[e].apply(r,h([],p(h(h(h([],p(a),!1),[t],!1),p(i),!1)),!1))},s([U,u("design:type",Function),u("design:paramtypes",[T,se,ce,ue]),u("design:returntype",void 0)],t,"create",null),t=s([Q(F,{withFactory:t.create}),u("design:paramtypes",[String,ct,Array])],t)}(F),ft=function(e){function t(t){var r=e.call(this)||this;return r.config=t,r}return o(t,e),t.prototype.createLogger=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return new ut(e,new ct(this.config,e),t)},t=s([Q(T),u("design:paramtypes",[I])],t)}(T),lt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.prototype.createParamsProvider=function(e){return new dt(e)},t=s([Q(ye)],t)}(ye),dt=function(e){function t(t){var r=e.call(this)||this;return r.instanceProvider=t,r}return o(t,e),Object.defineProperty(t.prototype,"props",{get:function(){return this.instanceProvider()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"events",{get:function(){return this.instanceProvider()},enumerable:!1,configurable:!0}),t}(ge),pt=function(e){function t(t,r){var n=e.call(this)||this;return n.id=t,n.storeServices=r,n.initialized=!1,n.listenToAll=!1,n.unsubscribers=[],n.dispatch=function(e){return n.store.dispatch(e)},n.subscribe=function(e){var t=n.store.subscribe(e);return n.unsubscribers=h(h([],p(n.unsubscribers),!1),[t],!1),function(){n.unsubscribers=n.unsubscribers.filter((function(e){return e!==t})),t()}},n}return o(t,e),t.prototype[Symbol.observable]=function(){throw new Error("Method not implemented.")},t.prototype.init=function(){this.store=this.createStore(),this.unregister=this.storeServices.registerStore(this),this.initialized=!0},t.prototype.createStore=function(){var e=Se.compose;return"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__&&(e=window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({name:this.id.value})),(0,Se.legacy_createStore)((function(e){return e}),e((0,Se.applyMiddleware)(_e(this))))},t.prototype.getState=function(){return this.store?this.store.getState():null},t.prototype.replaceReducer=function(e){this.store.replaceReducer(e)},t.prototype.notifyActionListener=function(e){this.storeServices.notifyActionListener(e)},t.prototype.destroy=function(){this.unsubscribers.forEach((function(e){return e()})),this.unsubscribers=[],this.unregister()},t=s([Q(W),u("design:paramtypes",[se,z])],t)}(W),ht=function(e){function t(t){var r=e.call(this)||this;return r.stream=t,r.globalActionListeners=[],r.stores=[],r.state={},r.getState=function(){return r.state},r.dispatch=function(e){var t=a({},e),n=t.meta||{};return t.meta=a(a({},n),{broadcasted:!0,origin:n.source||""}),r.stores.forEach((function(e){return e.dispatch(t)})),e},r}return o(t,e),Object.defineProperty(t.prototype,"id",{get:function(){return"ReduxStoreServices"},enumerable:!1,configurable:!0}),t.prototype.createStore=function(e){return new pt(new se(e),this)},t.prototype.createGlobalActionListener=function(e,t){var r=this;void 0===t&&(t={});var n=a(a({},{showBroadcasted:!1}),t);return this.globalActionListeners.push({listener:e,params:n}),function(){r.globalActionListeners=r.globalActionListeners.filter((function(t){return t.listener!==e}))}},t.prototype.tryInvokeActionListeners=function(e){try{this.globalActionListeners.forEach((function(t){(e.meta.broadcasted&&t.params.showBroadcasted||!e.meta.broadcasted)&&t.listener(e)}))}catch(e){this.stream.sendError(y.REDUX,e)}},t.prototype.registerStore=function(e){var t=this;e.subscribe((function(){return t.state[e.id.value]=e.getState()}));var r=new yt(e,this,this.stream);return this.stores.push(r),function(){t.stores=t.stores.filter((function(e){return e!==r})),delete t.state[e.id.value]}},t.prototype.notifyActionListener=function(e){this.tryInvokeActionListeners(e)},t.prototype.replaceReducer=function(e){},t.prototype.destroy=function(){},t.prototype.destroyAll=function(){this.stores.forEach((function(e){e.store.destroy()})),this.stores=[],this.globalActionListeners=[],this.state={}},t=s([Q(z),u("design:paramtypes",[j])],t)}(z),yt=function(){function e(e,t,r){var n=this;this.store=e,this.originalStoreDispatch=e.dispatch.bind(e),e.dispatch=function(e){var i=n.originalStoreDispatch(e);try{var o=a({},e);o.meta=a(a({},o.meta||{}),{source:n.store.id.value}),t.dispatch(o)}catch(e){r.sendError(y.REDUX,e)}return i}}return Object.defineProperty(e.prototype,"id",{get:function(){return this.store.id.value},enumerable:!1,configurable:!0}),e.prototype.dispatch=function(e){return this.shouldInvokeOriginalDispatch(e)?this.originalStoreDispatch(e):e},e.prototype.shouldInvokeOriginalDispatch=function(e){return this.store.listenToAll&&e.meta.source!==this.store.id.value||Array.isArray(e.meta.target)&&e.meta.target.indexOf(this.store.id.value)>=0},e}(),gt=function(){function e(e,t){this.config=e,this.logger=t,this.registry={}}return e.prototype.init=function(){this.registerStaticModules()},e.prototype.reset=function(){this.registry={},this.registerStaticModules()},e.prototype.load=function(e){return Promise.resolve(void 0)},e.prototype.registerStaticModules=function(){var e=this,t=this.config.getConfig(fe.StaticWidgetMappings);Object.keys(t).forEach((function(r){e.registerStaticModule(r,t[r])}))},e.prototype.registerStaticModule=function(e,t){this.registry[e]=a(a({},function(e){return{namespace:vt(e)?e.namespace:void 0,factory:bt(e)?e:mt(e)?e.factory:void 0,url:wt(e)?e:"function"!=typeof e&&e.url?e.url:void 0}}(t)),this.registry[e])},e=s([Q(A),u("design:paramtypes",[I,F])],e)}();function vt(e){return void 0!==e.namespace}function bt(e){return"function"==typeof e}function mt(e){return void 0!==e.factory}function wt(e){return"string"==typeof e}var Ot=function(){function e(e,t,r,n){this.moduleId=e,this.resolver=t,this.communicator=r,this.registry=n}return e.prototype.load=function(){var e=this;return new Promise((function(t){e.communicator.get(e.moduleId,e.resolvedUrl,t)}))},Object.defineProperty(e.prototype,"resolvedUrl",{get:function(){if(void 0!==this.moduleDefinitionUrl)return this.moduleDefinitionUrl;var e=this.resolver.module(this.moduleId);return this.resolver.url(e)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"moduleDefinitionUrl",{get:function(){return this.registry[this.moduleId]&&this.registry[this.moduleId].url},enumerable:!1,configurable:!0}),e}(),Pt=function(){function e(e,t,r){this.moduleId=e,this.registry=t,this.loader=r}return e.prototype.resolve=function(){var e=this;return new Promise((function(t){void 0!==e.factory?Promise.resolve(e.factory.call(null)).then(t):e.isGlobalScript?t(e.globalScript):e.loader.load().then(t)}))},Object.defineProperty(e.prototype,"factory",{get:function(){return this.registry[this.moduleId]&&this.registry[this.moduleId].factory},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isGlobalScript",{get:function(){return void 0!==this.globalScript},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"globalScript",{get:function(){return n.window[this.moduleId]||n.window[this.moduleIdAsGlobalVar]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"moduleIdAsGlobalVar",{get:function(){return f(663)(this.moduleId)},enumerable:!1,configurable:!0}),e}(),jt=function(){function e(e,t){this.config=e,this.moduleId=t}return Object.defineProperty(e.prototype,"widgetBundle",{get:function(){return this.config.getConfig(fe.WidgetBundleName)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"versionDelimiter",{get:function(){return this.config.getConfig(fe.VersionDelimiter)},enumerable:!1,configurable:!0}),e.prototype.resolve=function(){return this.resolveModulePath()},e.prototype.resolveModulePath=function(){return this.resolveBaseModulePath().concat("/",this.widgetBundle)},e.prototype.resolveBaseModulePath=function(){return this.hasTargetVersion()?this.module().concat("/",this.version()):this.module()},e.prototype.hasTargetVersion=function(){return void 0!==this.version()},e.prototype.version=function(){return this.moduleIdParts[1]},e.prototype.module=function(){return this.moduleIdParts[0]},Object.defineProperty(e.prototype,"moduleIdParts",{get:function(){return this.moduleId.split(this.versionDelimiter)},enumerable:!1,configurable:!0}),e}(),_t=function(e){function t(t,r){var i=e.call(this,t,r)||this;return i.useRequireJs=void 0!==n.window.bwtkRequireJS,i.requireContext="",i}return o(t,e),t.createLogger=function(e){return e.createLogger(q.Loader)},Object.defineProperty(t.prototype,"inject",{get:function(){return this._inject||(this._inject=Inject.createContext().Inject)},enumerable:!1,configurable:!0}),t.prototype.init=function(){var t=this;e.prototype.init.call(this),this.useRequireJs?this.requireContext=this.config.getConfig(fe.RequireContext,""):(this.inject.setModuleRoot(this.config.getConfig(fe.RegistryPath)),this.inject.addModuleRule(/.*/,(function(e){return void 0!==(t.registry[e]&&t.registry[e].factory)?e:new jt(t.config,e).resolve()})),this.inject.addFetchRule(/.*/,(function(e,r,n,i,o){var a=o.moduleId;new Pt(a,t.registry,new Ot(a,n,i,t.registry)).resolve().then((function(t){e(null,t)}))}))),this.config.setDefaultConfig(fe.VersionDelimiter,"@"),this.config.setDefaultConfig(fe.WidgetBundleName,"widget"),this.useRequireJs||this.config.onChange(fe.RegistryPath,(function(e,r){return t.inject.setModuleRoot(r)}))},t.prototype.reset=function(){e.prototype.reset.call(this),this.useRequireJs||this._inject.reset()},t.prototype.load=function(e){var t=this,r=this.registry[e]||void 0;return this.useRequireJs&&r&&n.window.define(e,[],(function(){return(r.factory||function(){})()})),new Promise((function(i,o){(t.useRequireJs?t.requireContext.length?n.window.bwtkRequireJS[t.requireContext].require:n.window.bwtkRequireJS.require:t.inject.require)([e],(function(n){t.logger.info('INFO - Widget loaded: "'.concat(e,'"'));var o=n.default||n,s=r&&r.namespace;void 0!==s&&(o.prototype=a(a({},o.prototype),{namespace:s})),i(o)}))}))},s([U,u("design:type",Function),u("design:paramtypes",[T]),u("design:returntype",void 0)],t,"createLogger",null),t=s([Q(A),c(1,X(t.createLogger)),u("design:paramtypes",[I,F])],t)}(gt);function Et(e){return"function"==typeof e}function St(e){return function(t){if(function(e){return Et(null==e?void 0:e.lift)}(t))return t.lift((function(t){try{return e(t,this)}catch(e){this.error(e)}}));throw new TypeError("Unable to lift unknown Observable type")}}var Ct,Lt,Rt=((Lt=function(e){Error.call(e),e.stack=(new Error).stack},Ct=function(e){Lt(this),this.message=e?e.length+" errors occurred during unsubscription:\n"+e.map((function(e,t){return t+1+") "+e.toString()})).join("\n  "):"",this.name="UnsubscriptionError",this.errors=e}).prototype=Object.create(Error.prototype),Ct.prototype.constructor=Ct,Ct);function It(e,t){if(e){var r=e.indexOf(t);0<=r&&e.splice(r,1)}}var kt=function(){function e(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}var t;return e.prototype.unsubscribe=function(){var e,t,r,n,i;if(!this.closed){this.closed=!0;var o=this._parentage;if(o)if(this._parentage=null,Array.isArray(o))try{for(var a=d(o),s=a.next();!s.done;s=a.next())s.value.remove(this)}catch(t){e={error:t}}finally{try{s&&!s.done&&(t=a.return)&&t.call(a)}finally{if(e)throw e.error}}else o.remove(this);var c=this.initialTeardown;if(Et(c))try{c()}catch(e){i=e instanceof Rt?e.errors:[e]}var u=this._finalizers;if(u){this._finalizers=null;try{for(var f=d(u),l=f.next();!l.done;l=f.next()){var y=l.value;try{Mt(y)}catch(e){i=null!=i?i:[],e instanceof Rt?i=h(h([],p(i)),p(e.errors)):i.push(e)}}}catch(e){r={error:e}}finally{try{l&&!l.done&&(n=f.return)&&n.call(f)}finally{if(r)throw r.error}}}if(i)throw new Rt(i)}},e.prototype.add=function(t){var r;if(t&&t!==this)if(this.closed)Mt(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=null!==(r=this._finalizers)&&void 0!==r?r:[]).push(t)}},e.prototype._hasParent=function(e){var t=this._parentage;return t===e||Array.isArray(t)&&t.includes(e)},e.prototype._addParent=function(e){var t=this._parentage;this._parentage=Array.isArray(t)?(t.push(e),t):t?[t,e]:e},e.prototype._removeParent=function(e){var t=this._parentage;t===e?this._parentage=null:Array.isArray(t)&&It(t,e)},e.prototype.remove=function(t){var r=this._finalizers;r&&It(r,t),t instanceof e&&t._removeParent(this)},e.EMPTY=((t=new e).closed=!0,t),e}();function Mt(e){Et(e)?e():e.unsubscribe()}kt.EMPTY;var At=null,xt=null,Dt=!1,Tt=!1,Ft={setTimeout:function(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];var i=Ft.delegate;return(null==i?void 0:i.setTimeout)?i.setTimeout.apply(i,h([e,t],p(r))):setTimeout.apply(void 0,h([e,t],p(r)))},clearTimeout:function(e){var t=Ft.delegate;return((null==t?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0};function Nt(){}var zt=Wt("C",void 0,void 0);function Wt(e,t,r){return{kind:e,value:t,error:r}}var qt=null,Kt=function(e){function t(t){var r,n=e.call(this)||this;return n.isStopped=!1,t?(n.destination=t,((r=t)instanceof kt||r&&"closed"in r&&Et(r.remove)&&Et(r.add)&&Et(r.unsubscribe))&&t.add(n)):n.destination=Yt,n}return o(t,e),t.create=function(e,t,r){return new Vt(e,t,r)},t.prototype.next=function(e){this.isStopped?Jt(function(e){return Wt("N",e,void 0)}(e),this):this._next(e)},t.prototype.error=function(e){this.isStopped?Jt(Wt("E",void 0,e),this):(this.isStopped=!0,this._error(e))},t.prototype.complete=function(){this.isStopped?Jt(zt,this):(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(e){this.destination.next(e)},t.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(kt),Bt=Function.prototype.bind;function Ut(e,t){return Bt.call(e,t)}var Gt=function(){function e(e){this.partialObserver=e}return e.prototype.next=function(e){var t=this.partialObserver;if(t.next)try{t.next(e)}catch(e){Xt(e)}},e.prototype.error=function(e){var t=this.partialObserver;if(t.error)try{t.error(e)}catch(e){Xt(e)}else Xt(e)},e.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(e){Xt(e)}},e}(),Vt=function(e){function t(t,r,n){var i,o,a=e.call(this)||this;return Et(t)||!t?i={next:null!=t?t:void 0,error:null!=r?r:void 0,complete:null!=n?n:void 0}:a&&Tt?((o=Object.create(t)).unsubscribe=function(){return a.unsubscribe()},i={next:t.next&&Ut(t.next,o),error:t.error&&Ut(t.error,o),complete:t.complete&&Ut(t.complete,o)}):i=t,a.destination=new Gt(i),a}return o(t,e),t}(Kt);function Xt(e){var t;Dt?(t=e,Dt&&qt&&(qt.errorThrown=!0,qt.error=t)):function(e){Ft.setTimeout((function(){if(!At)throw e;At(e)}))}(e)}function Jt(e,t){var r=xt;r&&Ft.setTimeout((function(){return r(e,t)}))}var Yt={closed:!0,next:Nt,error:function(e){throw e},complete:Nt},Ht=function(e){function t(t,r,n,i,o,a){var s=e.call(this,t)||this;return s.onFinalize=o,s.shouldUnsubscribe=a,s._next=r?function(e){try{r(e)}catch(e){t.error(e)}}:e.prototype._next,s._error=i?function(e){try{i(e)}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._error,s._complete=n?function(){try{n()}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._complete,s}return o(t,e),t.prototype.unsubscribe=function(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;e.prototype.unsubscribe.call(this),!r&&(null===(t=this.onFinalize)||void 0===t||t.call(this))}},t}(Kt);function Zt(e,t){return St((function(r,n){var i,o,a,s=0;r.subscribe(new Ht(n,(function(r){return e.call(t,r,s++)&&n.next(r)}),i,o,a))}))}var Qt=function(){function e(){var e=this;this.executeCallback=function(t,r,n){try{r.call(null,t)}catch(t){void 0!==n?n.call(null,t):e.sendError(y.STREAM,t)}},this.subscribeAll=function(t){var r=e.subject.subscribe((function(r){return e.executeCallback(r,t)})),n=e.errorStream.subscribe((function(r){return e.executeCallback(r,t)}));return function(){r.unsubscribe(),n.unsubscribe()}}}return e.prototype.init=function(){this.subject=new ve.Subject,this.errorStream=new ve.ReplaySubject(10)},e.prototype.subscribe=function(e,t){var r,n=this;if("function"==typeof e)return this.subject.subscribe((function(t){return n.executeCallback(t,e)})).unsubscribe;var i="string"==typeof e?((r={})[e]=t||function(){},r):e,o=Object.keys(i).map((function(e){return n.subject.pipe(Zt((function(t){return t.type===e}))).subscribe((function(t){return n.executeCallback(t,i[e])}))}));return function(){return o.forEach((function(e){return e.unsubscribe()}))}},e.prototype.subscribeError=function(e){var t=this,r=this.errorStream.subscribe((function(r){return t.executeCallback(r,e,(function(e){}))}));return r.unsubscribe.bind(r)},e.prototype.send=function(e,t){this.subject.next({type:e,payload:t})},e.prototype.sendError=function(e,t,r){var n;e instanceof Error?n={type:y.GENERIC,payload:{error:e},error:!0}:(n={type:e,error:!0},void 0!==t&&(n.payload={error:t})),r&&(n.payload=n.payload?a(a({},n.payload),{widget:r}):{widget:r}),this.errorStream.next(n)},Object.defineProperty(e.prototype,"errors",{get:function(){var e=this;return{subscribe:function(t){return e.subscribeError(t)},send:function(t,r){return e.sendError(t,r)}}},enumerable:!1,configurable:!0}),e.prototype.unsubscribe=function(){this.subject.unsubscribe(),this.errorStream.unsubscribe()},e.prototype.destroy=function(){},e=s([Q(j)],e)}(),$t=(new Ve).appendProviders(new Ge(Qt).providers).appendProviders(new Ge(Ye).providers).appendProviders(new Ge(Ze).providers).appendProviders(new Ge(st).providers).appendProviders(new Ge(ft).providers).appendProviders(new Ge(lt).providers).appendProviders(new Ge(ht).providers).appendProviders(new Ge(_t).providers).toArray(),er=(new Ve).appendProviders(new Ge(se).providers).appendProviders(new Ge(ue).providers).appendProviders(new Ge(ut).providers).appendProviders(new Ge(pt).providers).appendProviders(new Ge(et).providers).removeLowPriorityProvidersFrom($t).toArray(),tr=function(){function e(e,t,r){this.globalInjector=e,this.widgetName=t,this.widgetConstructor=r}return e.prototype.createWidgetInstance=function(){return this.instance=this.injector.get(this.widgetConstructor),this.instance},Object.defineProperty(e.prototype,"injector",{get:function(){return this._injector||(this._injector=this.globalInjector.createChild(this.providers))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"providers",{get:function(){var e=this;return(new Ve).appendProviders(er).appendProviders([Ge.provide(ce,(function(){return new ce(e.widgetName)}),!0),Ge.provide(re,(function(){return e.widgetConstructor}),!0),Ge.provide(ge,new Be((function(t){return t.createParamsProvider((function(){return e.instance}))}),[ye]).factory,!0)]).appendProviders(new Ge(this.widgetConstructor).providers).removeLowPriorityProvidersFrom($t).toArray()},enumerable:!1,configurable:!0}),e}(),rr=function(){function e(e){this.injector=e}return e.prototype.createInstance=function(e,t){return new tr(this.injector,e,t).createWidgetInstance()},e}(),nr=function(e){function t(t){var r=e.call(this)||this;return r.interceptors=new Map,r.baseinjector=r.injector=function(e){var t=new ke.Injector((new Ve).appendProviders($t).appendProviders(new Ge(e).providers).toArray());return t.createChild(ir(t))}(t),r}return o(t,e),Object.defineProperty(t.prototype,"di",{get:function(){return this.injector.get(M)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"loader",{get:function(){return this.injector.get(A)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"store",{get:function(){return this.injector.get(z)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"stream",{get:function(){return this.injector.get(j)},enumerable:!1,configurable:!0}),t.prototype.getService=function(e){return this.injector.get(e)},t.prototype.addServiceInterceptor=function(e,t,r){var n=this,i=Array.isArray(t)?t:["*"],o=r||t;return i.forEach((function(t){return n.appendInterceptor(e,t,o)})),this.rebuildInjectorWithInterceptors(),function(){i.forEach((function(t){return n.getMembersInterceptors(e).set(t,n.getInterceptors(e,t).filter((function(e){return e!==o})))})),n.rebuildInjectorWithInterceptors()}},t.prototype.appendInterceptor=function(e,t,r){this.interceptors.set(e,this.createMemberInterceptorMap(e,t,r))},t.prototype.createMemberInterceptorMap=function(e,t,r){return this.getMembersInterceptors(e).set(t,h(h([],p(this.getInterceptors(e,t)),!1),[r],!1))},t.prototype.getInterceptors=function(e,t){return this.getMembersInterceptors(e).get(t)||[]},t.prototype.getMembersInterceptors=function(e){return this.interceptors.get(e)||new Map},t.prototype.rebuildInjectorWithInterceptors=function(){var e=this,t=[];this.interceptors.forEach((function(r,n){t=h([],p(e.createProxyBindings(n,r)),!1)})),this.injector=this.injector.createChild(t),this.injector=this.injector.createChild(h([],p(ir(this.injector)),!1))},t.prototype.createProxyBindings=function(e,t){var r=this.baseinjector.get(e);return or(e,(function(){return r}))},t}(S);function ir(e){return or(q.Di,new Be((function(){return new rr(e)})).factory)}function or(e,t){return[Ge.provide(e,t,!0),Ge.provide(ar(e),new Be((function(e){return e}),[e]).factory,!0)]}function ar(e){switch(e){case q.Ajax:return R;case q.Store:return z;case q.Config:return I;case q.Localization:return D;case q.Loader:return A;case q.Di:return M;case q.Logger:return T;case q.EventStream:return j;default:return null}}var sr=n.window,cr="function"==typeof sr.bwtkEventListener?sr.bwtkEventListener:function(){},ur=function(){function e(e){this.config=e,this.optionKeys={localization:{webServicesPath:le.LocalizationServicesPath,defaultLocale:le.DefaultLocale,brand:le.Brand,timeout:le.Timeout,disableNetwork:le.DebugDisableNetwork,showKeys:le.DebugShowKeys},loader:{registryPath:fe.RegistryPath,staticWidgetMappings:fe.StaticWidgetMappings},logger:{severityLevel:N.SeverityLevel}}}return e.prototype.setConfig=function(e,t){var r,n=p(e.split("."),2),i=n[0],o=n[1];r=i&&i in this.optionKeys&&o&&o in this.optionKeys[i]?this.optionKeys[i][o]:e,this.config.setConfig(r,t)},e}(),fr=function(e,t){void 0===e&&(e={}),lr(n.window,t);var r=a({"loader.registryPath":"","localization.webServicesPath":"","loader.staticWidgetMappings":{}},e),i=new nr(_t);S.setInstanceProvider((function(){return i})),S.instance.getService(q.EventStream).init();var o=S.instance.getService(q.Config);o.init(),"string"==typeof t&&t.length&&o.setDefaultConfig(fe.RequireContext,t);var s=new ur(o);Object.keys(r).forEach((function(e){return s.setConfig(e,r[e])})),S.instance.getService(q.Loader).init(),S.instance.getService(q.Localization).init(),void 0!==n.window.__BWTK_DEVTOOLS_EXTENSION__&&n.window.__BWTK_DEVTOOLS_EXTENSION__.connect(void 0),cr("INIT_END")},lr=function(e,t){if(void 0===e.bwtkRequireJS)e.ReactIntl;else{var r=t?e.bwtkRequireJS[t]:e.bwtkRequireJS;if(void 0===r)return;r.require(["react-intl"],(function(t){t&&(e.ReactIntl=t)}))}},dr=!1,pr=function(){dr||(dr=!0,void 0!==n.window.__BWTK_DEVTOOLS_EXTENSION__&&n.window.__BWTK_DEVTOOLS_EXTENSION__.disconnect(),tt.forEach((function(e,t){e.unmount(),t.remove()})),tt.clear(),S.instance.getService(q.EventStream).unsubscribe(),S.instance.getService(q.Localization).destroyAll(),S.instance.getService(q.Store).destroyAll())};e.combineEpics,t.FormattedMessage,r.connect;var hr=n.window;hr._Inject&&(hr._Inject(hr),hr._Inject=void 0,hr.Inject.disableAMD())})(),l})(),"object"==typeof exports&&"object"==typeof module?module.exports=t(require("redux-observable"),require("react-intl"),require("react-redux"),require("redux-actions"),require("react"),require("react-dom"),require("rxjs"),require("redux")):"function"==typeof define&&define.amd?define("bwtk",["redux-observable","react-intl","react-redux","redux-actions","react","react-dom","rxjs","redux"],t):"object"==typeof exports?exports.bwtk=t(require("redux-observable"),require("react-intl"),require("react-redux"),require("redux-actions"),require("react"),require("react-dom"),require("rxjs"),require("redux")):e.bwtk=t(e.ReduxObservable,e.ReactIntl,e.ReactRedux,e.ReduxActions,e.React,e.ReactDOM,e.rxjs,e.Redux);
//# sourceMappingURL=bwtk.min.js.map