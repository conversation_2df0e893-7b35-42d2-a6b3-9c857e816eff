import React from "react";

import { LocalizationState } from "bwtk";
import { IntlProvider } from "react-intl";
import { IStoreState } from "./store/Store";
import { connect } from "react-redux";
import {Container} from "@bell/bell-ui-library";
import { PaymentMethod } from "./views/PaymentMethod";
import {TermsAndCondition} from "./views/TermsAndCondition";
import { CurrentBalance, SelectBills } from "./views/CheckboxCard";
// import { PaymentAlreadyExist } from "./views/PaymentMethod/PaymentAlreadyExist";
// import { PaymentAlreadyExistRadio } from "./views/PaymentMethod/PaymentAlreadyExistRadio";
// import { PaymentInputFormFieldsPaymentAlreadyExist } from "./views/Form/PaymentInputFormFieldsPaymentAlreadyExist";
// import { Confirmation } from "./views/Confirmation";
// import {SelectBills} from "./views/CheckboxCard";
import { CreditCardInputValue, defaultCreditCardInputValue, PaymentItem, defaultBankInputValue, InputBankAccountDetail, AccountInputValues, SubscriberOffersWithBan } from "./models";
import { IAppOwnProps } from "./models/App";
import Config from "./Config";
// import { Confirmation } from "./views/Confirmation";
import { CurrentSection, PaymentItemAccountType, PageTitleCurrentSection } from "./models/Enums";
import { Confirmation } from "./views/Confirmation";
import { getRedirectUrl, getInteracBankInfo, } from "./store/Actions";
import Loader from "./views/Loader/Loader";
import { getBanSpecificTransactionId } from "./utils";
import { APIFailure } from "./views/ErrorPage";
// import { number } from "prop-types";




interface AppState {
  config: Config | null; // config can be null initially before it is loaded
  loading: boolean;
  error: string | null;
  paymentItem: PaymentItem[];
  currentSteps: any;
  isLoading: boolean;
  creditCardAutopayOffers: SubscriberOffersWithBan[];
  debitCardAutopayOffers: SubscriberOffersWithBan[];
  bankitems: any[];
  language: "en" | "fr";
}

// const selectedMethod = PreAuthorizedPaymentMethod.Debit;


class Component extends React.Component<AppConnectedProps, any, AppState> {
  static displayName = "App";

  constructor(props: AppConnectedProps) {
    super(props);
    const enableSelectBills = props.Config?.getPaymentItem?.length > 1 ? true : false;
    // const autopayOfferscheck = props.Config?.debitCardAutopayOffers || props.Config?.creditCardAutopayOffers || [];
    // const phone = props?.Config?.creditCardAutopayOffers?.[0]?.AutopayEligibleSubscribers?.[0]?.subscriberTelephoneNumber;
    // const normalizedPhone = phone.replace(/\D/g, '');
        
    // const enableSelectBills = items.length > 1 ? true : false;
    // Preauth
    // All Ban Preauth
    this.state = {
      localizationLoaded: true,
      paymentHeadingStepState: enableSelectBills ? "inactive" : "active",
      currentSteps: ":",
      currentSection: enableSelectBills ? CurrentSection.SelectBills : CurrentSection.PaymentMethod,
      creditCardInputValue: defaultCreditCardInputValue,
      BankInputValue: defaultBankInputValue ,
      isBankSelected: false,
      checkedBillItems: enableSelectBills === false ? props.Config?.getPaymentItem : [],
      accountInputValues: enableSelectBills === false 
        ? [{
          accountNumber: props.Config?.getPaymentItem[0]?.Ban,
          subNumber: props.Config?.getPaymentItem[0]?.subscriberId, // This can be null or undefined if not provided
          transactionID: getBanSpecificTransactionId(props.Config?.getPaymentItem[0]?.Ban, props.Config?.transactionIdArray),
          payBalanceAmnt: 0,
          // incentiveDiscountDetails: [{
          //   autopayEligibleSubscribers:autopayOfferscheck
          //   ?.flatMap(offer => offer.AutopayEligibleSubscribers ?? [])
          //   .map(subscriber => ({
          //     mdn: subscriber.subscriberTelephoneNumber?.replace(/\D/g, ''), // Normalize phone
          //     autopayOffers: subscriber.autopayOffers?.map(offer => ({
          //       newDiscountAmount: offer.currentdiscountAmount ?? 0,
          //       currentDiscountAmount: offer.discountAmount ?? 0,
          //       offerImpact:offer.action ?? ''
          //     })) ?? [],
                    
          //   })),
          //    selectedPaymentMethod: selectedMethod === PreAuthorizedPaymentMethod.Debit ? "D" : "C"
          // }] 
                
        }]
        : [],
      checkedCurrentBalanceItems: [],
      notOptedBalanceItems: [],
      apiSatusIsFailed: false,
      InteracCode: ""
    };

    this.onCurrentSteps = this.onCurrentSteps.bind(this);
    this.setIsHeadingStepActive = this.setIsHeadingStepActive.bind(this);
    this.setCreditCardInputValue = this.setCreditCardInputValue.bind(this);
    this.setBankInputValue = this.setBankInputValue.bind(this);;
    this.setCurrentSection = this.setCurrentSection.bind(this);
    this.setCheckedBillItems = this.setCheckedBillItems.bind(this);
    this.setAccountValues = this.setAccountValues.bind(this);
    this.setCheckedCurrentBalanceItems = this.setCheckedCurrentBalanceItems.bind(this);
    this.setIsBankSelected = this.setIsBankSelected.bind(this);
    this.setNotOptedBalanceItems = this.setNotOptedBalanceItems.bind(this);
    this.setApiSatusIsFailed = this.setApiSatusIsFailed.bind(this);
    this.setInteracCode = this.setInteracCode.bind(this);
  }

  setInteracCode(InteracCode : any) {
    this.setState({InteracCode});
  }
    
  onCurrentSteps(step: any) {
    if (this.state.currentSteps !== step) {
      this.setState({ currentSteps: step });
    }
  }

  setIsHeadingStepActive(show: boolean){
    this.setState({paymentHeadingStepState: show});
  }

  setIsBankSelected(bankSelected: boolean){
    this.setState({isBankSelected: bankSelected});
  }

  setCreditCardInputValue = (inputValue: CreditCardInputValue) => {
    this.setState({ creditCardInputValue: inputValue });
  };
  setBankInputValue = (inputvalue: InputBankAccountDetail) => {
    this.setState({ BankInputValue: inputvalue });
  };

  setCurrentSection = (section: CurrentSection) => {
    this.setState({ currentSection: section });
  };

  setCheckedBillItems = (newArray: PaymentItem[]) => {
    this.setState({ checkedBillItems: newArray });
  };

  setApiSatusIsFailed = (isFailed: boolean) => {
    this.setState({ apiSatusIsFailed: isFailed });
  };
  
  getQueryParameter = (param: string) => new URLSearchParams(document.location.search.substring(1)).get(param);

  removeSessionStorageCheckedItems() {
    const items = sessionStorage.getItem("itemsChecked");
    if (items && items.length > 0) {
      sessionStorage.removeItem("itemsChecked");
    }
  }

  componentDidMount() {
    const code = this.getQueryParameter("code");
    if (code && code != null) {
      const newUrl = this.props.Config?.currentUrl || '';
      window.history.pushState({ path: newUrl }, '', newUrl);
      this.setInteracCode(code);
      this.props.getInteracBankInfoAction(code);
      if (this.props.Config?.getPaymentItem?.length === 1) {
        this.removeSessionStorageCheckedItems();
      }
    }
    else {
      this.removeSessionStorageCheckedItems();
      // this.props.redirectUrlAction({});
    }

    this.props.redirectUrlAction({});   // call redirecturl code on every load
  }


  

  setCheckedCurrentBalanceItems = (newArray: PaymentItem[]) => {
    this.setState({checkedCurrentBalanceItems: newArray});
  };

  setAccountValues = (newArray: AccountInputValues[]) => {
    this.setState({accountInputValues: newArray});
  };

  setNotOptedBalanceItems = (newArray: PaymentItem[]) => {
    this.setState({notOptedBalanceItems: newArray});
  };
   
  render() {
    const { 
      localization, 
      Config,
    } = this.props;

    // var config: any = (window as any)["config"];
    
    
 
                    
    const enableSelectBills = Config?.getPaymentItem?.length > 1 ? true : false;
    const IsSingleClickEnabled = Config?.IsSingleClickEnabled === "ON" ? true :false ;
    const IsAutopayCreditEnabled = Config?.IsAutopayCreditEnabled === "ON" ? true : false ;
    // let enableSelectBills = items.length > 1 ? true : false;
    let enableSingleClickForPAD = Config?.getPaymentItem?.some(item => item.Due > 0 && item.isOneTimePaymentEligible && item.AccountType !== PaymentItemAccountType.OneBill && !item.isLastBillOnPreauth) ?? false;
    let enableSingleClickForPACC = Config?.getPaymentItem?.some(item => item.Due > 0 && item.isOneTimeCreditCardPaymentEnabled && item.AccountType !== PaymentItemAccountType.OneBill && !item.isLastBillOnPreauth) ?? false;
    const isPreauth: boolean = Config?.getPaymentItem?.some(item => item.IsOnPreauthorizedPayments ?? false);
    

    const { 
      // currentSteps,
      currentSection,
      creditCardInputValue,
      BankInputValue,
      isBankSelected,
      checkedBillItems,
      accountInputValues,
      checkedCurrentBalanceItems,
      notOptedBalanceItems,
      apiSatusIsFailed,
      InteracCode
    } = this.state;

    const checkedBillItemsHasBalance = checkedBillItems?.some((x: PaymentItem)=> x.Due > 0 && x.AccountType !== PaymentItemAccountType.OneBill && !x.isLastBillOnPreauth) ?? false;

    enableSingleClickForPAD = enableSingleClickForPAD && checkedBillItemsHasBalance && IsSingleClickEnabled;
    enableSingleClickForPACC = enableSingleClickForPACC && checkedBillItemsHasBalance && IsSingleClickEnabled;
         
    return (
      <IntlProvider 
        locale={localization.locale} 
        messages={localization.messages}
      >
        {this.props.isLoading ? <Loader/> :
          <Container>
            {!apiSatusIsFailed &&
                  <>
                    {currentSection !== CurrentSection.Confirmation &&
                      <>
                        <SelectBills 
                          paymentItem={Config.getPaymentItem} 
                          isShow={enableSelectBills} 
                          onCurrentSteps={this.onCurrentSteps}
                          setCurrentSection={this.setCurrentSection}
                          currentSection={currentSection}
                          setCheckedBillItems={this.setCheckedBillItems}
                          paymentItems={Config.getPaymentItem} 
                          setAccountValues={this.setAccountValues}
                          accountInputValues={accountInputValues}
                          transactionIds={Config.transactionIdArray}
                        />
                      
                        <PaymentMethod paymentItem={Config.getPaymentItem} 
                          isHeadingStepActive={currentSection === CurrentSection.PaymentMethod ? "active": "inactive"} 
                          isSingleClickEnableForPACC={enableSingleClickForPAD}
                          isSingleClickEnableForPAD={enableSingleClickForPACC}
                          onCurrentSteps={this.onCurrentSteps}
                          setHeadingSteps={this.setIsHeadingStepActive}
                          setInputValue={this.setCreditCardInputValue}
                          inputValue={creditCardInputValue}
                          setInputBankValue={this.setBankInputValue}
                          inputBankValue={BankInputValue}
                          setIsBankSelected ={this.setIsBankSelected}
                          setCurrentSection={this.setCurrentSection}
                          currentSection={currentSection}
                          checkedBillItems={checkedBillItems}
                          bankList={Config.getBankList}
                          accountInputValues={accountInputValues}
                          creditCardAutopayOffers = {Config.creditCardAutopayOffers}
                          debitCardAutopayOffers={Config.debitCardAutopayOffers}
                          language={Config.language as "en" | "fr"}
                          isInteractEnabled = {Config.IsInteracEnabled}
                          IsAutopayCreditEnabled = {IsAutopayCreditEnabled}
                          InteracCode={InteracCode}
                        />

                        {(enableSingleClickForPAD || enableSingleClickForPACC) &&
                          <CurrentBalance 
                            paymentItem={Config.getPaymentItem} 
                            checkedBillItems={checkedBillItems}
                            checkedCurrentBalanceItems={checkedCurrentBalanceItems}
                            setCheckedCurrentBalanceItems={this.setCheckedCurrentBalanceItems}
                            isShow={currentSection === CurrentSection.CurrentBalance} 
                            onCurrentSteps={this.onCurrentSteps}
                            setCurrentSection={this.setCurrentSection}
                            currentSection={currentSection}
                            language={Config.language as "en" | "fr"}
                            setAccountValues={this.setAccountValues}
                            accountInputValues={accountInputValues}
                            transactionIds={Config.transactionIdArray}
                            isBankPaymentSelected={isBankSelected}
                            setNotOptedBalanceItems={this.setNotOptedBalanceItems}
                        
                          />
                        }
                    

                        <TermsAndCondition 
                          isActive={currentSection === CurrentSection.TermsAndCondition}  
                          onCurrentSteps={this.onCurrentSteps}
                          setCurrentSection={this.setCurrentSection}
                          currentSection={currentSection}
                          checkedBillItems={checkedBillItems}
                          paymentItem={Config.getPaymentItem} 
                          province={Config.province}
                          language={Config.language}
                          userProfileProv={Config.userProfileProvince}
                          accountInputValues={accountInputValues}
                          isBankSelected={isBankSelected}
                          setApiSatusIsFailed={this.setApiSatusIsFailed}
                          creditCardAutopayOffers = {Config.creditCardAutopayOffers}
                          debitCardAutopayOffers={Config.debitCardAutopayOffers}/>
                      </>
                    }

                    {currentSection === CurrentSection.Confirmation &&
                  <Confirmation
                    paymentItem={Config.getPaymentItem} 
                    checkedBillItems={checkedBillItems} 
                    checkedCurrentBalanceItems={checkedCurrentBalanceItems}
                    showPaymentSummary={true}
                    isNewbank={false} 
                    isPreauth={isPreauth}
                    inputValue={creditCardInputValue} 
                    isShow={currentSection === CurrentSection.Confirmation}
                    inputBankValue = {BankInputValue}
                    isBankPaymentSelected = {isBankSelected}
                    BankList={Config.getBankList}
                    showCurrentBalance={enableSingleClickForPAD || enableSingleClickForPACC}
                    language={Config.language as "en" | "fr"}
                    accountInputValues={accountInputValues}
                    currentSection={currentSection}
                    notOptedBalanceItems={notOptedBalanceItems}
                    setApiSatusIsFailed={this.setApiSatusIsFailed}  creditCardAutopayOffers = {Config.creditCardAutopayOffers}
                    debitCardAutopayOffers={Config.debitCardAutopayOffers}  bankitems={[]}   
                    apiSatusIsFailed={apiSatusIsFailed}    
                    backCTAURL={Config.flowInitUrl}     
                                    
                  />
                    }
                  </>
            }
            {apiSatusIsFailed &&
                <APIFailure></APIFailure>
            }
          </Container> 
        }            
      </IntlProvider>
    );
  }
  parseDOMString = (e:string) =>  // Convert Spacial Characters specially for French word.
    new DOMParser().parseFromString(
      e,
      "text/html"
    ).documentElement.textContent;
    
  // Update the title dynamically (Start)
  
  componentDidUpdate(prevProps: AppConnectedProps, prevState: AppState) {
    let pageTitle = this.props.Config.pagetitle || "";
    const PageTitleConfirmation = pageTitle.replace(/Step/g, '');
    if (prevState.currentSteps !== this.state.currentSteps) {
      pageTitle =  `${pageTitle} ${this.state.currentSteps}`;
      document.title =  this.parseDOMString(pageTitle) || "";
    }
      
    if (this.state.currentSection === CurrentSection.Confirmation) { // Update page te for Confirmation only      
      const pageConfimationTitle = `${PageTitleCurrentSection.Confirmation} ${PageTitleConfirmation}` ;
      document.title =  this.parseDOMString(pageConfimationTitle) || "";
    }
      
    if (this.state.apiSatusIsFailed)
    {
      const container = document.getElementById("container");
      if (container && container.childElementCount > 0) {
        container.replaceWith(...Array.from(container.childNodes));
      }
    }
  }
    
  // // Update the title dynamically (END)

}



export interface AppConnectedProps {
  localization: LocalizationState;
  Config: Config;
  redirectUrlAction: Function;
  getInteracBankInfoAction: Function;
  isLoading: Boolean;
}
const mapStateToProps = (state: IStoreState, ownProps: AppConnectedProps) => ({
  localization: state.localization,
  Config: ownProps.Config,
  isLoading: state.isLoading
});

const mapDispatchToProps = (dispatch: React.Dispatch<any>) => ({
  redirectUrlAction: () => {dispatch(getRedirectUrl({}));},
  getInteracBankInfoAction: (code: string) => { dispatch(getInteracBankInfo({code})); }
});

export const App = connect<
  IStoreState,
  IAppOwnProps
>(
  mapStateToProps as any,
  mapDispatchToProps as any
)(Component);
