import * as React from "react";
import { <PERSON><PERSON>, But<PERSON>, Heading, Text } from "@bell/bell-ui-library";
import { injectIntl } from "react-intl";
import AlertNotificationList from "./AlertNotificationList";
import AlertNotificationListItem from "./AlertNotificationListItem";
import { AccountInputValues, PaymentItem } from "../../models";

interface AlertErrorOneTimePaymentProps {
  intl: any;
  checkedCurrentBalanceItems: PaymentItem[];
  submitMultiOrderPayment: any;
  accountInputValue: AccountInputValues[];
  language: "en" | "fr";
  notOptedBalanceItems: PaymentItem[];
  setOmnitureOnOneTimePaymentFailure: Function;
}

const AlertErrorOneTimePaymentComponent = ({intl, checkedCurrentBalanceItems, submitMultiOrderPayment, accountInputValue, language, notOptedBalanceItems, setOmnitureOnOneTimePaymentFailure}:AlertErrorOneTimePaymentProps) => {
  const ALERT_ERROR_OTP_ALL_BALANCE = intl.formatMessage({id: "ALERT_ERROR_OTP_ALL_BALANCE"});
  const ALERT_ERROR_HEADING_SOME_BALANCE = intl.formatMessage({id: "ALERT_ERROR_HEADING_SOME_BALANCE"});
  const ALERT_ERROR_OTP_BALANCE_DESC = intl.formatMessage({id: "ALERT_ERROR_OTP_BALANCE_DESC"});
  const ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR = intl.formatMessage({id: "ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR"});
  const ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL = intl.formatMessage({id: "ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL"});

  const OrderItemsFailed = submitMultiOrderPayment.filter((item:any) => item?.otp && !item?.otp.isSuccess);
  const failedOrderFormIdList = OrderItemsFailed.map((item:any) => item?.OrderFormId);
  const failedOrderItems = accountInputValue
    .filter((x) => failedOrderFormIdList.includes(x.transactionID))
    .map((x) => x.accountNumber);
  const failedOtpItems:any = checkedCurrentBalanceItems?.filter((item) => failedOrderItems.includes(item.Ban));

  const SelectedOtpItems:any = checkedCurrentBalanceItems;
  const SomeOtpItemsFailed = (SelectedOtpItems.length > failedOtpItems.length) ? true : false;
  const AllOtpItemsFailed = (SelectedOtpItems.length === failedOtpItems.length && OrderItemsFailed.length > 1) ? true : false;
  const ALERT_ERROR_OTP_BALANCE = SelectedOtpItems.length === 1 ? intl.formatMessage({id: "ALERT_ERROR_OTP_BALANCE"},{
    balance: SelectedOtpItems[0].DueStr,
  }):"";
  const ALERT_ERROR_OTP_BALANCE_SR= SelectedOtpItems.length === 1 ? intl.formatMessage({id: "ALERT_ERROR_OTP_BALANCE_SR"},{
    balance: SelectedOtpItems[0].DueStr,
  }):"";
  React.useEffect(() => {
    setTimeout(() => {
      if(AllOtpItemsFailed ||SomeOtpItemsFailed){
        setOmnitureOnOneTimePaymentFailure({s_oPYM: "", s_oCCDT: ""});
      }
    },1000);
  },[SomeOtpItemsFailed,AllOtpItemsFailed]);

  return (
    <Alert 
      variant="error"
      className="payment-block payment-border payment-rounded-20 sm:payment-flex payment-px-15 sm:payment-px-30 payment-py-30 sm:payment-pb-45 payment-border-y-1 brui-relative brui-rounded-none sm:payment-rounded-20"
      iconSize="36"
      id="alert-otp-fail"
    >
      <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-15 sm:payment-pt-0">
        <Heading level="h3" variant="xs" className="payment-mb-5 payment-font-sans brui-text-red">
          {SelectedOtpItems.length === 1 &&
                     <>
                       <span aria-hidden="true" dangerouslySetInnerHTML={{ __html: ALERT_ERROR_OTP_BALANCE}}></span>
                       <span className="payment-sr-only">{ALERT_ERROR_OTP_BALANCE_SR}</span>
                     </>
          }

          {SomeOtpItemsFailed &&
                        <span dangerouslySetInnerHTML={{ __html: ALERT_ERROR_HEADING_SOME_BALANCE}}></span>
          }

          {AllOtpItemsFailed &&
                        <span dangerouslySetInnerHTML={{ __html: ALERT_ERROR_OTP_ALL_BALANCE}}></span>
          }
        </Heading>
        <Text elementType="div">
          <span className="payment-text-14 payment-leading-18 payment-text-gray" dangerouslySetInnerHTML={{ __html: ALERT_ERROR_OTP_BALANCE_DESC}}></span>
        </Text>
        {(SomeOtpItemsFailed || AllOtpItemsFailed) &&
                    <div className="!payment-border-none payment-mt-15">
                      <AlertNotificationList 
                        label="" >
                        {failedOtpItems.map((item:any) => (
                          <AlertNotificationListItem
                            label={item.NickName}
                            labelDescription={item.Due}
                            variant="priceList"
                            priceSettings={{language, showZeroDecimalPart: true}}    
                          />
                        ))}
                      </AlertNotificationList>

                      {/* NOTE: this only shows when SOME OTP FAIL and with item/s that opted out  */}
                      {(SomeOtpItemsFailed && notOptedBalanceItems.length > 0) && (
                        <div className="payment-border-t-gray-4 payment-mt-15">
                          <AlertNotificationList 
                            label={(notOptedBalanceItems.length === 1) ? ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR : ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL}
                          >
                            {notOptedBalanceItems.map((x) => (
                              <AlertNotificationListItem
                                label={x.NickName}
                                labelDescription={x.Due}
                                variant="priceList"
                                priceSettings={{language, showZeroDecimalPart: true}}    
                              />
                            ))}
                          </AlertNotificationList>
                        </div>
                      )}
                    </div>
        }
        <Button 
          className="payment-mt-30" 
          variant="primary" 
          size="regular"
          onClick={() => {
            location.href = `${intl.formatMessage({id: "CTA_MAKE_PAYMENT_LINK"})}`;
          }}
        >
          {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_BUTTON_DESC"})}
        </Button>
      </Text>
    </Alert>
  );
};

export const AlertErrorOneTimePayment = injectIntl(AlertErrorOneTimePaymentComponent);
