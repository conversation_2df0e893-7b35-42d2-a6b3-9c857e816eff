export declare class LoaderConfigKeys {
    static get RequireContext(): string;
    static get RegistryPath(): string;
    static get VersionDelimiter(): string;
    static get WidgetBundleName(): string;
    static get StaticWidgetMappings(): string;
}
export interface StaticWidgetMappings {
    [moduleId: string]: String | (() => any) | StaticWidgetDefinition;
}
export interface StaticWidgetDefinition {
    namespace?: string;
    factory?: <T>() => T | Promise<T>;
    url?: string;
}
