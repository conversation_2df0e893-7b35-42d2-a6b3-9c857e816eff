{"version": 3, "file": "isPromise.js", "sourceRoot": "", "sources": ["../../src/util/isPromise.ts"], "names": [], "mappings": ";AAAA,mBAA6B,KAAuB;IAClD,MAAM,CAAC,KAAK,IAAI,OAAa,KAAM,CAAC,SAAS,KAAK,UAAU,IAAI,OAAQ,KAAa,CAAC,IAAI,KAAK,UAAU,CAAC;AAC5G,CAAC;AAFe,iBAAS,YAExB,CAAA", "sourcesContent": ["export function isPromise<T>(value: any | Promise<T>): value is Promise<T> {\n  return value && typeof (<any>value).subscribe !== 'function' && typeof (value as any).then === 'function';\n}\n"]}