import * as React from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ton, Link, Icon, Text, Price} from "@bell/bell-ui-library";
import AlertNotificationListItem from "./AlertNotificationListItem";
import AlertNotificationList from "./AlertNotificationList";
import { injectIntl } from "react-intl";
// import { AccountInputValues, PaymentItem } from "../../models";


// import { Alert, Heading, Button } from "@bell/@bell/bell-ui-library";


const alertErrorFormList = [
  { label: "Account holder name", labelDescription: "This information is required." }, 
  { label: "Transit number", labelDescription: "This information is required." },
  { label: "Bank name", labelDescription: "This information is required." },
  { label: "Account number", labelDescription: "This information is required." },
];


interface AlertNotificationProps {
  intl: any;
  children?: React.ReactNode;

} 

/* Sample Components usecases to use in other alerts*/
/* ERROR ALERTS*/

export const AlertErrorFormList =  injectIntl(({ intl }:AlertNotificationProps) => (
  <Alert 
    variant="error" 
    className="brui-block sm:brui-flex brui-px-0 sm:brui-px-30 payment-py-30 brui-border-x-0 sm:payment-border-x-1 payment-border-y-1 brui-relative brui-rounded-none sm:payment-rounded-20" 
    iconSize="36"
    id="alert-3">
    <Text elementType="div" className="payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-[7px]">
      <Heading level="h2" variant="xs" className=" sm:brui-mt-7 brui-mb-15 brui-font-sans brui-leading-22">
        <span aria-hidden="true">{intl.formatMessage({id: "ALERT_ERROR_HEADING"})}</span>
        <span className="payment-sr-only">{intl.formatMessage({id: "ALERT_ERROR_HEADING_SR"})}</span>
      </Heading>
      <div>
        <AlertNotificationList>
          {alertErrorFormList.map((item) => (
            <AlertNotificationListItem label={item.label} labelDescription={item.labelDescription} variant="errorList"/> 
          ))}
        </AlertNotificationList>
      </div>
    </Text>
  </Alert>
));

export const AlertErrorWithAccountPrice = injectIntl(({children, intl}:AlertNotificationProps) => (
  <Alert
    variant="error" 
    className="brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block" 
    iconSize="36"
    id="alert-2">
    <Text elementType="div" className="brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0 brui-flex-1">
      <Heading level="h2" variant="xs" className="brui-mb-5 brui-font-sans brui-text-red ">        
        {intl.formatMessage({id: "ALERT_ERROR_HEADING_SOME_BALANCE"})}                
      </Heading>
      <div>
        {children}
      </div>
      <Button className="brui-mt-30" variant="primary" size="regular">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_BUTTON_DESC"})}
      </Button>          
    </Text>
  </Alert>
));


export const AlertErrorForm = injectIntl(({children, intl}:AlertNotificationProps) => (
  <Alert 
    variant="error" 
    className="payment-block sm:payment-flex payment-px-0 sm:payment-px-30 payment-py-30 payment-border-x-0 sm:payment-border-x-1 payment-border-y-1 payment-relative payment-rounded-none sm:payment-rounded-20" 
    iconSize="36"
    id="alert-3"
    aria-labelledby="error-alert-1 error-alert-2 error-alert-3">
    <Text elementType="div" className="payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-0">
      <Heading id="error-alert-1" level="h2" variant="xs" className=" sm:payment-mt-7 payment-mb-15 payment-font-sans payment-leading-22">
        <span aria-hidden="true">{intl.formatMessage({id: "ALERT_ERROR_HEADING"})}</span>
        <span className="payment-sr-only">{intl.formatMessage({id: "ALERT_ERROR_HEADING_SR"})}</span>
      </Heading>
      <div>
        {children}
      </div>
    </Text>
  </Alert>
));


export const AlertErrorPACCSucessOTPFail = injectIntl(({intl}:AlertNotificationProps) =>  (
  <Alert
    variant="error" 
    className="brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block" 
    iconSize="36"
    id="alert-2">
    <Text elementType="div" className="brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0">
      <Heading level="h2" variant="xs" className="brui-mb-12 brui-text-red brui-font-sans">
        <strong>Your balance of $195.45 was not paid</strong> due to an error processing your request. 
               
      </Heading>  
      <p className="brui-text-14 brui-my-15 brui-text-gray">
        A separate one-time payment must be made to pay this balance, or risk late fees.
      </p>     
      <Button variant="primary" size="regular">
        Make a Payment
      </Button>
    </Text>
  </Alert>
));


/* INFO ALERTS*/

export const AlertInfoSingleButton = injectIntl(({intl}:AlertNotificationProps) => (
  <Alert 
    variant="info" 
    className="brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block" 
    iconSize="36"
    id="alert-3">
    <Text elementType="div" className="brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0">
      <Heading level="h2" variant="xs" className="brui-mb-12 brui-font-sans brui-font-bold brui-leading-22">               
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_HEADING"})}
      </Heading>
      <p className="brui-text-14 brui-my-15 brui-text-gray">               
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_DESC"})}            
      </p>
      <Text elementType="div" className="sm:brui-flex brui-block">
        <Text elementType="div" className="brui-pr-0 sm:brui-pr-10">
          <Button variant="primary" size="regular">               
            {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_BUTTON_DESC"})}     
          </Button>
        </Text>
      </Text>
    </Text>
  </Alert>
));



/* SUCCESS ALERTS*/


export const AlertSuccessWithCheckIcon = injectIntl(({intl}:AlertNotificationProps) => (
        
  <Alert
    variant="success" 
    className="brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block" 
    iconSize="36"
    id="alert-3">
    <Text elementType="div" className="brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0">
      <Heading level="h2" variant="xs" className="brui-mb-15 brui-font-sans brui-leading-22">             
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_HEADING"})}
      </Heading>
      <Text elementType="div" role="list" className="brui-text-14 brui-mb-30 brui-text-black brui-ml-5">
        <Text elementType="div" role="listitem" className="brui-flex brui-items-start brui-mb-15"> 
          <Icon className="brui-text-20 brui-text-blue" iconClass="bi_check_light" iconName="icon.bi_check_small_flat_fin" />                   
          <span className="brui-text-16 brui-leading-20 brui-ml-10">{intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE"})}</span>
        </Text>
        <Text elementType="div" role="listitem" className="brui-flex brui-items-start brui-mb-15 brui-text-black"> 
          <Icon className="brui-text-20 brui-text-blue" iconClass="bi_check_light" iconName="icon.bi_check_small_flat_fin" /> 
          <span className="brui-text-16 brui-leading-20 brui-ml-10">{intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})}</span>
        </Text>
      </Text>
      <p className="brui-text-14 brui-mb-5 brui-text-gray brui-leading-18">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})} <strong>000011</strong>
      </p>
      <p className="brui-text-14 brui-text-gray brui-leading-18">              
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC"})}
        <Link variant="textBlue" size="small" href="https://www.bell.ca/" className=""> {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})}</Link>
      </p>      
    </Text>
  </Alert>
));

export const AlertSuccessWithCheckIconPreAuth = injectIntl(({intl}:AlertNotificationProps) => (
  <Alert
    variant="success" 
    className="brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block" 
    iconSize="36"
    id="alert-3">
    <Text elementType="div" className="brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0">
      <Heading level="h2" variant="xs" className="brui-mb-15 brui-font-sans brui-leading-22">             
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_HEADING"})}
      </Heading>
      <Text elementType="div" role="list" className="brui-text-14 brui-mb-30 brui-text-black brui-ml-5">
        <Text elementType="div" role="listitem" className="brui-flex brui-items-start brui-mb-15"> 
          <Icon className="brui-text-20 brui-text-blue" iconClass="bi_check_light" iconName="icon.bi_check_small_flat_fin" />                   
          <span className="brui-text-16 brui-leading-20 brui-ml-10">{intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE"})}</span>
        </Text>
      </Text>
      <p className="brui-text-14 brui-mb-5 brui-text-gray brui-leading-18">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})} <strong>000011</strong>
      </p>
      <p className="brui-text-14 brui-text-gray brui-leading-18">              
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC"})}
        <Link variant="textBlue" size="small" href="https://www.bell.ca/" className=""> {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})}</Link>
      </p>      
    </Text>
  </Alert>
));

export const AlertSuccessWithCheckIconOptedIn = injectIntl(({intl}:AlertNotificationProps) => (
  <Alert
    variant="success" 
    className="brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block" 
    iconSize="36"
    id="alert-3">
    <Text elementType="div" className="brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0">
      <Heading level="h2" variant="xs" className="brui-mb-15 brui-font-sans brui-leading-22">             
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_HEADING"})}
      </Heading>
      <Text elementType="div" role="list" className="brui-text-14 brui-mb-30 brui-text-black brui-ml-5">
        <Text elementType="div" role="listitem" className="brui-flex brui-items-start brui-mb-15"> 
          <Icon className="brui-text-12 brui-text-blue brui-mt-3" iconClass="bi_check_light" iconName="bi_check_light" /> 
          <span className="brui-text-16 brui-leading-20 brui-ml-10">{intl.formatMessage({id: "ALERT_CONFIRMATION_CURRENT_BALANCE"})}
          </span>
        </Text>
        <Text elementType="div" role="listitem" className="brui-flex brui-items-start brui-mb-15"> 
          <Icon className="brui-text-20 brui-text-blue" iconClass="bi_check_light" iconName="icon.bi_check_small_flat_fin" />                   
          <span className="brui-text-16 brui-leading-20 brui-ml-10">{intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE"})}</span>
        </Text>
      </Text>
      <p className="brui-text-14 brui-mb-5 brui-text-gray brui-leading-18">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})} <strong>000011</strong>
      </p>
      <p className="brui-text-14 brui-text-gray brui-leading-18">              
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC"})}
        <Link variant="textBlue" size="small" href="https://www.bell.ca/" className=""> {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})}</Link>
      </p>         
    </Text>
  </Alert>
));

export const AlertSuccessWithCheckIconCreditIncreased = injectIntl(({intl}:AlertNotificationProps) => (
  <Alert
    variant="success" 
    className="brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block" 
    iconSize="36"
    id="alert-3">
    <Text elementType="div" className="brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0">
      <Heading level="h2" variant="xs" className="brui-mb-15 brui-font-sans brui-leading-22">             
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_HEADING"})}
      </Heading>
      <Text elementType="div" role="list" className="brui-text-14 brui-mb-30 brui-text-black brui-ml-5">
        <Text elementType="div" role="listitem" className="brui-flex brui-items-start brui-mb-15"> 
          <Icon className="brui-text-20 brui-text-blue" iconClass="bi_check_light" iconName="icon.bi_check_small_flat_fin" />                   
          <span className="brui-text-16 brui-leading-20 brui-ml-10">{intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE"})}</span>
        </Text>
        <Text elementType="div" role="listitem" className="brui-flex brui-items-start brui-mb-15 brui-text-black"> 
          <Icon className="brui-text-20 brui-text-blue" iconClass="bi_check_light" iconName="icon.bi_check_small_flat_fin" /> 
          <span className="brui-text-16 brui-leading-20 brui-ml-10">{intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})}</span>
        </Text>
      </Text>
      <p className="brui-text-14 brui-mb-5 brui-text-gray brui-leading-18">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})} <strong>000011</strong>
      </p>
      <p className="brui-text-14 brui-text-gray brui-leading-18">              
        {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC"})}
        <Link variant="textBlue" size="small" href="https://www.bell.ca/" className=""> {intl.formatMessage({id: "ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})}</Link>
      </p>      
    </Text>
  </Alert>
));


/* WARNING ALERTS*/


export const AlertWarningWithAccountPrice = injectIntl(({intl,children}:AlertNotificationProps) =>  (
  <Alert
    variant="warning" 
    className="brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block" 
    iconSize="36">
    <Text elementType="div" className="brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0">
      <Heading level="h2" variant="xs" className="brui-mb-5 brui-font-sans brui-font-bold">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_HEADING"})}
      </Heading>
      {/* <p className="brui-text-14 brui-mb-10 brui-text-gray">
                Pre-authorized payments will only begin on your next billing period. You must pay the following balance(s) in a separate and final one-time payment, or risk late fees:
                </p> */}
      <div>{children}</div>   
      <Button className="brui-mt-30" variant="primary" size="regular">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_BUTTON_DESC"})}  
      </Button>
    </Text>
  </Alert>
));


export const AlertWarningWithPrice = injectIntl(({intl}:AlertNotificationProps) =>  (
  <Alert
    variant="warning" 
    className="brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block" 
    iconSize="36">
    <Text elementType="div" className="brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0">
      <Heading level="h2" variant="xs" className="brui-mb-0 sm:brui-mb-12 brui-font-sans brui-font-bold brui-leading-22">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_HEADING"})}
      </Heading>
      <p className="brui-leading-18 brui-text-14 brui-mt-5 brui-mb-15 sm:brui-mt-0 sm:brui-mb-0 sm:brui-my-15 brui-text-gray">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_DESC_1"})} 
        <Price 
          language="en"
          showZeroDecimalPart
          price={195.45}
          variant="defaultPrice"
          className="!brui-text-14 brui-leading-14 brui-m-5 brui-font-normal brui-inline-block"/>
        <span className="brui-sr-only">195 point 45 dollars</span> {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_DESC_2"})}  
      </p>
      <Text elementType="div" className="sm:brui-flex brui-block">
        <Text elementType="div" className="brui-pr-0 sm:brui-pr-10">
          <Button variant="primary" size="regular">
            {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_BUTTON_DESC"})}   
          </Button>
        </Text>
      </Text>
    </Text>
  </Alert>
));


export const AlertWarningWithSomeBalancesPaid = injectIntl(({intl}:AlertNotificationProps) =>  (
  <Alert
    variant="warning" 
    className="payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-20 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block" 
    iconSize="36">
    <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0">
      <Heading level="h2" variant="xs" className="payment-mb-15 payment-font-sans payment-font-bold">
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_HEADING"})}
      </Heading>
      <p className="payment-text-14 payment-mb-10 payment-text-gray">                
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_DESC"})}              
      </p>
      <Text elementType="div" className="payment-mb-30 payment-mt-15">
        <Text elementType="div" className="payment-flex payment-justify-between sm:payment-justify-normal">
          <label className="payment-text-14 sm:payment-basis-1/4">
            <strong>1234567890</strong>
          </label>
          <Price 
            language="en"
            showZeroDecimalPart
            price={206.98}
            variant="defaultPrice"
            className="!payment-text-14 payment-leading-14 payment-m-5 payment-font-normal"/>
        </Text>      
      </Text>       
      <Button variant="primary" size="regular">               
        {intl.formatMessage({id: "ALERT_CONFIRMATION_INFO_BUTTON_DESC"})}     
      </Button>
    </Text>
  </Alert>
));

//   interface AlertNotificationMultiProps {
//     intl: any;
//     multiban: boolean;
//     submitMultiOrderPayment: any;
//     accountInputValue: AccountInputValues[];
//     paymentItem: PaymentItem[];

// } 

// export const AlertWarningWithSomeBalancesPaidMulti = injectIntl(({
//     intl,
//     multiban,
//     submitMultiOrderPayment,
//     accountInputValue, 
//     paymentItem,
// }:AlertNotificationMultiProps) =>  {
    
//     let amountDue = 0.00;
//     if(!multiban){
//         const accountNumber = accountInputValue
//             .filter((x) => x.transactionID === submitMultiOrderPayment[0]?.OrderFormId)
//             .map((x) => x.accountNumber)[0]; 

//         if (accountNumber) {
//             const amountDueItem = paymentItem
//             .filter((x) => x.Ban === accountNumber)
//             .map((x) => x.DueStr)[0]; 

//             if (amountDueItem) {
//                 amountDue = Number(amountDueItem);
//             }
//         }
//     }
//     return (
//         <Alert
//         variant="warning" 
//         className="payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-20 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block" 
//         iconSize="36">
//             <Text elementType="div" className="payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0">
//                 { multiban &&(
//                     <>
//                     <Heading level="h2" variant="xs" className="payment-mb-15 payment-font-sans payment-font-bold">
//                         {intl.formatMessage({id:"ALERT_CONFIRMATION_OPTED_SOME_BALANCE_MULTI"})}
//                     </Heading>
//                     <p className="payment-text-14 payment-mb-10 payment-text-gray">                            
//                         {intl.formatMessage({id:"ALERT_CONFIRMATION_OPTED_SOME_BALANCES_DESC_MULTI"})}              
//                     </p>
//                     <Text elementType="div" className="payment-mb-30 payment-mt-15">
//                         {
                        
//                             submitMultiOrderPayment.map((item: any, index: any) => {
//                                 // Define the variable to store the filtered account number
//                                 const accountNumber = accountInputValue
//                                     .filter((x) => x.transactionID === item.OrderFormId && x.payBalanceAmnt === 0)
//                                     .map((x) => x.accountNumber)[0];

//                                 const amountDue = paymentItem
//                                     .filter((x) => x.Ban === accountNumber)
//                                     .map((x) => x.AmountStr)[0];
                                
                            
//                                 return (
//                                     <>
//                                         {(
//                                             <Text elementType="div" className="payment-flex payment-justify-between sm:payment-justify-normal">
//                                             <label className="payment-text-14 sm:payment-basis-1/4">
//                                                 <strong>{accountNumber}</strong>
//                                             </label>
//                                             <Price 
//                                                 language="en"
//                                                 showZeroDecimalPart
//                                                 price={Number(amountDue)}
//                                                 variant="defaultPrice"
//                                                 className="!payment-text-14 payment-leading-14 payment-m-5 payment-font-normal"/>
//                                             </Text>     
//                                         )}
//                                     </>
//                                 );
//                             })
//                         }   
//                     </Text>   
//                     </>
//                 )}
//                 {!multiban && (
//                     <>
//                         <Heading level="h2" variant="xs" className="payment-mb-15 payment-font-sans payment-font-bold">
//                         {intl.formatMessage({ id: "ALERT_CONFIRMATION_OPTED_SOME_BALANCE_SINGLE" })}
//                         </Heading>

//                         <p className="payment-text-14 payment-mb-10 payment-text-gray">
//                         {intl.formatMessage({ id: "ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_1" })}
//                         <Price
//                             language="en"
//                             price={Number(amountDue)} // Now amountDue is available in the outer scope
//                             variant="defaultPrice"
//                             className="!payment-text-14 payment-leading-14 brui-m-5 payment-font-normal brui-inline-block"
//                         />
//                         {intl.formatMessage({ id: "ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_2" })}
//                         </p>
//                     </>
//                   )}
//                 <Button 
//                     variant="primary" 
//                     size="regular"
//                     onClick={() => {
//                             location.href = `${intl.formatMessage({id:"CTA_MAKE_PAYMENT_LINK"})}`;
//                         }}
//                 >               
//                     {intl.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"})}     
//                 </Button>
//             </Text>
//         </Alert>
//   );
// })
