/*! bell-preauth-setup (widget) 1.0.0 | bwtk 6.1.0 | 2025-06-25T22:50:22.268Z */
!function(e,t){var n,r;if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("react"),require("react-redux"),require("bwtk"),require("redux"),require("redux-actions"),require("redux-observable"),require("rxjs"),require("react-intl"),require("react-dom"));else if("function"==typeof define&&define.amd)define(["react","react-redux","bwtk","redux","redux-actions","redux-observable","rxjs","react-intl","react-dom"],t);else for(r in n="object"==typeof exports?t(require("react"),require("react-redux"),require("bwtk"),require("redux"),require("redux-actions"),require("redux-observable"),require("rxjs"),require("react-intl"),require("react-dom")):t(e.<PERSON><PERSON>,e.<PERSON>actRedux,e.bwtk,e.Redux,e.ReduxActions,e.ReduxObservable,e.rxjs,e.ReactIntl,e.ReactDOM))("object"==typeof exports?exports:e)[r]=n[r]}(self,function(e,t,n,r,a,i,o,l,u){return function(){function s(e){var t,n=d[e];return void 0!==n?n.exports:(t=d[e]={exports:{}},m[e].call(t.exports,t,t.exports,s),t.exports)}var c,m=[,function(t){"use strict";t.exports=e},function(e){"use strict";e.exports=t},function(e){"use strict";e.exports=n},function(e){"use strict";e.exports=r},function(e){"use strict";e.exports=a},function(e){"use strict";e.exports=JSON.parse('{"en":{"ALERT_CONFIRMATION_INFO_HEADING":"Avoid late fees by paying your current balance.","ALERT_CONFIRMATION_INFO_DESC":"Pre-authorized payments will only begin on your next billing period. Any existing balance on your account must be paid separately with a final one-time payment, or risk late fees.","ALERT_CONFIRMATION_INFO_BUTTON_DESC":"Make a payment","ALERT_CONFIRMATION_INFO_DESC_1":"Pre-authorized payments will only begin on your next billing period. You must pay your ","ALERT_CONFIRMATION_INFO_DESC_2":"balance in a separate and final one-time payment, or risk late fees. ","EXISTING_CC_TITLE":"Select a credit Card","AUTOPAY_TRANSACTION_NOTE_DESC":"If you have any upcoming changes to your account, they may impact the Autopay credits above. Any changes to the credits will take effect within 1 to 2 billing periods.","AUTOPAY_TRANSACTION_NOTE_HEADING":"Note:","EXISTING_BANK_TITLE":"Select a bank account","EXISTING_BANK_TITLE_SR":"Requried, Select a bank account","EXISTING_BANK_ACCOUNT_MANUAL_DETAILS_LABEL":"Choose a bank account already on file or add a new one manually","SELECT_BILLS_HEADING":"Select bills","SELECT_BILLS_HEADING_SINGULAR":"Select bill","SELECT_BILLS_ACCOUNT_TITLE":"{accounttype} account number","SELECT_BILLS_HEADING_DESC":"For which bill(s) do you want to update the payment information?","SELECT_BANK_TITLE":"Select a bank account","ACCOUNT_TYPE_ONE_BILL":"One Bill","ACCOUNT_TYPE_MYBILL":"One Bill","ACCOUNT_TYPENAME_ONEBILL":"One Bill","ACCOUNT_TYPENAME_MYBILL":"My bill","ACCOUNT_TYPENAME_MOBILITY":"Mobility","ACCOUNT_TYPENAME_TV":"TV","ACCOUNT_TYPENAME_INTERNET":"Internet","ACCOUNT_TYPENAME_HOMEPHONE":"Home Phone","ACCOUNT_TYPENAME_MOBILITY_ONEBILL":"Mobility and One Bill","ACCOUNT_TYPENAME_SINGLEBAN":"Single Ban","ACCOUNT_BALANCE":"Your current balance is","CHECKBOX_BALANCE":"${balance}","CHECKBOX_BALANCE_SR":"Your current balance is {balance} dollars","BACK_TO_MY_BELL":"Back to MyBell","CTA_NEXT":"Next","CTA_EDIT":"Edit","CTA_CLOSE":"Close dialog box","CTA_EXPAND_TERMS":"Expand terms of service","CTA_CONFIRM":"Confirm and submit","CTA_CANCEL":"Cancel","CTA_COLLAPSE_TERMS":"Collapse terms of service","CTA_INTERAC":"Sign in with Interac®","CTA_INTERAC_SR":"Sign in with Interac registered trademark verification service","CREDIT_CARD_LABEL":"Credit card","CREDIT_CARD_TYPE_LABEL":"Card type","CREDIT_CARD_NAME_LABEL":"Cardholder name","CREDIT_CARD_NAME_DESC_INPUT_LABEL":"As it appears on the card","CREDIT_CARD_NUMBER_LABEL":"Credit card number","CREDIT_CARD_NUMBER_DESC_INPUT_LABEL":"15 or 16 digits","CREDIT_CARD_EXPIRY_LABEL":"Expiration date","CREDIT_CARD_EXPIRY_SR_MONTH_LABEL":"Required, Expiration date month","CREDIT_CARD_EXPIRY_SR_YEAR_LABEL":"Required, Expiration date year","CREDIT_CARD_SECURITY_CODE_INPUT_LABEL":"Card security code","CREDIT_CARD_NAME_SR_LABEL":"Required, Cardholder name","CREDIT_CARD_NUMBER_SR_LABEL":"Required, Credit card number","CREDIT_CARD_EXPIRY_DATE_LABEL":"Required, Expiration date","CREDIT_CARD_EXPIRY_DATE_SR_LABEL":" {month} / {year}","CREDIT_CARD_MONTH_TEXT":"month","CREDIT_CARD_YEAR_TEXT":"year","CREDIT_CARD_SECURITY_CODE_INPUT_SR_LABEL":"Required, Card security code","ERROR_CREDIT_CARD_NAME_INPUT_LABEL":"Enter a valid cardholder name","ERROR_CREDIT_CARD_NUMBER_INPUT_LABEL":"Enter a valid credit card number","ERROR_CREDIT_CARD_EXPIRY_INPUT_LABEL":"Enter a valid expiration date","ERROR_CREDIT_CARD_SECURITY_CODE_INPUT_LABEL":"Enter a valid card security code","CREDIT_CARD_VALID":"Valid Until","CREDIT_CARD_MONTH_PLACEHOLDER":"MM","CREDIT_CARD_YEAR_PLACEHOLDER":"YY","REQUIRED_LABEL":"*Required information","MODAL_NO_NAME":"No name on card?","MODAL_SECURITY_CODE":"What is a card security code?","MODAL_NO_NAME_TITLE":"No name on the credit card?","MODAL_NO_NAME_DESC":"If you’re using a prepaid credit card that doesn’t have a cardholder name, enter the name of the Bell account holder instead.","MODAL_SECURITY_CODE_TITLE":"What is a card security code?","MODAL_SECURITY_CODE_DESC":"The card security code (CSC) is a fraud prevention feature used to verify that the credit card is in your possession. It’s sometimes called a card verification code (CVC) or card verification value (CVV). See where to find your security code below:","CARD_TYPE_VISA_MASTERCARD":"Visa and Mastercard","CARD_TYPE_VISA_MASTERCARD_DESC":"A 3-digit number on the back of your credit card.","CARD_TYPE_AMERICAN_EXP":"American Express","CARD_TYPE_AMERICAN_EXP_DESC":"A 4-digit number on the front of your credit card.","MODAL_TRANSIT_ACCOUNT_NUMBERS_TITLE":"Find your transit and account numbers","MODAL_BANK_STATEMENT_TITLE":"Look at a bank statement or cheque","MODAL_BANK_STATEMENT_DESC":"You can find your bank account details on a bank statement or cheque. You can also find your account information in your banking app or online account.","MODAL_TRANSIT_NUMBER_DESC":"The <strong>transit number</strong> is 5 digits.","MODAL_ACCOUNT_NUMBER_DESC":"Your <strong>account number</strong> is 7 to 12 digits.","ALERT_ERROR_HEADING":"An error occurred while processing your request.","ALERT_ERROR_HEADING_SR":"Warning, An error occured while processing your request.","ALERT_ERROR_INFO_REQUIRED":"- This information is required.","ALERT_ERROR_SELECT_BILL_INFO_REQUIRED":"Select a bill – This information is required.","ALERT_ERROR_SELECT_BILL_INFO":"Select a bill","ALERT_ERROR_SELECT_BILL_DESC":"This information is required.","ALERT_ERROR_ONE_SELECT_BILL":"Select at least one bill","ALERT_ERROR_HEADING_SOME_BALANCE":"<strong>Some of your current balances</strong> were not paid due to an error processing your request.","ALERT_ERROR_OTP_ALL_BALANCE":"<strong>Your current balance(s) were not paid</strong> due to an error processing your request.","ALERT_ERROR_OTP_BALANCE":"<strong>Your balance of ${balance} was not paid.</strong>","ALERT_ERROR_OTP_BALANCE_SR":"Your balance of {balance} dollars was not paid","ALERT_ERROR_OTP_BALANCE_DESC":"There was an error processing your one-time payment. Please pay your current account balance to avoid late payment charges.","ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR":"Though you opted not to pay the following balance, they will also require a separate payment:","ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL":"Though you opted not to pay the following balance(s), they will also require a separate payment:","SELECT_PAYMENT_METHOD_HEADING":"Choose a payment method","PAY_CURRENT_BALANCE_HEADING":"Pay your current balance","PAY_CURRENT_BALANCE_DESC":"Pre-authorized payments will  <strong class=\'brui-text-black\'>begin on your next bill.</strong> You can pay your current account balance separately with a one-time payment to avoid late payment charges.","PAY_CURRENT_BALANCE_OPTED_IN":"Your one-time payment will be processed in <strong>3 to 5 business days</strong>. After that, you’ll have a balance of {balance} on your account.","PAY_CURRENT_BALANCE_OPTED_IN_PACC":"Your one-time payment will be processed <strong>as soon as you submit this transaction</strong>.","PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR":"<p>There’s an outstanding balance on your account.</p><p class=\'brui-mt-10\'>Pre-authorized payments will begin on your next bill. You can pay your current account balance separately with a one-time payment to avoid late payment charges.</p>","PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL":"<p>There are outstanding balances on your accounts.</p><p class=\'brui-mt-10\'>Pre-authorized payments will begin on your next bill. You can pay your current account balances separately with a one-time payment to avoid late payment charges.</p>","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_IN":"Your one-time payment will be processed in <strong>3 to 5 business days</strong>. After that, you’ll have a balance of {balance} on your account.","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_SINGULAR":"You chose not to pay your current account balance.","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_PLURAL":"You chose not to pay your current account balances.","PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_SINGULAR":"There’s an outstanding balance on your other account.","PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_PLURAL":"There are outstanding balances on your other accounts.","PAYMENT_AMOUNT":"Payment amount","UNPAID_BALANCE":"Unpaid balance","PAY_MY_BALANCE":"Pay my balance of","PAY_MY_BALANCE_SR":"Pay my balance of {balance} dollars","PAY_CURRENT_BALANCE_NOTE_1":"We’ll use the bank account information provided above to make the payment.","PAY_CURRENT_BALANCE_NOTE_2":"Note: If you made a payment in the last 3 to 5 business days, your balance owing may not be updated here yet.","TERMS_AND_CONDITION_HEADING":"Pre-authorized Payment Authorization","TERMS_AND_CONDITION_DISCLAIMER":"By clicking confirm and submit, I am confirming that I have read and agree to the Bell Terms of Service and the pricing details of my selected service(s).","TRANSACTION_SUBMITTED_HEADING":"Transaction submitted","CONFIRMATION_HEADING":"Confirmation","ALERT_CONFIRMATION_SUCCESS_HEADING":"Here’s what’s happening:","ALERT_CONFIRMATION_SUCCESS_MESSAGE":"Pre-authorized payments have been updated and will begin on your <strong>next bill.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_V2":"Pre-authorized payments have been updated and will begin on your <strong>next billing cycle.</strong>","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC":"Your one-time payment for <strong> {account} </strong> has been processed.","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD":"Your one-time payment for <strong> {account} </strong> will be processed in <strong>3 to 5 business days.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS":"Your Autopay credit will start within 1 to 2 billing periods.","ALERT_CONFIRMATION_SUCCESS_NUMBER":"Confirmation number:","ALERT_CONFIRMATION_SUCCESS_DESC":"We’ve sent a confirmation to {email}. If this isn’t your correct email address, please","ALERT_CONFIRMATION_SUCCESS_DESC_LINK":"update your profile","ALERT_CONFIRMATION_SUCCESS_CONFIRM_NO":"Confirmation Number:","ALERT_CONFIRMATION_CURRENT_BALANCE":"The current balance for {account} has been paid.","PAYMENT_SUMMARY_TITLE":"Payment summary","PAYMENT_INFORMATION_TITLE":"Payment information","BILL_INFORMATION_TITLE":"Bill information","BILL_INFORMATION_ACCOUNT_NUMBER":"Account","PAYMENT_METHOD_TITLE":"Payment method","CURRENT_BALANCE_TITLE":"Current balance","SUMMARY_CURRENT_BAL_OTP_FAILURE":"Payment for your current balance was not processed. Please make a separate one-time payment.","TERMS_AND_CON_TITLE":"Pre-authorized Payment Authorization","TERMS_AND_CON_DESC_1":"You authorize us, Bell, to set up pre-authorized payment using the payment information you provided, as follows","TERMS_AND_CON_DESC_LIST_1":"For <strong>monthly payments:</strong>","TERMS_AND_CON_DESC_LIST_1_ITEM1":"We will debit your bank account or charge your credit card the total amount due, on the same date or close to that date each month (for example, the date may be different if it falls on a Sunday or a holiday);","TERMS_AND_CON_DESC_LIST_1_ITEM2":"You will be notified at least 10 days in advance on your bill of the amount due, which may vary due to charges you incurred;","TERMS_AND_CON_DESC_LIST_1_ITEM3":"For services for which a bill is not provided, you waive the requirement to be notified of the amount before the payment when the amount remains the same or is as previously agreed.","TERMS_AND_CON_DESC_LIST_2":"To <strong>add funds</strong> to your account for prepaid services:","TERMS_AND_CON_DESC_LIST_2_ITEM1":"We will debit your bank account or charge your credit card the amount set according to the criteria you selected;","TERMS_AND_CON_DESC_LIST_2_ITEM2":"<strong>You waive the requirement to be notified of the amount before the payment when the amount is the same as you selected or lower.</strong>","TERMS_AND_CON_DESC_2":"For <strong>other types of payments</strong>, we will obtain your authorization before the payment. In some circumstances, we may also use your pre-authorized payment method to make refunds.","TERMS_AND_CON_DESC_3":"If any payment is not compliant, you may have certain rights such as requesting its refund. For more information or to cancel this authorization, call us or go to MyBell (mybell.ca). When you cancel your authorization, you must notify us at least 30 days before the next pre-authorized payment date. For pre-authorized debits made with a bank account, to obtain a sample cancellation form, or for more information on your right to cancel this authorization, contact your financial institution or <a class=\\"focus-visible:payment-outline-blue focus-visible:payment-outline focus-visible:payment-outline-2 focus-visible:payment-outline-offset-3 focus-visible:payment-rounded-6 payment-underline-offset-2 payment-text-14 payment-text-blue payment-underline payment-leading-18 hover:payment-text-blue-1 hover:payment-no-underline\\" href=\\"#\\">visit payments.ca.</a>","TERMS_AND_CON_DESC_4":"Bell ","TERMS_AND_CON_ZIP_CODE":"P.O Box 9000","TERMS_AND_CON_REGION":"North York, Ontario M3C 2X7","TERMS_AND_CON_TEL":"**************","TERMS_AND_CONDITION_HEADING_QC":"Terms and conditions","TERMS_AND_CON_TITLE_QC":"Autorisation pour prélèvement automatique","TERMS_AND_CON_DESC_1_QC":"Vous nous autorisez, Bell, à mettre en place le paiement par prélèvement automatique en utilisant les renseignements de paiement que vous avez fournis, comme suit.","TERMS_AND_CON_DESC_LIST_1_QC":"Pour les paiements mensuels:","TERMS_AND_CON_DESC_LIST_1_ITEM1_QC":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit le montant total dû, à la même date ou à une date près de cette date chaque mois (par exemple, la date peut être différente si elle tombe un dimanche ou un jour férié);","TERMS_AND_CON_DESC_LIST_1_ITEM2_QC":"Vous serez avisé au moins 10 jours à l’avance sur votre facture du montant dû, qui peut varier en fonction des frais que vous avez engagés;","TERMS_AND_CON_DESC_LIST_1_ITEM3_QC":"Pour les services pour lesquels aucune facture n’est fournie, vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant demeure le même ou correspond à celui convenu précédemment.","TERMS_AND_CON_DESC_LIST_2_QC":"Pour <strong>ajouter des fonds</strong> à votre compte pour les services prépayés :","TERMS_AND_CON_DESC_LIST_2_ITEM1_QC":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit, le montant établi selon les critères que vous avez sélectionnés;","TERMS_AND_CON_DESC_LIST_2_ITEM2_QC":"<strong>Vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant est le même que celui que vous avez sélectionné ou inférieur.</strong>","TERMS_AND_CON_DESC_2_QC":"Pour les <strong>autres types de paiements</strong>,  nous obtiendrons votre autorisation avant le prélèvement. Dans certaines circonstances, nous pouvons également utiliser votre mode de paiement par prélèvement automatique pour effectuer des remboursements.","TERMS_AND_CON_DESC_3_QC":"Si un prélèvement n’est pas conforme à l’autorisation, vous pouvez avoir certains droits, comme demander son remboursement. Pour en savoir plus ou pour annuler cette autorisation, appelez-nous ou rendez-vous sur MonBell (monbell.ca). Lorsque vous annulez votre autorisation, vous devez nous en aviser au moins 30 jours avant la date du prochain prélèvement. Pour les prélèvements automatiques effectués avec un compte bancaire, pour obtenir un exemple de formulaire d’annulation ou pour en savoir plus sur votre droit d’annuler cette autorisation, communiquez avec votre institution financière ou visitez paiements.ca.","TERMS_AND_CON_DESC_4_QC":"Bell Canada ","TERMS_AND_CON_ZIP_CODE_QC":"C.P. 8712, succ. Centre-Ville","TERMS_AND_CON_REGION_QC":"Montréal (Québec) H3C 3P6","TERMS_AND_CON_TEL_QC":"Tél.: **************","BANK_ACCOUNT_LABEL":"Bank Account","TRANSIT_ACC_NO_PNG":"/Styles/BRF3/content/img/transit-account-number.png","INTERACT_VERIFICATION_V2_PNG":"/Styles/BRF3/content/img/interac-logos/interac-verification-v2-en.png","MASTER_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/mastercard.png","VISA_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/visa.png","AMEX_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/amex.png","FRONT_CC_PNG":"/Styles/BRF3/content/img/Front.png","BACK_CC_PNG":"/Styles/BRF3/content/img/Back.png","SELECT_BILLS_CC_DESC":"{CreditCardType} ending in {CCFourDigits}, expiring {ExpiryDate}","NEW_CREDIT_ACCOUNT_LABEL":"New credit account","SELECT_BILLS_BANK_DESC":"{BankName} account {BankMaskedDigits}","ALERT_GREAT_NEWS":"Great news","ALERT_GREAT_NEWS_DESC":" - by setting up pre-authorized debit payments, the following services will be eligible for an Autopay credit:","ALERT_GREAT_NEWS_DESC_1":" - you’ll receive an Autopay credit.","ALERT_GREAT_NEWS_NOTE":"Note: ","ALERT_GREAT_NEWS_NOTE_DESC":"If you have any upcoming changes to your account, they may impact the Autopay credit above. Any changes to the credit will take effect within 1 to 2 billing periods.","BANK_ACCOUNT_AUTOMATIC_LABEL":"Automatically retrieve account details with Interac® verification service","BANK_ACCOUNT_AUTOMATIC_DESCRIPTION":"Simply use the Interac® verification service to securely sign in to your bank’s website. Available for the following banks:","BANK_ACCOUNT_MANUAL_DETAILS_LABEL":"Enter your bank account details manually","BANK_ACCOUNT_MANUAL_DETAILS_DESCRIPTION":"Bell accepts payments from most banks and credit unions in Canada.","BANK_NAME_LABEL":"Bank","BANK_HOLDER_NAME_LABEL":"Account holder name","BANK_TRANSIT_NUMBER_LABEL":"Transit number","BANK_TRANSIT_NUMBER_DESCRIPTION":"5 digits","BANK_ACCOUNT_NUMBER_LABEL":"Account number","BANK_ACCOUNT_NUMBER_DESCRIPTION":"7 to 12 digits","BANK_ACCOUNT_FETCHED_LABEL":"Account number filled automatically Please confirm this is the correct account.","BANK_NEED_HELP":"Need help?","BANK_NEW_BANK_ACCOUNT_LABEL":"New bank account","SELECT_REQUIRED_LEGEND":"Required","CC_IMAGE_SR_LABEL":"Accepted credit cards: MasterCard Visa AMEX","LOAD_MORE":"Load More","PAYMENT_METHOD":"Payment method","ACCOUNT_HOLDER":"Account holder name ","BANK_NAME":"Bank ","TRANSIT_NUMER":"Transit number ","ACCOUNT_NUMBER":"Account number ","BANK_HOLDER_NAME_ERROR_LABEL":"Enter a valid name","BANK_NAME_ERROR_LABEL":"Select a bank","BANK_TRANSIT_ERROR_LABEL":"Enter a valid, 5-digit transit number","BANK_ACCOUNT_HOLDER_NUMBER_ERROR_LABEL":"Enter a valid bank account number of 7 to 12 digits","ALERT_ERROR_GENERAL_DESC":"This information is required.","PAGE_TITLE_CONFIRMATON":"Confirmation: Set up pre-authorized payments document","INTERAC_BOX_LOGO":"/Styles/BRF3/content/img/interac-logos/Interac_box_logo.png","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_SINGLE":"Avoid late payment charges by paying your current account balance.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_1":"Pre-authorized payments will begin on your next bill. You can pay your current account balance of ","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_2":" separately with a one-time payment to avoid late payment charges.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_MULTI":"Avoid late payment charges by paying your current account balance.","ALERT_CONFIRMATION_OPTED_SOME_BALANCES_MULTI":"Avoid late payment charges by paying your current account balances.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_MULTI":"Pre-authorized payments will begin on your next bill. You can pay your current account balance separately with a one-time payment to avoid late payment charges.","ALERT_CONFIRMATION_OPTED_SOME_BALANCES_DESC_MULTI":"Pre-authorized payments will begin on your next bill. You can pay your current account balances separately with a one-time payment to avoid late payment charges.","CTA_MAKE_PAYMENT_LINK":"/Payment/MakePayment","LOADER":"Loading data. Please wait…","INTERAC_FETCHED_LABEL":"Account number filled automatically","INTERAC_FETCHED_SUBTITLE":"Please confirm this is the correct account.","ALERT_ERROR_HEADING_INTERAC":"We experienced a technical issue","ALERT_ERROR_HEADING_INTERAC_SR":"Error: We experienced a technical issue","ALERT_ERROR_HEADING_INTERAC_DESC":"Sorry, we were unable to process your request due to a technical issue. You can enter your bank account details manually instead.","FAILURE_API_BAN_HEADING":"Something went wrong","FAILURE_API_BAN_HEADING_SR":"Error, Something went wrong","FAILURE_API_BAN_MAIN_DESC":"Sorry, we were unable to process your request.","FAILURE_API_BAN_MAIN_DESC_2":"Please try again.","FAILURE_API_BAN_SUB_DESC":"If the issue persists:","FAILURE_API_BAN_SUB_DESC_LISTITEM_1":"Use a different credit card or set up pre-authorized debit payments instead.","FAILURE_API_BAN_SUB_DESC_LISTITEM_2":"Wait a little while and try again later.","FAILURE_API_BAN_BUTTON":"Try again","LOADER_SUBMIT":"Submitting payment information","LOADER_SUBMIT_DESC":"Please wait","LABEL_LOADED_OFFERS_DEBIT_TITLE":"-by setting up pre-authorized debit payments, the following services will be eligible for an Autopay credit:","LABEL_LOADED_OFFER_DEBIT_TITLE":"-by setting up pre-authorized debit payments, the following service will be eligible for an Autopay credit:","LABEL_LOADED_OFFERS_TITLE_TRUEAUTOPAY":"-by setting up pre-authorized payments, the following services will be eligible for an Autopay credit","LABEL_LOADED_OFFER_TITLE_TRUEAUTOPAY":"-by setting up pre-authorized payments, the following service will be eligible for an Autopay credit","GREAT_NEWS":"Great news","MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING":"Note:","MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE":"If you have any upcoming changes to your account, they may impact the Autopay credits above. Any changes to the credits will take effect within 1 to 2 billing periods.","MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE":"If you have any upcoming changes to your account, they may impact the Autopay credit above. Any changes to the credit will take effect within 1 to 2 billing periods.","SORRY_MESSAGE":"Sorry, the Autopay credit isn’t available for this payment method.","SORRY_MESSAGE_CREDIT":"Sorry, the Autopay credit isn’t available for this payment method. To receive the credit, set up pre-authorized debit payments instead.","LABEL_LOADED_OFFERS_CREDIT_TITLE":"– by setting up pre-authorized credit card payments, the following services will be eligible for an Autopay credit:","LABEL_LOADED_OFFER_CREDIT_TITLE":"– by setting up pre-authorized credit card payments, the following service will be eligible for an Autopay credit:","REVIEW_PAGE_AUTOPAY_CREDIT":"Great news – you’ll receive an Autopay credit.","AUTOPAY_ALERT":"Your Autopay credit will start within 1 to 2 billing periods.","AUTOPAY_CREDITS_TITLE":"Autopay credits","InteracSupportedFinancialInstitutions":"BMO,CIBC,Desjardins,RBC,Scotiabank,TD","BANK_ACCOUNT_SR_TEXT":"Account number ending in {Account}","CREDIT_CARD_SR_TEXT":"Card number ending in {Account}","PAYMENT_METHOD_DEBIT":"Debit","SELECT_BANK_PLACEHOLDER":"Select an option","CREDIT_CARD_NAME_LABEL_V2":"Cardholder","SELECT_ALL_BAN":"Select all bills","NOT_ON_PREAUTH":"Not on pre-authorized payments","CREDIT_CARD_NUMBER_LABEL_V2":"Card number"},"fr":{"CREDIT_CARD_NUMBER_LABEL_V2":"Numéro de carte","NOT_ON_PREAUTH":"Prélèvements automatique non configurés","SELECT_ALL_BAN":"Sélectionnez toutes les factures","ALERT_CONFIRMATION_INFO_HEADING":"Évitez des frais de retard en payant le solde actuel de votre compte.","ALERT_CONFIRMATION_INFO_DESC":"Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément votre solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.","ALERT_CONFIRMATION_INFO_BUTTON_DESC":"Effectuer un paiement","ALERT_CONFIRMATION_INFO_DESC_1":"Pre-authorized payments will only begin on your next billing period. You must pay your ","ALERT_CONFIRMATION_INFO_DESC_2":"balance in a separate and final one-time payment, or risk late fees. ","EXISTING_CC_TITLE":"Sélectionnez une carte de crédit","EXISTING_BANK_TITLE":"Sélectionner un compte bancaire","EXISTING_BANK_TITLE_SR":"Requis, Sélectionner un compte bancaire","EXISTING_BANK_ACCOUNT_MANUAL_DETAILS_LABEL":"Choisir un compte bancaire déjà associé à votre compte Bell, ou en ajouter un autre manuellement","SELECT_BILLS_HEADING":"Sélectionner les factures","SELECT_BILLS_HEADING_SINGULAR":"Sélectionner les factures","SELECT_BILLS_ACCOUNT_TITLE":"Numéro de compte {accounttype}","SELECT_BILLS_HEADING_DESC":"Pour quelle(s) facture(s) souhaitez-vous mettre à jour les renseignements de paiement?","SELECT_BANK_TITLE":"Requis, Sélectionner un compte bancaire","ACCOUNT_TYPE_ONE_BILL":"Facture unique","ACCOUNT_TYPE_MYBILL":"One Bill","ACCOUNT_TYPENAME_ONEBILL":"Facture unique","ACCOUNT_TYPENAME_MYBILL":"Ma facture","ACCOUNT_TYPENAME_MOBILITY":"Mobilité","ACCOUNT_TYPENAME_TV":"TV","ACCOUNT_TYPENAME_INTERNET":"Internet","ACCOUNT_TYPENAME_HOMEPHONE":"Home Phone","ACCOUNT_TYPENAME_MOBILITY_ONEBILL":"Mobility and One Bill","ACCOUNT_TYPENAME_SINGLEBAN":"Single Ban","ACCOUNT_BALANCE":"Votre solde actuel est de","CHECKBOX_BALANCE":"{balance}$","CHECKBOX_BALANCE_SR":"Votre solde actuel est de {balance} dollars","BACK_TO_MY_BELL":"Retour à MonBell","CTA_NEXT":"Suivant","CTA_EDIT":"Modifier","CTA_CLOSE":"Fermer la boîte de dialogue","CTA_EXPAND_TERMS":"Afficher les modalités de service","CTA_COLLAPSE_TERMS":"Masquer les modalités de service","CTA_CONFIRM":"Confirmer et envoyer","CTA_CANCEL":"Annuler","CTA_INTERAC":"Se connecter avec Interac®","CTA_INTERAC_SR":"Se connecter avec Interac marque déposée","CREDIT_CARD_LABEL":"Carte de crédit","CREDIT_CARD_TYPE_LABEL":"Type de carte","CREDIT_CARD_NAME_LABEL":"Nom du titulaire de la carte","CREDIT_CARD_NAME_DESC_INPUT_LABEL":"Tel qu’il apparaît sur la carte","CREDIT_CARD_NUMBER_LABEL":"Numéro de carte de crédit","CREDIT_CARD_NUMBER_DESC_INPUT_LABEL":"15 ou 16 chiffres","CREDIT_CARD_EXPIRY_LABEL":"Date d’expiration","CREDIT_CARD_EXPIRY_SR_MONTH_LABEL":"Requis, Date d’expiration mois","CREDIT_CARD_EXPIRY_SR_YEAR_LABEL":"Requis, Date d’expiration année","CREDIT_CARD_SECURITY_CODE_INPUT_LABEL":"Code de sécurité","CREDIT_CARD_NAME_SR_LABEL":"Requis, Nom du titulaire de la carte ","CREDIT_CARD_NUMBER_SR_LABEL":"Requis, Numéro de carte de crédit","CREDIT_CARD_EXPIRY_DATE_LABEL":"Requis, Date d’expiration","CREDIT_CARD_EXPIRY_DATE_SR_LABEL":" {{month} / {year}}","CREDIT_CARD_MONTH_TEXT":"mois","CREDIT_CARD_YEAR_TEXT":"année","CREDIT_CARD_SECURITY_CODE_INPUT_SR_LABEL":"Requis, Code de sécurité","ERROR_CREDIT_CARD_NAME_INPUT_LABEL":"Entrer un nom de titulaire de carte valide ","ERROR_CREDIT_CARD_NUMBER_INPUT_LABEL":"Entrer un numéro de carte de crédit valide ","ERROR_CREDIT_CARD_EXPIRY_INPUT_LABEL":"Entrer une date d’expiration valide","ERROR_CREDIT_CARD_SECURITY_CODE_INPUT_LABEL":"Entrer un code de sécurité valide ","CREDIT_CARD_VALID":"Valable jusqu\'au","CREDIT_CARD_MONTH_PLACEHOLDER":"MM","CREDIT_CARD_YEAR_PLACEHOLDER":"AA","REQUIRED_LABEL":"*Renseignement requis","MODAL_NO_NAME":"Il n’y a pas de nom sur la carte?","MODAL_NO_NAME_TITLE":"Pas de nom sur la carte de crédit?","MODAL_NO_NAME_DESC":"Si vous utilisez une carte de crédit prépayée sur laquelle ne figure aucun nom, entrez le nom du titulaire de compte Bell. ","MODAL_SECURITY_CODE":"Qu’est-ce que le code de sécurité de la carte?","MODAL_SECURITY_CODE_TITLE":"Qu’est-ce que le code de sécurité?","MODAL_SECURITY_CODE_DESC":"Le code de sécurité de la carte de crédit (CSC) est une mesure de prévention contre la fraude. Il permet de vérifier que la carte de crédit est en votre possession. Ce code est parfois appelé code de vérification de carte (CVC) ou valeur de vérification de carte (CVV). Voici où trouver votre code de sécurité :","CARD_TYPE_VISA_MASTERCARD":"Visa et Mastercard","CARD_TYPE_VISA_MASTERCARD_DESC":"Un numéro de 3 chiffres au verso de la carte de crédit.","CARD_TYPE_AMERICAN_EXP":"American Express","CARD_TYPE_AMERICAN_EXP_DESC":"Un numéro de 4 chiffres au recto de la carte de crédit.","MODAL_TRANSIT_ACCOUNT_NUMBERS_TITLE":"Trouver votre numéro de succursale et votre numéro de compte","MODAL_BANK_STATEMENT_TITLE":"Sur un relevé bancaire ou un chèque","MODAL_BANK_STATEMENT_DESC":"Vous trouverez vos renseignements bancaires sur un relevé bancaire ou un chèque. Vous pouvez aussi trouver ces renseignements dans votre application bancaire ou en accédant à votre compte bancaire en ligne.","MODAL_TRANSIT_NUMBER_DESC":"Le numéro de succursale comporte 5 chiffres.","MODAL_ACCOUNT_NUMBER_DESC":"Votre numéro de compte comporte de 7 à 12 chiffres.","ALERT_ERROR_HEADING":"Une erreur est survenue pendant le traitement de votre demande.","ALERT_ERROR_HEADING_SR":"Une erreur est survenue pendant le traitement de votre demande.","ALERT_ERROR_INFO_REQUIRED":"– Ce renseignement est requis.","ALERT_ERROR_SELECT_BILL_INFO":"Sélectionner une facture","ALERT_ERROR_SELECT_BILL_DESC":"Ce renseignement est requis.","ALERT_ERROR_SELECT_BILL_INFO_REQUIRED":"Select a bill – This information is required.","ALERT_ERROR_ONE_SELECT_BILL":"Sélectionnez au moins une facture","ALERT_ERROR_HEADING_SOME_BALANCE":"<strong>Certains de vos soldes actuels</strong> n’ont pas été payés en raison d’une erreur lors du traitement de votre demande.","ALERT_ERROR_OTP_ALL_BALANCE":"<strong>Votre ou vos soldes actuels</strong> n’ont pas été payés en raison d’une erreur lors du traitement de votre demande.","ALERT_ERROR_OTP_BALANCE":"<strong>Votre solde de {balance} $ n\'a pas été payé.</strong>","ALERT_ERROR_OTP_BALANCE_SR":"Votre solde de {balance} dollars n\'a pas été payé.","ALERT_ERROR_OTP_BALANCE_DESC":"Une erreur s’est produite lors du traitement de votre paiement unique. Veuillez payer le solde actuel de votre compte afin d’éviter des frais de retard.","ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR":"Même si vous avez choisi de ne pas payer le solde suivant, un paiement séparé sera également requis:","ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL":"Même si vous avez choisi de ne pas payer le(s) solde(s) suivant(s), ils nécessiteront également un paiement séparé:","SELECT_PAYMENT_METHOD_HEADING":"Choisir le mode de paiement","PAY_CURRENT_BALANCE_HEADING":"Payer votre solde actuel","PAY_CURRENT_BALANCE_DESC":"Les paiements par prélèvement automatique débuteront <strong class=\'brui-text-black\'>à partir de votre prochaine facture.</strong> Vous pouvez payer séparément votre solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.","PAY_CURRENT_BALANCE_OPTED_IN":"Votre paiement unique sera traité dans un délai de <strong>trois à cinq jours ouvrables</strong>. Par la suite, il restera un solde de {balance} dans votre compte.","PAY_CURRENT_BALANCE_OPTED_IN_PACC":"Votre paiement unique sera traité <strong>dès que vous enverrez cette transaction</strong>.","PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR":"<p>Votre compte comporte un solde à payer.</p><p class=\'brui-mt-10\'>Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément votre solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.</p>","PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL":"<p>Vos comptes comportent des soldes à payer.</p><p class=\'brui-mt-10\'>Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément vos soldes actuels au moyen d’un paiement unique, afin d’éviter des frais de retard.</p>","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_IN":"Votre paiement unique sera traité dans un délai de <strong>trois à cinq jours ouvrables</strong>. Par la suite, il restera un solde de {balance} dans votre compte.","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_SINGULAR":"Vous avez choisi de ne pas payer le solde actuel de votre compte.","PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_PLURAL":"Vous avez choisi de ne pas payer le solde actuel de votre compte.","PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_SINGULAR":"Votre autre compte comporte un solde à payer.","PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_PLURAL":"Vos autres comptes comportent des soldes à payer.","PAYMENT_AMOUNT":"Montant du paiement","UNPAID_BALANCE":"Solde impayé","PAY_MY_BALANCE":"Payer mon solde de","PAY_MY_BALANCE_SR":"Payer mon solde de {balance} dollars","PAY_CURRENT_BALANCE_NOTE_1":"Nous utiliserons les renseignements relatifs au compte bancaire ci-dessus pour le paiement.","PAY_CURRENT_BALANCE_NOTE_2":"Remarque : Si vous avez effectué un paiement au cours des trois à cinq derniers jours ouvrables, il se peut que votre solde ne soit pas encore mis à jour.","TERMS_AND_CONDITION_HEADING":"Modalités et conditions","TERMS_AND_CONDITION_DISCLAIMER":"En cliquant sur Confirmer et envoyer, je confirme avoir lu et accepté les modalités de service Bell et les tarifs du ou des services sélectionnés","TRANSACTION_SUBMITTED_HEADING":"Transaction envoyée","CONFIRMATION_HEADING":"Confirmation","ALERT_CONFIRMATION_SUCCESS_HEADING":"Voici ce qui se passe :","ALERT_CONFIRMATION_SUCCESS_MESSAGE":"Les paiements par prélèvement automatique ont été mis à jour et commenceront sur votre <strong>prochaine facture.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_V2":"Pre-authorized payments have been set up and will begin on your <strong>next billing cycle.</strong>","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC":"Votre paiement unique pour le compte <strong> {account} </strong> a bien été traité.","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD":"Votre paiement unique porté au compte <strong> {account} </strong> sera traité dans un délai de <strong>trois à cinq jours ouvrables.</strong>","ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD_FR":"Votre paiement unique porté au compte no <strong> {account} </strong> sera traité dans un délai de <strong>trois à cinq jours ouvrables.</strong>","ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS":"Votre crédit pour autopaiement débutera d’ici une à deux périodes de facturation.","ALERT_CONFIRMATION_SUCCESS_NUMBER":"Numéro de confirmation :","ALERT_CONFIRMATION_SUCCESS_DESC":"Nous avons envoyé un courriel de confirmation à l’adresse {email}. S’il ne s’agit pas de la bonne adresse de courriel, veuillez","ALERT_CONFIRMATION_SUCCESS_DESC_LINK":"mettre votre profil à jour","ALERT_CONFIRMATION_SUCCESS_CONFIRM_NO":"Confirmation Number:","ALERT_CONFIRMATION_CURRENT_BALANCE":"The current balance for {account} has been paid.","PAYMENT_SUMMARY_TITLE":"Sommaire des prélèvements","PAYMENT_INFORMATION_TITLE":"Renseignements de paiement","BILL_INFORMATION_TITLE":"Renseignements sur la facture","BILL_INFORMATION_ACCOUNT_NUMBER":"Compte","PAYMENT_METHOD_TITLE":"Mode de paiement","CURRENT_BALANCE_TITLE":"Solde actuel","SUMMARY_CURRENT_BAL_OTP_FAILURE":"Le paiement de votre solde actuel n’a pas été traité. Veuillez effectuer un paiement unique distinct.","TERMS_AND_CON_TITLE":"Autorisation pour prélèvement automatique","TERMS_AND_CON_DESC_1":"Vous nous autorisez, Bell, à mettre en place le paiement par prélèvement automatique en utilisant les renseignements de paiement que vous avez fournis, comme suit.","TERMS_AND_CON_DESC_LIST_1":"Pour les paiements mensuels:","TERMS_AND_CON_DESC_LIST_1_ITEM1":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit le montant total dû, à la même date ou à une date près de cette date chaque mois (par exemple, la date peut être différente si elle tombe un dimanche ou un jour férié);","TERMS_AND_CON_DESC_LIST_1_ITEM2":"Vous serez avisé au moins 10 jours à l’avance sur votre facture du montant dû, qui peut varier en fonction des frais que vous avez engagés;","TERMS_AND_CON_DESC_LIST_1_ITEM3":"Pour les services pour lesquels aucune facture n’est fournie, vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant demeure le même ou correspond à celui convenu précédemment.","TERMS_AND_CON_DESC_LIST_2":"Pour <strong>ajouter des fonds</strong> à votre compte pour les services prépayés :","TERMS_AND_CON_DESC_LIST_2_ITEM1":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit, le montant établi selon les critères que vous avez sélectionnés;","TERMS_AND_CON_DESC_LIST_2_ITEM2":"<strong>Vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant est le même que celui que vous avez sélectionné ou inférieur.</strong>","TERMS_AND_CON_DESC_2":"Pour les <strong>autres types de paiements</strong>,  nous obtiendrons votre autorisation avant le prélèvement. Dans certaines circonstances, nous pouvons également utiliser votre mode de paiement par prélèvement automatique pour effectuer des remboursements.","TERMS_AND_CON_DESC_3":"Si un prélèvement n’est pas conforme à l’autorisation, vous pouvez avoir certains droits, comme demander son remboursement. Pour en savoir plus ou pour annuler cette autorisation, appelez-nous ou rendez-vous sur MonBell (monbell.ca). Lorsque vous annulez votre autorisation, vous devez nous en aviser au moins 30 jours avant la date du prochain prélèvement. Pour les prélèvements automatiques effectués avec un compte bancaire, pour obtenir un exemple de formulaire d’annulation ou pour en savoir plus sur votre droit d’annuler cette autorisation, communiquez avec votre institution financière ou visitez paiements.ca.","TERMS_AND_CON_DESC_4":"Bell Canada ","TERMS_AND_CON_ZIP_CODE":"C.P. 8712, succ. Centre-Ville","TERMS_AND_CON_REGION":"Montréal (Québec) H3C 3P6","TERMS_AND_CON_TEL":"Tél.: **************","TERMS_AND_CONDITION_HEADING_QC":"Modalités et conditions","TERMS_AND_CON_TITLE_QC":"Autorisation pour prélèvement automatique","TERMS_AND_CON_DESC_1_QC":"Vous nous autorisez, Bell, à mettre en place le paiement par prélèvement automatique en utilisant les renseignements de paiement que vous avez fournis, comme suit.","TERMS_AND_CON_DESC_LIST_1_QC":"Pour les paiements mensuels:","TERMS_AND_CON_DESC_LIST_1_ITEM1_QC":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit le montant total dû, à la même date ou à une date près de cette date chaque mois (par exemple, la date peut être différente si elle tombe un dimanche ou un jour férié);","TERMS_AND_CON_DESC_LIST_1_ITEM2_QC":"Vous serez avisé au moins 10 jours à l’avance sur votre facture du montant dû, qui peut varier en fonction des frais que vous avez engagés;","TERMS_AND_CON_DESC_LIST_1_ITEM3_QC":"Pour les services pour lesquels aucune facture n’est fournie, vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant demeure le même ou correspond à celui convenu précédemment.","TERMS_AND_CON_DESC_LIST_2_QC":"Pour <strong>ajouter des fonds</strong> à votre compte pour les services prépayés :","TERMS_AND_CON_DESC_LIST_2_ITEM1_QC":"Nous débiterons votre compte bancaire ou porterons à votre carte de crédit, le montant établi selon les critères que vous avez sélectionnés;","TERMS_AND_CON_DESC_LIST_2_ITEM2_QC":"<strong>Vous renoncez à l’exigence d’être avisé du montant avant le prélèvement lorsque le montant est le même que celui que vous avez sélectionné ou inférieur.</strong>","TERMS_AND_CON_DESC_2_QC":"Pour les <strong>autres types de paiements</strong>,  nous obtiendrons votre autorisation avant le prélèvement. Dans certaines circonstances, nous pouvons également utiliser votre mode de paiement par prélèvement automatique pour effectuer des remboursements.","TERMS_AND_CON_DESC_3_QC":"Si un prélèvement n’est pas conforme à l’autorisation, vous pouvez avoir certains droits, comme demander son remboursement. Pour en savoir plus ou pour annuler cette autorisation, appelez-nous ou rendez-vous sur MonBell (monbell.ca). Lorsque vous annulez votre autorisation, vous devez nous en aviser au moins 30 jours avant la date du prochain prélèvement. Pour les prélèvements automatiques effectués avec un compte bancaire, pour obtenir un exemple de formulaire d’annulation ou pour en savoir plus sur votre droit d’annuler cette autorisation, communiquez avec votre institution financière ou visitez paiements.ca.","TERMS_AND_CON_DESC_4_QC":"Bell Canada ","TERMS_AND_CON_ZIP_CODE_QC":"C.P. 8712, succ. Centre-Ville","TERMS_AND_CON_REGION_QC":"Montréal (Québec) H3C 3P6","TERMS_AND_CON_TEL_QC":"Tél.: **************","BANK_ACCOUNT_LABEL":"Du compte bancaire","TRANSIT_ACC_NO_PNG":"/Styles/BRF3/content/img/transit-account-number.png","INTERACT_VERIFICATION_V2_PNG":"/Styles/BRF3/content/img/interac-logos/interac-verification-v2-en.png","MASTER_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/mastercard.png","VISA_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/visa.png","AMEX_CC_PNG":"/Styles/BRF3/content/img/creditCardIcons/amex.png","FRONT_CC_PNG":"/Styles/BRF3/content/img/Front.png","BACK_CC_PNG":"/Styles/BRF3/content/img/Back.png","INTERAC_BOX_LOGO":"/Styles/BRF3/content/img/interac-logos/Interac_box_logo.png","SELECT_BILLS_CC_DESC":"{CreditCardType} se terminant par {CCFourDigits}, expirant le {ExpiryDate}","NEW_CREDIT_ACCOUNT_LABEL":"Nouvelle carte de crédit","SELECT_BILLS_BANK_DESC":"{BankName} account {BankMaskedDigits}","ALERT_GREAT_NEWS":"Bonne nouvelle","ALERT_GREAT_NEWS_DESC":" : avec des prélèvements automatiques par débit, les services suivants donneront droit à un crédit pour autopaiement :","ALERT_GREAT_NEWS_DESC_1":" : vous aurez droit à un crédit pour autopaiement.","ALERT_GREAT_NEWS_NOTE":"Remarque : ","ALERT_GREAT_NEWS_NOTE_DESC":"S’il y a des changements à venir à votre compte, ils pourraient avoir une incidence sur le crédit pour autopaiement ci-dessus. Tout changement apporté au crédit entrera en vigueur d’ici une à deux périodes de facturation.","BANK_ACCOUNT_AUTOMATIC_LABEL":"Entrer automatiquement les renseignements relatifs à votre compte","BANK_ACCOUNT_AUTOMATIC_DESCRIPTION":"Utilisez le service de vérification InteracMD pour vous connecter en toute sécurité au site Web de votre institution financière. Offert pour les institutions suivantes :","BANK_ACCOUNT_MANUAL_DETAILS_LABEL":"Entrer manuellement les renseignements relatifs à votre compte","BANK_ACCOUNT_MANUAL_DETAILS_DESCRIPTION":"Bell accepte les paiements de la plupart des banques et coopératives de crédit au Canada.","BANK_NAME_LABEL":"Institution financière","BANK_HOLDER_NAME_LABEL":"Nom du titulaire du compte","BANK_TRANSIT_NUMBER_LABEL":"Numéro de succursale","BANK_TRANSIT_NUMBER_DESCRIPTION":"5 chiffres","BANK_ACCOUNT_NUMBER_LABEL":"Numéro de compte","BANK_ACCOUNT_NUMBER_DESCRIPTION":"7 à 12 chiffres","BANK_ACCOUNT_FETCHED_LABEL":"Numéro de compte entré automatiquement Veuillez confirmer qu’il s’agit du bon compte.","BANK_NEED_HELP":"Besoin d’aide?","BANK_NEW_BANK_ACCOUNT_LABEL":"Nouvelle carte bancaire","SELECT_REQUIRED_LEGEND":"Requis","CC_IMAGE_SR_LABEL":"Cartes de crédit acceptées: Visa MasterCard American Express","LOAD_MORE":"Charger Plus","PAYMENT_METHOD":"Mode de paiement","ACCOUNT_HOLDER":"Nom du titulaire du compte ","BANK_NAME":"Institution financière  ","TRANSIT_NUMER":"Numéro de succursale ","ACCOUNT_NUMBER":"Numéro de compte ","BANK_HOLDER_NAME_ERROR_LABEL":"Entrer un nom valide","BANK_NAME_ERROR_LABEL":"Sélectionner votre institution financière","BANK_TRANSIT_ERROR_LABEL":"Entrer un numéro de succursale de 5 chiffres valide","BANK_ACCOUNT_HOLDER_NUMBER_ERROR_LABEL":"Entrer un numéro de compte bancaire de 7 à 12 chiffres valide","ALERT_ERROR_GENERAL_DESC":"Ce renseignement est requis.","PAGE_TITLE_CONFIRMATON":"Confirmation: Établir un prélèvement automatique document","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_SINGLE":"Évitez des frais de retard en payant le solde actuel de votre compte.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_1":"Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément votre solde actuel de ","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_2":" $ au moyen d’un paiement unique, afin d’éviter des frais de retard.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_MULTI":"Évitez des frais de retard en payant les solde actuel de vos comptes.","ALERT_CONFIRMATION_OPTED_SOME_BALANCES_MULTI":"Évitez des frais de retard en payant les solde actuel de vos comptes.","ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_MULTI":"Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément vos solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.","ALERT_CONFIRMATION_OPTED_SOME_BALANCES_DESC_MULTI":"Les paiements par prélèvement automatique débuteront à partir de votre prochaine facture. Vous pouvez payer séparément vos solde actuel au moyen d’un paiement unique, afin d’éviter des frais de retard.","CTA_MAKE_PAYMENT_LINK":"/Payment/MakePayment","AUTOPAY_TRANSACTION_NOTE_DESC":"S’il y a des changements à venir à votre compte, ils pourraient avoir une incidence sur le crédit pour autopaiement ci-dessus. Tout changement apporté au crédit entrera en vigueur d’ici une à deux périodes de facturation.","AUTOPAY_TRANSACTION_NOTE_HEADING":"Remarque :","LOADER":"Chargement des données. Veuillez patienter…","INTERAC_FETCHED_LABEL":"Numéro de compte entré automatiquement","INTERAC_FETCHED_SUBTITLE":"Veuillez confirmer qu’il s’agit du bon compte.","ALERT_ERROR_HEADING_INTERAC":"Un problème technique est survenu","ALERT_ERROR_HEADING_INTERAC_SR":"Erreur: Un problème technique est survenu","ALERT_ERROR_HEADING_INTERAC_DESC":"Il nous est malheureusement impossible de traiter votre demande en raison d’un problème technique. Vous pouvez entrer vos renseignements bancaires manuellement.","FAILURE_API_BAN_HEADING":"Un problème est survenu","FAILURE_API_BAN_HEADING_SR":"Erreur, Un problème est survenu","FAILURE_API_BAN_MAIN_DESC":"Il nous est malheureusement impossible de traiter votre demande.","FAILURE_API_BAN_MAIN_DESC_2":"Veuillez réessayer.","FAILURE_API_BAN_SUB_DESC":"Si le problème persiste :","FAILURE_API_BAN_SUB_DESC_LISTITEM_1":"Essayez d’utiliser une autre carte de crédit ou encore de configurer des prélèvements automatiques par débit.","FAILURE_API_BAN_SUB_DESC_LISTITEM_2":"Attendez un moment, puis réessayez.","FAILURE_API_BAN_BUTTON":"Réessayer","LOADER_SUBMIT":"Transmission des renseignements de paiement en cours","LOADER_SUBMIT_DESC":"Veuillez patienter","LABEL_LOADED_OFFERS_TITLE":" – avec des paiements par prélèvement automatique, les services suivants donneront droit à un crédit pour autopaiement :","LABEL_LOADED_OFFER_TITLE":" – avec des paiements par prélèvement automatique, le service suivant donnera droit à un crédit pour autopaiement :","GREAT_NEWS":"Bonne nouvelle","MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING":"Remarque :","MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE":" S’il y a des changements à venir à votre compte, ils pourraient avoir une incidence sur les crédits pour autopaiement ci-dessus. Tout changement apporté aux crédits entrera en vigueur d’ici une à deux périodes de facturation.","MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE":" S’il y a des changements à venir à votre compte, ils pourraient avoir une incidence sur le crédit pour autopaiement ci-dessus. Tout changement apporté au crédit entrera en vigueur d’ici une à deux périodes de facturation.","SORRY_MESSAGE":"Désolé, aucun crédit pour autopaiement n’est offert pour ce mode de paiement.","SORRY_MESSAGE_CREDIT":"Désolé, aucun crédit pour autopaiement n’est offert pour ce mode de paiement. Pour avoir droit au crédit, configurez les prélèvements automatiques par débit.","LABEL_LOADED_OFFERS_CREDIT_TITLE":" : avec des paiements par prélèvement automatique sur carte de crédit, les services suivants donneront droit à un crédit pour autopaiement :","LABEL_LOADED_OFFER_CREDIT_TITLE":" : avec des paiements par prélèvement automatique sur carte de crédit, le service suivant donnera droit à un crédit pour autopaiement :","LABEL_LOADED_OFFERS_DEBIT_TITLE":"avec des prélèvements automatiques par débit, les services suivants donneront droit à un crédit pour autopaiement :","LABEL_LOADED_OFFER_DEBIT_TITLE":"avec des paiements par prélèvement automatique sur carte de crédit, le service suivant donnera droit à un crédit pour autopaiement:","LABEL_LOADED_OFFERS_TITLE_TRUEAUTOPAY":"avec des paiements par prélèvement automatique, les services suivants donneront droit à un crédit pour autopaiement","LABEL_LOADED_OFFER_TITLE_TRUEAUTOPAY":"avec des paiements par prélèvement automatique, le service suivant donneront droit à un crédit pour autopaiement","REVIEW_PAGE_AUTOPAY_CREDIT":"Bonne nouvelle : vous aurez droit à un crédit pour autopaiement.","AUTOPAY_ALERT":"Votre crédit pour autopaiement débutera d’ici une à deux périodes de facturation.","AUTOPAY_CREDITS_TITLE":"Crédits pour autopaiement","InteracSupportedFinancialInstitutions":"BMO,CIBC,Desjardins,RBC,Scotiabank,TD","BANK_ACCOUNT_SR_TEXT":"Numéro de compte se terminant par {Account}","CREDIT_CARD_SR_TEXT":"Numéro de carte se terminant par {Account}","PAYMENT_METHOD_DEBIT":"Débit","SELECT_BANK_PLACEHOLDER":"Sélectionner votre institution financière","CREDIT_CARD_NAME_LABEL_V2":"Nom du détenteur du compte"}}')},function(e){"use strict";e.exports=i},function(e){"use strict";e.exports=o},function(e){"use strict";e.exports=l},function(e,t,n){var r;r=(e,t)=>(()=>{"use strict";function n(e){var t,r=ca[e];return void 0!==r?r.exports:(t=ca[e]={exports:{}},sa[e](t,t.exports,n),t.exports)}function r(e){e.length=0}function a(e,t,n){return Array.prototype.slice.call(e,t,n)}function i(e){return e.bind.apply(e,[null].concat(a(arguments,1)))}function o(e){return requestAnimationFrame(e)}function l(e,t){return typeof t===e}function u(e){return!s(e)&&l("object",e)}function s(e){return null===e}function c(e){try{return e instanceof(e.ownerDocument.defaultView||window).HTMLElement}catch(e){return!1}}function m(e){return Me(e)?e:[e]}function d(e,t){m(e).forEach(t)}function p(e,t){return e.indexOf(t)>-1}function b(e,t){return e.push.apply(e,m(t)),e}function E(e,t,n){e&&d(t,function(t){t&&e.classList[n?"add":"remove"](t)})}function f(e,t){E(e,De(t)?t.split(" "):t,!0)}function _(e,t){d(t,e.appendChild.bind(e))}function y(e,t){d(e,function(e){var n=(t||e).parentNode;n&&n.insertBefore(e,t)})}function N(e,t){return c(e)&&(e.msMatchesSelector||e.matches).call(e,t)}function g(e,t){var n=e?a(e.children):[];return t?n.filter(function(e){return N(e,t)}):n}function A(e,t){return t?g(e,t)[0]:e.firstElementChild}function C(e,t,n){return e&&(n?Pe(e).reverse():Pe(e)).forEach(function(n){"__proto__"!==n&&t(e[n],n)}),e}function v(e){return a(arguments,1).forEach(function(t){C(t,function(n,r){e[r]=t[r]})}),e}function T(e){return a(arguments,1).forEach(function(t){C(t,function(t,n){Me(t)?e[n]=t.slice():u(t)?e[n]=T({},u(e[n])?e[n]:{},t):e[n]=t})}),e}function h(e,t){d(t||Pe(e),function(t){delete e[t]})}function I(e,t){d(e,function(e){d(t,function(t){e&&e.removeAttribute(t)})})}function R(e,t,n){u(t)?C(t,function(t,n){R(e,n,t)}):d(e,function(e){s(n)||""===n?I(e,t):e.setAttribute(t,String(n))})}function S(e,t,n){var r=document.createElement(e);return t&&(De(t)?f(r,t):R(r,t)),n&&_(n,r),r}function O(e,t,n){if(Be(n))return getComputedStyle(e)[t];s(n)||(e.style[t]=""+n)}function x(e,t){O(e,"display",t)}function M(e){e.setActive&&e.setActive()||e.focus({preventScroll:!0})}function L(e,t){return e.getAttribute(t)}function D(e,t){return e&&e.classList.contains(t)}function B(e){return e.getBoundingClientRect()}function P(e){d(e,function(e){e&&e.parentNode&&e.parentNode.removeChild(e)})}function k(e){return A((new DOMParser).parseFromString(e,"text/html").body)}function w(e,t){e.preventDefault(),t&&(e.stopPropagation(),e.stopImmediatePropagation())}function U(e,t){return e&&e.querySelector(t)}function F(e,t){return t?a(e.querySelectorAll(t)):[]}function H(e,t){E(e,t,!1)}function Y(e){return e.timeStamp}function j(e){return De(e)?e:e?e+"px":""}function G(e,t){if(!e)throw new Error("["+ke+"] "+(t||""))}function V(e,t,n){return je(e-t)<n}function z(e,t,n,r){var a=Ue(t,n),i=Fe(t,n);return r?a<e&&e<i:a<=e&&e<=i}function q(e,t,n){var r=Ue(t,n),a=Fe(t,n);return Ue(Fe(r,e),a)}function K(e){return+(e>0)-+(e<0)}function X(e,t){return d(t,function(t){e=e.replace("%s",""+t)}),e}function W(e){return e<10?"0"+e:""+e}function Q(){function e(e,t,n){d(e,function(e){e&&d(t,function(t){t.split(" ").forEach(function(t){var r=t.split(".");n(e,r[0],r[1])})})})}var t=[];return{bind:function(n,r,a,i){e(n,r,function(e,n,r){var o="addEventListener"in e,l=o?e.removeEventListener.bind(e,n,a,i):e.removeListener.bind(e,a);o?e.addEventListener(n,a,i):e.addListener(a),t.push([e,n,r,a,l])})},unbind:function(n,r,a){e(n,r,function(e,n,r){t=t.filter(function(t){return!!(t[0]!==e||t[1]!==n||t[2]!==r||a&&t[3]!==a)||(t[4](),!1)})})},dispatch:function(e,t,n){var r,a=!0;return"function"==typeof CustomEvent?r=new CustomEvent(t,{bubbles:a,detail:n}):(r=document.createEvent("CustomEvent")).initCustomEvent(t,a,!1,n),e.dispatchEvent(r),r},destroy:function(){t.forEach(function(e){e[4]()}),r(t)}}}function Z(e){var t=e?e.event.bus:document.createDocumentFragment(),n=Q();return e&&e.event.on(ut,n.destroy),v(n,{bus:t,on:function(e,r){n.bind(t,m(e).join(" "),function(e){r.apply(r,Me(e.detail)?e.detail:[])})},off:i(n.unbind,t),emit:function(e){n.dispatch(t,e,a(arguments,1))}})}function $(e,t,n,r){function a(){if(!d){if(m=e?Ue((c()-u)/e,1):1,n&&n(m),m>=1&&(t(),u=c(),r&&++p>=r))return i();s=o(a)}}function i(){d=!0}function l(){s&&cancelAnimationFrame(s),m=0,s=0,d=!0}var u,s,c=Date.now,m=0,d=!0,p=0;return{start:function(t){t||l(),u=c()-(t?m*e:0),d=!1,s=o(a)},rewind:function(){u=c(),m=0,n&&n(m)},pause:i,cancel:l,set:function(t){e=t},isPaused:function(){return d}}}function J(e){return e=De(e)?e:e.key,vn[e]||e}function ee(e,t,n){function r(){a.forEach(function(e){e.style("transform","translateX(-"+100*e.index+"%)")})}var a=t.Slides;return{mount:function(){Z(e).on([Ve,Je],r)},start:function(e,t){a.style("transition","opacity "+n.speed+"ms "+n.easing),Oe(t)},cancel:xe}}function te(e,t,n){function r(){c(""),u.cancel()}var a,o=t.Move,l=t.Controller,u=t.Scroll,s=t.Elements.list,c=i(O,s,"transition");return{mount:function(){Z(e).bind(s,"transitionend",function(e){e.target===s&&a&&(r(),a())})},start:function(t,r){var i=o.toPosition(t,!0),s=o.getPosition(),m=function(t){var r,a,i=n.rewindSpeed;return e.is(yn)&&i&&(r=l.getIndex(!0),a=l.getEnd(),0===r&&t>=a||r>=a&&0===t)?i:n.speed}(t);je(i-s)>=1&&m>=1?n.useScroll?u.scroll(i,m,!1,r):(c("transform "+m+"ms "+n.easing),o.translate(i,!0),a=r):(o.jump(t),r())},cancel:r}}function ne(...e){return e.filter(Boolean).join(" ")}function re(e){return null!==e&&"object"==typeof e}function ae(e,t){if(Array.isArray(e)&&Array.isArray(t))return e.length===t.length&&!e.some((e,n)=>!ae(e,t[n]));if(re(e)&&re(t)){const n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&!n.some(n=>!Object.prototype.hasOwnProperty.call(t,n)||!ae(e[n],t[n]))}return e===t}function ie(e,t){const n=e;return function(e,t){if(e){const n=Object.keys(e);for(let r=0;r<n.length;r++){const a=n[r];if("__proto__"!==a&&!1===t(e[a],a))break}}}(t,(e,t)=>{Array.isArray(e)?n[t]=e.slice():re(e)?n[t]=ie(re(n[t])?n[t]:{},e):n[t]=e}),n}function oe(){return oe=Object.assign?Object.assign.bind():function(e){var t,n,r;for(t=1;t<arguments.length;t++)for(r in n=arguments[t])({}).hasOwnProperty.call(n,r)&&(e[r]=n[r]);return e},oe.apply(null,arguments)}function le(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}function ue(e){return e}function se(e,t){void 0===t&&(t=ue);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var a=t(e,r);return n.push(a),function(){n=n.filter(function(e){return e!==a})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){var t,a,i,o;r=!0,t=[],n.length&&(a=n,n=[],a.forEach(e),t=n),i=function(){var n=t;t=[],n.forEach(e)},(o=function(){return Promise.resolve().then(i)})(),n={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),n}}}}}function ce(e,t){return void 0===t&&(t=ue),se(e,t)}function me(e,t){return me=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},me(e,t)}function de(e){return de="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},de(e)}function pe(e){if(!e)return null;if("undefined"==typeof WeakRef)return function(){return e||null};var t=e?new WeakRef(e):null;return function(){return(null==t?void 0:t.deref())||null}}function be(e){setTimeout(e,1)}function Ee(e,t,n,r){var a,i=null,o=e;do{if((a=r[o]).guard)a.node.dataset.focusAutoGuard&&(i=a);else{if(!a.lockItem)break;if(o!==e)return;i=null}}while((o+=n)!==t);i&&(i.node.tabIndex=0)}var fe,_e,ye,Ne,ge,Ae,Ce,ve,Te,he,Ie,Re,Se,Oe,xe,Me,Le,De,Be,Pe,ke,we,Ue,Fe,He,Ye,je,Ge,Ve,ze,qe,Ke,Xe,We,Qe,Ze,$e,Je,et,tt,nt,rt,at,it,ot,lt,ut,st,ct,mt,dt,pt,bt,Et,ft,_t,yt,Nt,gt,At,Ct,vt,Tt,ht,It,Rt,St,Ot,xt,Mt,Lt,Dt,Bt,Pt,kt,wt,Ut,Ft,Ht,Yt,jt,Gt,Vt,zt,qt,Kt,Xt,Wt,Qt,Zt,$t,Jt,en,tn,nn,rn,an,on,ln,un,sn,cn,mn,dn,pn,bn,En,fn,_n,yn,Nn,gn,An,Cn,vn,Tn,hn,In,Rn,Sn,On,xn,Mn,Ln,Dn,Bn,Pn,kn,wn,Un,Fn,Hn,Yn,jn,Gn,Vn,zn,qn,Kn,Xn,Wn,Qn,Zn,$n,Jn,er,tr,nr,rr,ar,ir,or,lr,ur,sr,cr,mr,dr,pr,br,Er,fr,_r,yr,Nr,gr,Ar,Cr,vr,Tr,hr,Ir,Rr,Sr,Or,xr,Mr,Lr,Dr,Br,Pr,kr,wr,Ur,Fr,Hr,Yr,jr,Gr,Vr,zr,qr,Kr,Xr,Wr,Qr,Zr,$r,Jr,ea,ta,na,ra,aa,ia,oa,la,ua,sa={155:t=>{t.exports=e},514:e=>{e.exports=t}},ca={};n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},_e=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,n.t=function(e,t){var r,a,i;if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}for(r=Object.create(null),n.r(r),a={},fe=fe||[null,_e({}),_e([]),_e(_e)],i=2&t&&e;"object"==typeof i&&!~fe.indexOf(i);i=_e(i))Object.getOwnPropertyNames(i).forEach(t=>a[t]=()=>e[t]);return a.default=()=>e,n.d(r,a),r},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},ye={},n.r(ye),n.d(ye,{Accordion:()=>da,AccordionContent:()=>ga,AccordionIcon:()=>Ca,AccordionItem:()=>ba,AccordionToggleTitle:()=>va,AccordionTrigger:()=>Ta,Alert:()=>Oa,Button:()=>Da,Card:()=>Ia,Carousel:()=>Pa,CarouselArrows:()=>ka,CarouselContent:()=>wa,CarouselItem:()=>Ua,CarouselPagination:()=>Fa,Checkbox:()=>za,CheckboxCard:()=>Qa,CheckboxCardBody:()=>Za,CheckboxCardInput:()=>Ka,CheckboxCardPrice:()=>$a,Container:()=>Ja,Divider:()=>ei,DockBar:()=>ni,DynamicRadioContent:()=>$i,Fixed:()=>ri,FormControl:()=>oi,FormGroup:()=>ui,Heading:()=>ci,HeadingStep:()=>mi,Icon:()=>Aa,IconLink:()=>fi,InputError:()=>Ga,InputText:()=>Ni,Label:()=>Ai,Link:()=>Ei,ListItem:()=>Ci,Modal:()=>Si,ModalBody:()=>xi,ModalContent:()=>Bi,ModalFooter:()=>ki,ModalHeader:()=>Ui,Portal:()=>Ii,Price:()=>ji,RadioButton:()=>qi,RadioCard:()=>Zi,RadioCardBody:()=>Ji,RadioCardInput:()=>Xi,RadioCardPrice:()=>eo,SameHeightGroup:()=>no,SameHeightItem:()=>ro,Select:()=>Eo,SelectContext:()=>oo,SelectCustom:()=>co,SelectDropdown:()=>po,SelectNativeHidden:()=>uo,SelectOption:()=>fo,SrOnly:()=>Ra,Static:()=>ai,Tab:()=>Ao,TabList:()=>Co,TabPanel:()=>vo,Tabs:()=>No,Tag:()=>_o,Text:()=>ja,default:()=>To,useBodyHeightObserver:()=>Na,useHeightResizeObserver:()=>_a,useKeyboardListener:()=>Ea,useResponsiveHeight:()=>ya,useWindowResize:()=>fa}),Ne=n(155),ge=n.t(Ne,2),Ae=n.n(Ne);const ma=(0,Ne.createContext)({activeItems:null,toggleAccordionItems:null}),da=({mode:e,children:t,onActive:n,onInactive:r})=>{const[a,i]=(0,Ne.useState)([]);return(0,Ne.useEffect)(()=>{0===a.length?r&&r():n&&n()},[a]),Ae().createElement(ma.Provider,{value:{activeItems:a,toggleAccordionItems:t=>{"multiple"===e?a.includes(t)?i(e=>e.filter(e=>e!==t)):i(e=>[...e,t]):a.includes(t)?i([]):(i([]),i(e=>[...e,t]))}}},t)},pa=(0,Ne.createContext)(null),ba=({children:e,index:t,activeByDefault:n})=>{const{toggleAccordionItems:r}=(0,Ne.useContext)(ma);return(0,Ne.useEffect)(()=>{n&&r&&r(t)},[n]),Ae().createElement(pa.Provider,{value:t},e)},Ea=(e,t="keydown")=>{(0,Ne.useEffect)(()=>(document.addEventListener(t,e),()=>{document.removeEventListener(t,e)}),[])},fa=(e=300)=>{const[t,n]=(0,Ne.useState)({width:window.innerWidth,height:window.innerHeight});return(0,Ne.useEffect)(()=>{const t=()=>{let t=null;t&&clearTimeout(t),t=setTimeout(()=>{n({width:window.innerWidth,height:window.innerHeight})},e)};return window.addEventListener("resize",t),()=>{window.removeEventListener("resize",t)}},[e]),t};Ce=new WeakMap,ve=new WeakMap,Te={},he=0,Ie=function(e){return e&&(e.host||Ie(e.parentNode))},Re=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),a=t||function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body}(e);return a?(r.push.apply(r,Array.from(a.querySelectorAll("[aria-live]"))),function(e,t,n,r){var a,i,o,l,u,s,c=function(e,t){return t.map(function(t){if(e.contains(t))return t;var n=Ie(t);return n&&e.contains(n)?n:null}).filter(function(e){return Boolean(e)})}(t,Array.isArray(e)?e:[e]);return Te[n]||(Te[n]=new WeakMap),a=Te[n],i=[],o=new Set,l=new Set(c),u=function(e){e&&!o.has(e)&&(o.add(e),u(e.parentNode))},c.forEach(u),s=function(e){e&&!l.has(e)&&Array.prototype.forEach.call(e.children,function(e){if(o.has(e))s(e);else try{var t=e.getAttribute(r),l=null!==t&&"false"!==t,u=(Ce.get(e)||0)+1,c=(a.get(e)||0)+1;Ce.set(e,u),a.set(e,c),i.push(e),1===u&&l&&ve.set(e,!0),1===c&&e.setAttribute(n,"true"),l||e.setAttribute(r,"true")}catch(t){}})},s(t),o.clear(),he++,function(){i.forEach(function(e){var t=Ce.get(e)-1,i=a.get(e)-1;Ce.set(e,t),a.set(e,i),t||(ve.has(e)||e.removeAttribute(r),ve.delete(e)),i||e.removeAttribute(n)}),--he||(Ce=new WeakMap,Ce=new WeakMap,ve=new WeakMap,Te={})}}(r,a,n,"aria-hidden")):function(){return null}};const _a=(e,t,n)=>{(0,Ne.useEffect)(()=>{const r=new ResizeObserver(r=>{for(const a of r)if(a.target===e.current){const e=a.contentRect.height;e!==t&&n(e)}});return e.current&&r.observe(e.current),()=>{r.disconnect()}},[])},ya=(e,t)=>(0,Ne.useMemo)(()=>e<768?t.mobile:e>=768&&e<992?t.tablet:t.desktop,[e,t]),Na=(e=300)=>{const[t,n]=(0,Ne.useState)(0);return(0,Ne.useEffect)(()=>{const t=new ResizeObserver(t=>{const r=t[0].target;let a=null;a&&clearTimeout(a),a=setTimeout(()=>{n(r.scrollHeight)},e)});return t.observe(document.body),()=>{t.disconnect()}},[e]),t},ga=({children:e,id:t,className:n,collapseHeight:r,expandHeight:a,"aria-labelledby":i,...o})=>{const{activeItems:l}=(0,Ne.useContext)(ma),u=(0,Ne.useContext)(pa),s=(0,Ne.useRef)(null),c=l?.includes(u),[m,d]=(0,Ne.useState)(s.current?.scrollHeight||0),p=a?parseInt(a,10):m,b=c?`${Math.min(m,p)}px`:r;return _a(s,m,d),Ae().createElement("div",{role:"region",className:["brui-overflow-hidden brui-transition-height brui-ease-in-out brui-duration-200 brui-h-0 brui-group brui-max-h-full",r?.trim()?"brui-scrollbar":"",a?.trim()?"brui-scrollbar":"",l?.includes(u)||r?"":"brui-collapse",n].join(" ").trim(),id:t,"aria-labelledby":i,...o,style:{height:b}},Ae().createElement("div",{className:c||a?"":"brui-hidden",ref:s},e))},Aa=({iconClass:e,iconName:t,className:n,...r})=>Ae().createElement("span",{className:[t,e,n].join(" ").trim(),role:"img","aria-hidden":"true","aria-label":" ",...r}),Ca=({iconCollapse:e,iconExpand:t,iconClass:n,className:r})=>{const{activeItems:a}=(0,Ne.useContext)(ma),i=(0,Ne.useContext)(pa);return Ae().createElement(Aa,{className:r,iconClass:a?.includes(i)?t:e,iconName:n})},va=({titleCollapse:e,titleExpand:t,className:n})=>{const{activeItems:r}=(0,Ne.useContext)(ma),a=(0,Ne.useContext)(pa);return Ae().createElement("div",{className:n},r?.includes(a)?e:t)},Ta=({children:e,id:t,className:n,"aria-controls":r,...a})=>{const{activeItems:i,toggleAccordionItems:o}=(0,Ne.useContext)(ma),l=(0,Ne.useContext)(pa);return Ae().createElement("button",{"aria-expanded":!!i?.includes(l),className:["focus-visible:brui-outline-blue focus-visible:brui-outline focus-visible:brui-outline-2 focus-visible:brui-outline-offset-3 focus-visible:brui-rounded-6 brui-underline-offset-2",n].join(" ").trim(),onClick:()=>{o&&o(l)},id:t,"aria-controls":r,...a},e)},ha={gray:"brui-bg-gray-3",yellow:"brui-bg-yellow brui-border brui-border-yellow-1",solidGray:"brui-bg-gray-1",solidBlue:"brui-bg-blue",solidRed:"brui-bg-red-2",solidYellow:"brui-bg-yellow brui-border brui-border-yellow-1",solidWhite:"brui-bg-white",dropShadow:"brui-shadow-xl brui-border brui-border-gray-4",default:""},Ia=({variant:e,radius:t,children:n,className:r,defaultPadding:a=!1,...i})=>Ae().createElement("div",{className:[ha[e],t?"brui-rounded-10":"",a?"brui-p-30":"",r].join(" ").trim(),...i},n),Ra=({children:e,className:t})=>Ae().createElement("span",{className:["brui-sr-only",t].join(" ").trim()},e),Sa={error:{alertCss:"",iconCss:"brui-text-red",srText:"Error",iconName:"bi_exclamation_c",cardVariant:"default"},info:{alertCss:"brui-border",iconCss:"brui-text-blue",iconName:"bi_big_info_c",srText:"Information",cardVariant:"default"},success:{alertCss:"brui-border",iconCss:"brui-text-green",iconName:"bi_checkmark_c",srText:"Success",cardVariant:"default"},warning:{alertCss:"",iconCss:"brui-text-yellow",srText:"Warning",iconName:"bi_exclamation_c",cardVariant:"default"}},Oa=({variant:e,children:t,className:n,iconSize:r="36",...a})=>Ae().createElement(Ia,{className:[Sa[e].alertCss,n].join(" ").trim(),variant:Sa[e].cardVariant,role:"alert",...a},Ae().createElement("div",null,Ae().createElement(Aa,{iconName:Sa[e].iconName,iconClass:"bi_brui",className:Sa[e].iconCss,role:"img","aria-hidden":"true",style:{fontSize:`${r}px`}}),Ae().createElement(Ra,null,Sa[e].srText)),t),xa={textBlue:"brui-inline-block brui-rounded-4 brui-bg-transparent brui-text-blue brui-underline brui-underline-offset-2 enabled:hover:brui-text-blue-1 enabled:hover:brui-no-underline focus:brui-text-blue-1 focus:brui-no-underline focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 !brui-p-0 disabled:brui-opacity-40",icon:"focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3",default:"",primary:"brui-inline-block brui-rounded-30 brui-bg-blue-1 brui-text-white brui-border-blue-1 brui-border-2 enabled:hover:brui-bg-blue enabled:hover:brui-border-blue focus:brui-outline-blue-2 focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 disabled:brui-bg-gray-2 disabled:brui-text-white disabled:brui-border-gray-2",secondary:"brui-inline-block brui-rounded-30 brui-bg-white brui-text-blue-1 brui-border-blue-1 brui-border-2 enabled:hover:brui-text-blue focus:brui-outline-blue-2 focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 disabled:brui-bg-gray-2 disabled:brui-text-white",primaryReverse:"brui-inline-block brui-rounded-30 brui-bg-white brui-text-blue-1 brui-border-white brui-border-2 enabled:hover:brui-bg-blue-4 enabled:hover:brui-border-blue-4 focus:brui-outline-white focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 disabled:brui-text-white",secondaryReverse:"brui-inline-block brui-rounded-30 brui-bg-blue-1 brui-text-white brui-border-white brui-border-2 enabled:hover:brui-bg-blue-3 focus:brui-outline-white focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 disabled:brui-text-white"},Ma={regular:"brui-text-15 brui-leading-17 brui-py-7 brui-px-30",small:"brui-text-14 brui-leading-18 brui-py-5 brui-px-14",default:""},La=(0,Ne.forwardRef)(function({variant:e="primary",size:t="regular",className:n,...r},a){const i=(0,Ne.useMemo)(()=>({buttonStyle:[Ma[t],xa[e],n].join(" ").trim()}),[e,t]);return Ae().createElement("button",{className:i.buttonStyle,...r,ref:a})}),Da=La;Se="(prefers-reduced-motion: reduce)",Oe=setTimeout,xe=function(){},Me=Array.isArray,Le=i(l,"function"),De=i(l,"string"),Be=i(l,"undefined"),Pe=Object.keys,we="data-"+(ke="splide"),Ue=Math.min,Fe=Math.max,He=Math.floor,Ye=Math.ceil,je=Math.abs,Ge={},Ve="mounted",ze="ready",qe="move",Ke="moved",Xe="click",We="active",Qe="inactive",Ze="visible",$e="hidden",Je="refresh",et="updated",tt="resize",nt="resized",rt="drag",at="dragging",it="dragged",ot="scroll",lt="scrolled",ut="destroy",st="arrows:mounted",ct="arrows:updated",mt="pagination:mounted",dt="pagination:updated",pt="navigation:mounted",bt="autoplay:play",Et="autoplay:playing",ft="autoplay:pause",_t="lazyload:loaded",yt="ei",Tt="ttb",ht={width:["height"],left:["top","right"],right:["bottom","left"],x:["y"],X:["Y"],Y:["X"],ArrowLeft:[Ct=(Nt="Arrow")+"Up",At=Nt+"Right"],ArrowRight:[vt=Nt+"Down",gt=Nt+"Left"]},Mt=(St="aria-")+"selected",wt=St+"live",Ut=St+"busy",Ft=St+"atomic",Ht=[It="role",Rt="tabindex","disabled",Ot=St+"controls",xt=St+"current",Lt=St+"label",Dt=St+"labelledby",Bt=St+"hidden",Pt=St+"orientation",kt=St+"roledescription"],Gt=ke,Vt=(Yt=ke+"__")+"track",zt=Yt+"list",Xt=(qt=Yt+"slide")+"__container",tn=Yt+"progress__bar",nn=Yt+"toggle",rn=Yt+"sr",an=(jt="is-")+"initialized",pn=[on=jt+"active",sn=jt+"visible",ln=jt+"prev",un=jt+"next",cn=jt+"loading",mn=jt+"focus-in",dn=jt+"overflow"],bn={slide:qt,clone:Kt=qt+"--clone",arrows:Wt=Yt+"arrows",arrow:Qt=Yt+"arrow",prev:Zt=Qt+"--prev",next:$t=Qt+"--next",pagination:Jt=Yt+"pagination",page:en=Jt+"__page",spinner:Yt+"spinner"},En="touchstart mousedown",fn="touchmove mousemove",_n="touchend touchcancel mouseup click",yn="slide",Nn="loop",gn="fade",An=we+"-interval",Cn={passive:!1,capture:!0},vn={Spacebar:" ",Right:At,Left:gt,Up:Ct,Down:vt},Tn="keydown",Rn="["+(hn=we+"-lazy")+"], ["+(In=hn+"-srcset")+"]",Sn=[" ","Enter"],On=Object.freeze({__proto__:null,Media:function(e,t,n){function r(e){e&&c.destroy()}function a(e,t){var n=matchMedia(t);c.bind(n,"change",i),m.push([e,n])}function i(){var t=l.is(7),a=n.direction,i=m.reduce(function(e,t){return T(e,t[1].matches?t[0]:{})},{});h(n),o(i),n.destroy?e.destroy("completely"===n.destroy):t?(r(!0),e.mount()):a!==n.direction&&e.refresh()}function o(t,r,a){T(n,t),r&&T(Object.getPrototypeOf(n),t),!a&&l.is(1)||e.emit(et,n)}var l=e.state,u=n.breakpoints||{},s=n.reducedMotion||{},c=Q(),m=[];return{setup:function(){var e="min"===n.mediaQuery;Pe(u).sort(function(t,n){return e?+t-+n:+n-+t}).forEach(function(t){a(u[t],"("+(e?"min":"max")+"-width:"+t+"px)")}),a(s,Se),i()},destroy:r,reduce:function(e){matchMedia(Se).matches&&(e?T(n,s):h(n,Pe(s)))},set:o}},Direction:function(e,t,n){return{resolve:function(e,t,r){var a="rtl"!==(r=r||n.direction)||t?r===Tt?0:-1:1;return ht[e]&&ht[e][a]||e.replace(/width|left|right/i,function(e,t){var n=ht[e.toLowerCase()][a]||e;return t>0?n.charAt(0).toUpperCase()+n.slice(1):n})},orient:function(e){return e*("rtl"===n.direction?1:-1)}}},Elements:function(e,t,n){function a(){var e,t,r;s=l("."+Vt),c=A(s,"."+zt),G(s&&c,"A track/list element is missing."),b(S,g(c,"."+qt+":not(."+Kt+")")),C({arrows:Wt,pagination:Jt,prev:Zt,next:$t,bar:tn,toggle:nn},function(e,t){h[t]=l("."+e)}),v(h,{root:y,track:s,list:c,slides:S}),t=y.id||""+(e=ke)+W(Ge[e]=(Ge[e]||0)+1),r=n.role,y.id=t,s.id=s.id||t+"-track",c.id=c.id||t+"-list",!L(y,It)&&"SECTION"!==y.tagName&&r&&R(y,It,r),R(y,kt,T.carousel),R(c,It,"presentation"),o()}function i(e){var t=Ht.concat("style");r(S),H(y,O),H(s,x),I([s,c],t),I(y,e?t:["style",kt])}function o(){H(y,O),H(s,x),O=u(Gt),x=u(Vt),f(y,O),f(s,x),R(y,Lt,n.label),R(y,Dt,n.labelledby)}function l(e){var t=U(y,e);return t&&function(e,t){if(Le(e.closest))return e.closest(t);for(var n=e;n&&1===n.nodeType&&!N(n,t);)n=n.parentElement;return n}(t,"."+Gt)===y?t:void 0}function u(e){return[e+"--"+n.type,e+"--"+n.direction,n.drag&&e+"--draggable",n.isNavigation&&e+"--nav",e===Gt&&on]}var s,c,m,d=Z(e),p=d.on,_=d.bind,y=e.root,T=n.i18n,h={},S=[],O=[],x=[];return v(h,{setup:a,mount:function(){p(Je,i),p(Je,a),p(et,o),_(document,En+" keydown",function(e){m="keydown"===e.type},{capture:!0}),_(y,"focusin",function(){E(y,mn,!!m)})},destroy:i})},Slides:function(e,t,n){function a(){S.forEach(function(e,t){l(e,t,-1)})}function o(){s(function(e){e.destroy()}),r(w)}function l(t,n,r){var a=function(e,t,n,r){function a(){var a=e.splides.map(function(e){var n=e.splide.Components.Slides.getAt(t);return n?n.slide.id:""}).join(" ");R(r,Lt,X(g.slideX,(x?n:t)+1)),R(r,Ot,a),R(r,It,v?"button":""),v&&I(r,kt)}function o(){s||l()}function l(){var n,a;s||(n=e.index,(a=u())!==D(r,on)&&(E(r,on,a),R(r,xt,y&&a||""),d(a?We:Qe,k)),function(){var t,n=function(){if(e.is(gn))return u();var t=B(b.Elements.track),n=B(r),a=T("left",!0),i=T("right",!0);return He(t[a])<=Ye(n[a])&&He(n[i])<=Ye(t[i])}(),a=!n&&(!u()||x);e.state.is([4,5])||R(r,Bt,a||""),R(F(r,_.focusableNodes||""),Rt,a?-1:""),v&&R(r,Rt,a?-1:0),n!==D(r,sn)&&(E(r,sn,n),d(n?Ze:$e,k)),n||document.activeElement!==r||(t=b.Slides.getAt(e.index))&&M(t.slide)}(),E(r,ln,t===n-1),E(r,un,t===n+1))}function u(){var r=e.index;return r===t||_.cloneStatus&&r===n}var s,c=Z(e),m=c.on,d=c.emit,p=c.bind,b=e.Components,f=e.root,_=e.options,y=_.isNavigation,N=_.updateOnMove,g=_.i18n,C=_.pagination,v=_.slideFocus,T=b.Direction.resolve,h=L(r,"style"),S=L(r,Lt),x=n>-1,P=A(r,"."+Xt),k={index:t,slideIndex:n,slide:r,container:P,isClone:x,mount:function(){x||(r.id=f.id+"-slide"+W(t+1),R(r,It,C?"tabpanel":"group"),R(r,kt,g.slide),R(r,Lt,S||X(g.slideLabel,[t+1,e.length]))),p(r,"click",i(d,Xe,k)),p(r,"keydown",i(d,"sk",k)),m([Ke,"sh",lt],l),m(pt,a),N&&m(qe,o)},destroy:function(){s=!0,c.destroy(),H(r,pn),I(r,Ht),R(r,"style",h),R(r,Lt,S||"")},update:l,style:function(e,t,n){O(n&&P||r,e,t)},isWithin:function(n,r){var a=je(n-t);return x||!_.rewind&&!e.is(Nn)||(a=Ue(a,e.length-a)),a<=r}};return k}(e,n,r,t);a.mount(),w.push(a),w.sort(function(e,t){return e.index-t.index})}function u(e){return e?b(function(e){return!e.isClone}):w}function s(e,t){u(t).forEach(e)}function b(e){return w.filter(Le(e)?e:function(t){return De(e)?N(t.slide,e):p(m(e),t.index)})}var g=Z(e),C=g.on,v=g.emit,T=g.bind,h=t.Elements,S=h.slides,x=h.list,w=[];return{mount:function(){a(),C(Je,o),C(Je,a)},destroy:o,update:function(){s(function(e){e.update()})},register:l,get:u,getIn:function(e){var r=t.Controller,a=r.toIndex(e),i=r.hasFocus()?1:n.perPage;return b(function(e){return z(e.index,a,a+i-1)})},getAt:function(e){return b(e)[0]},add:function(e,t){d(e,function(e){var r,a,o,l,u;De(e)&&(e=k(e)),c(e)&&((r=S[t])?y(e,r):_(x,e),f(e,n.classes.slide),a=e,o=i(v,tt),l=F(a,"img"),(u=l.length)?l.forEach(function(e){T(e,"load error",function(){--u||o()})}):o())}),v(Je)},remove:function(e){P(b(e).map(function(e){return e.slide})),v(Je)},forEach:s,filter:b,style:function(e,t,n){s(function(r){r.style(e,t,n)})},getLength:function(e){return e?S.length:w.length},isEnough:function(){return w.length>n.perPage}}},Layout:function(e,t,n){function r(){_=n.direction===Tt,O(R,"maxWidth",j(n.width)),O(S,h("paddingLeft"),o(!1)),O(S,h("paddingRight"),o(!0)),a(!0)}function a(e){var t,r=B(R);(e||y.width!==r.width||y.height!==r.height)&&(O(S,"height",(t="",_&&(G(t=l(),"height or heightRatio is missing."),t="calc("+t+" - "+o(!1)+" - "+o(!0)+")"),t)),L(h("marginRight"),j(n.gap)),L("width",n.autoWidth?null:j(n.fixedWidth)||(_?"":s())),L("height",j(n.fixedHeight)||(_?n.autoHeight?null:s():l()),!0),y=r,v(nt),N!==(N=f())&&(E(R,dn,N),v("overflow",N)))}function o(e){var t=n.padding,r=h(e?"right":"left");return t&&j(t[r]||(u(t)?0:t))||"0px"}function l(){return j(n.height||B(x).width*n.heightRatio)}function s(){var e=j(n.gap);return"calc((100%"+(e&&" + "+e)+")/"+(n.perPage||1)+(e&&" - "+e)+")"}function c(){return B(x)[h("width")]}function m(e,t){var n=M(e||0);return n?B(n.slide)[h("width")]+(t?0:b()):0}function d(e,t){var n,r,a=M(e);return a?(n=B(a.slide)[h("right")],r=B(x)[h("left")],je(n-r)+(t?0:b())):0}function p(t){return d(e.length-1)-d(0)+m(0,t)}function b(){var e=M(0);return e&&parseFloat(O(e.slide,h("marginRight")))||0}function f(){return e.is(gn)||p(!0)>c()}var _,y,N,g=Z(e),A=g.on,C=g.bind,v=g.emit,T=t.Slides,h=t.Direction.resolve,I=t.Elements,R=I.root,S=I.track,x=I.list,M=T.getAt,L=T.style;return{mount:function(){var e,t;r(),C(window,"resize load",(e=i(v,tt),t=$(0,e,null,1),function(){t.isPaused()&&t.start()})),A([et,Je],r),A(tt,a)},resize:a,listSize:c,slideSize:m,sliderSize:p,totalSize:d,getPadding:function(e){return parseFloat(O(S,h("padding"+(e?"Right":"Left"))))||0},isOverflow:f}},Clones:function(e,t,n){function a(){m(Je,i),m([et,tt],l),(s=u())&&(function(t){var r=p.get().slice(),a=r.length;if(a){for(;r.length<t;)b(r,r);b(r.slice(-t),r.slice(0,t)).forEach(function(i,o){var l=o<t,u=function(t,r){var a=t.cloneNode(!0);return f(a,n.classes.clone),a.id=e.root.id+"-clone"+W(r+1),a}(i.slide,o);l?y(u,r[0].slide):_(d.list,u),b(N,u),p.register(u,o-t+(l?0:a),i.index)})}}(s),t.Layout.resize(!0))}function i(){o(),a()}function o(){P(N),r(N),c.destroy()}function l(){var e=u();s!==e&&(s<e||!e)&&c.emit(Je)}function u(){var r,a=n.clones;return e.is(Nn)?Be(a)&&(a=(r=n[E("fixedWidth")]&&t.Layout.slideSize(0))&&Ye(B(d.track)[E("width")]/r)||n[E("autoWidth")]&&e.length||2*n.perPage):a=0,a}var s,c=Z(e),m=c.on,d=t.Elements,p=t.Slides,E=t.Direction.resolve,N=[];return{mount:a,destroy:o}},Move:function(e,t,n){function r(){t.Controller.isBusy()||(t.Scroll.cancel(),a(e.index),t.Slides.update())}function a(e){i(s(e,!0))}function i(n,r){if(!e.is(gn)){var a=r?n:function(n){if(e.is(Nn)){var r=u(n),a=r>t.Controller.getEnd();(r<0||a)&&(n=o(n,a))}return n}(n);O(R,"transform","translate"+T("X")+"("+a+"px)"),n!==a&&E("sh")}}function o(e,t){var n=e-m(t),r=C();return e-h(r*(Ye(je(n)/r)||1))*(t?1:-1)}function l(){i(c(),!0),d.cancel()}function u(e){var n,r,a,i,o,l;for(n=t.Slides.get(),r=0,a=1/0,i=0;i<n.length&&(o=n[i].index,(l=je(s(o,!0)-e))<=a);i++)a=l,r=o;return r}function s(t,r){var a=h(g(t-1)-function(e){var t=n.focus;return"center"===t?(A()-y(e,!0))/2:+t*y(e)||0}(t));return r?function(t){return n.trimSpace&&e.is(yn)&&(t=q(t,0,h(C(!0)-A()))),t}(a):a}function c(){var e=T("left");return B(R)[e]-B(S)[e]+h(N(!1))}function m(e){return s(e?t.Controller.getEnd():0,!!n.trimSpace)}var d,p=Z(e),b=p.on,E=p.emit,f=e.state.set,_=t.Layout,y=_.slideSize,N=_.getPadding,g=_.totalSize,A=_.listSize,C=_.sliderSize,v=t.Direction,T=v.resolve,h=v.orient,I=t.Elements,R=I.list,S=I.track;return{mount:function(){d=t.Transition,b([Ve,nt,et,Je],r)},move:function(e,t,n,r){var a,u;e!==t&&(a=e>n,u=h(o(c(),a)),a?u>=0:u<=R[T("scrollWidth")]-B(S)[T("width")])&&(l(),i(o(c(),e>n),!0)),f(4),E(qe,t,n,e),d.start(t,function(){f(3),E(Ke,t,n,e),r&&r()})},jump:a,translate:i,shift:o,cancel:l,toIndex:u,toPosition:s,getPosition:c,getLimit:m,exceededLimit:function(e,t){t=Be(t)?c():t;var n=!0!==e&&h(t)<h(m(!1)),r=!1!==e&&h(t)>h(m(!0));return n||r},reposition:r}},Controller:function(e,t,n){function r(){f=S(!0),_=n.perMove,y=n.perPage,E=s();var e=q(B,0,O?E:f-1);e!==B&&(B=e,C.reposition())}function a(){E!==s()&&A(yt)}function o(e,t){var n=_||(p()?1:y),r=l(B+n*(e?-1:1),B,!(_||p()));return-1===r&&M&&!V(v(),T(!e),1)?e?0:E:t?r:u(r)}function l(t,r,a){if(R()||p()){var i=function(t){if(M&&"move"===n.trimSpace&&t!==B)for(var r=v();r===h(t,!0)&&z(t,0,e.length-1,!n.rewind);)t<B?--t:++t;return t}(t);i!==t&&(r=t,t=i,a=!1),t<0||t>E?t=_||!z(0,t,r,!0)&&!z(E,r,t,!0)?x?a?t<0?-(f%y||y):f:t:n.rewind?t<0?E:0:-1:c(m(t)):a&&t!==r&&(t=c(m(r)+(t<r?-1:1)))}else t=-1;return t}function u(e){return x?(e+f)%f||0:e}function s(){for(var e=f-(p()||x&&_?1:y);O&&e-- >0;)if(h(f-1,!0)!==h(e,!0)){e++;break}return q(e,0,f-1)}function c(e){return q(p()?e:y*e,0,E)}function m(e){return p()?Ue(e,E):He((e>=E?f-1:e)/y)}function d(e){e!==B&&(P=B,B=e)}function p(){return!Be(n.focus)||n.isNavigation}function b(){return e.state.is([4,5])&&!!n.waitForTransition}var E,f,_,y,N=Z(e),g=N.on,A=N.emit,C=t.Move,v=C.getPosition,T=C.getLimit,h=C.toPosition,I=t.Slides,R=I.isEnough,S=I.getLength,O=n.omitEnd,x=e.is(Nn),M=e.is(yn),L=i(o,!1),D=i(o,!0),B=n.start||0,P=B;return{mount:function(){r(),g([et,Je,yt],r),g(nt,a)},go:function(e,t,n){if(!b()){var r=function(e){var t,n,r,a=B;return De(e)?(n=(t=e.match(/([+\-<>])(\d+)?/)||[])[1],r=t[2],"+"===n||"-"===n?a=l(B+ +(""+n+(+r||1)),B):">"===n?a=r?c(+r):L(!0):"<"===n&&(a=D(!0))):a=x?e:q(e,0,E),a}(e),a=u(r);a>-1&&(t||a!==B)&&(d(a),C.move(r,a,P,n))}},scroll:function(e,n,r,a){t.Scroll.scroll(e,n,r,function(){var e=u(C.toIndex(v()));d(O?Ue(e,E):e),a&&a()})},getNext:L,getPrev:D,getAdjacent:o,getEnd:s,setIndex:d,getIndex:function(e){return e?P:B},toIndex:c,toPage:m,toDest:function(e){var t=C.toIndex(e);return M?q(t,0,E):t},hasFocus:p,isBusy:b}},Arrows:function(e,t,n){function r(){var e;!(e=n.arrows)||M&&L||(O=T||S("div",N.arrows),M=u(!0),L=u(!1),c=!0,_(O,[M,L]),!T&&y(O,h)),M&&L&&(v(D,{prev:M,next:L}),x(O,e?"":"none"),f(O,m=Wt+"--"+n.direction),e&&(p([Ve,Ke,Je,lt,yt],s),b(L,"click",i(l,">")),b(M,"click",i(l,"<")),s(),R([M,L],Ot,h.id),E(st,M,L))),p(et,a)}function a(){o(),r()}function o(){d.destroy(),H(O,m),c?(P(T?[M,L]:O),M=L=null):I([M,L],Ht)}function l(e){C.go(e,!0)}function u(e){return k('<button class="'+N.arrow+" "+(e?N.prev:N.next)+'" type="button"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="40" height="40" focusable="false"><path d="'+(n.arrowPath||"m15.5 0.932-4.3 4.38 14.5 14.6-14.5 14.5 4.3 4.4 14.6-14.6 4.4-4.3-4.4-4.4-14.6-14.6z")+'" />')}function s(){if(M&&L){var t=e.index,n=C.getPrev(),r=C.getNext(),a=n>-1&&t<n?g.last:g.prev,i=r>-1&&t>r?g.first:g.next;M.disabled=n<0,L.disabled=r<0,R(M,Lt,a),R(L,Lt,i),E(ct,M,L,n,r)}}var c,m,d=Z(e),p=d.on,b=d.bind,E=d.emit,N=n.classes,g=n.i18n,A=t.Elements,C=t.Controller,T=A.arrows,h=A.track,O=T,M=A.prev,L=A.next,D={};return{arrows:D,mount:r,destroy:o,update:s}},Autoplay:function(e,t,n){function r(){f()&&t.Slides.isEnough()&&(b.start(!n.resetProgress),s=u=C=!1,o(),p(bt))}function a(e){void 0===e&&(e=!0),C=!!e,o(),f()||(b.pause(),p(ft))}function i(){C||(u||s?a(!1):r())}function o(){g&&(E(g,on,!C),R(g,Lt,n.i18n[C?"play":"pause"]))}function l(e){var r=t.Slides.getAt(e);b.set(r&&+L(r.slide,An)||n.interval)}var u,s,c=Z(e),m=c.on,d=c.bind,p=c.emit,b=$(n.interval,e.go.bind(e,">"),function(e){var t=_.bar;t&&O(t,"width",100*e+"%"),p(Et,e)}),f=b.isPaused,_=t.Elements,y=t.Elements,N=y.root,g=y.toggle,A=n.autoplay,C="pause"===A;return{mount:function(){A&&(n.pauseOnHover&&d(N,"mouseenter mouseleave",function(e){u="mouseenter"===e.type,i()}),n.pauseOnFocus&&d(N,"focusin focusout",function(e){s="focusin"===e.type,i()}),g&&d(g,"click",function(){C?r():a(!0)}),m([qe,ot,Je],b.rewind),m(qe,l),g&&R(g,Ot,_.track.id),C||r(),o())},destroy:b.cancel,play:r,pause:a,isPaused:f}},Cover:function(e,t,n){function r(e){t.Slides.forEach(function(t){var n=A(t.container||t.slide,"img");n&&n.src&&a(e,n,t)})}function a(e,t,n){n.style("background",e?'center/cover no-repeat url("'+t.src+'")':"",!0),x(t,e?"none":"")}var o=Z(e).on;return{mount:function(){n.cover&&(o(_t,i(a,!0)),o([Ve,et,Je],i(r,!0)))},destroy:i(r,!1)}},Scroll:function(e,t,n){function r(e,n,r,u,m){var d,_,N,C=f();l(),!r||g&&y()||(d=t.Layout.sliderSize(),_=K(e)*d*He(je(e)/d)||0,e=E.toPosition(t.Controller.toDest(e%d))+_),N=V(C,e,1),A=1,n=N?0:n||Fe(je(e-C)/1.5,800),c=u,s=$(n,a,i(o,C,e,m),1),b(5),p(ot),s.start()}function a(){b(3),c&&c(),p(lt)}function o(e,t,a,i){var o,l,u=f(),s=(e+(t-e)*(o=i,(l=n.easingFunc)?l(o):1-Math.pow(1-o,4))-u)*A;N(u+s),g&&!a&&y()&&(A*=.6,je(s)<10&&r(_(y(!0)),600,!1,c,!0))}function l(){s&&s.cancel()}function u(){s&&!s.isPaused()&&(l(),a())}var s,c,m=Z(e),d=m.on,p=m.emit,b=e.state.set,E=t.Move,f=E.getPosition,_=E.getLimit,y=E.exceededLimit,N=E.translate,g=e.is(yn),A=1;return{mount:function(){d(qe,l),d([et,Je],u)},destroy:l,scroll:r,cancel:u}},Drag:function(e,t,n){function r(){var e=n.drag;E(!e),g="free"===e}function a(e){var t,r,a;C=!1,v||(t=b(e),r=e.target,a=n.noDrag,N(r,"."+en+", ."+Qt)||a&&N(r,a)||!t&&e.button||(D.isBusy()?w(e,!0):(T=t?B:window,A=x.is([4,5]),y=null,S(T,fn,i,Cn),S(T,_n,o,Cn),M.cancel(),L.cancel(),s(e))))}function i(t){if(x.is(6)||(x.set(6),R(rt)),t.cancelable)if(A){M.translate(f+c(t)/(G&&e.is(yn)?5:1));var r=m(t)>200,a=G!==(G=j());(r||a)&&s(t),C=!0,R(at),w(t)}else(function(e){return je(c(e))>je(c(e,!0))})(t)&&(A=function(e){var t=n.dragMinThreshold,r=u(t),a=r&&t.mouse||0,i=(r?t.touch:+t)||10;return je(c(e))>(b(e)?i:a)}(t),w(t))}function o(r){x.is(6)&&(x.set(3),R(it)),A&&(function(r){var a=function(t){if(e.is(Nn)||!G){var n=m(t);if(n&&n<200)return c(t)/n}return 0}(r),i=function(e){return H()+K(e)*Ue(je(e)*(n.flickPower||600),g?1/0:t.Layout.listSize()*(n.flickMaxPages||1))}(a),o=n.rewind&&n.rewindByDrag;P(!1),g?D.scroll(i,0,n.snap):e.is(gn)?D.go(F(K(a))<0?o?"<":"-":o?">":"+"):e.is(yn)&&G&&o?D.go(j(!0)?">":"<"):D.go(D.toDest(i),!0),P(!0)}(r),w(r)),O(T,fn,i),O(T,_n,o),A=!1}function l(e){!v&&C&&w(e,!0)}function s(e){y=_,_=e,f=H()}function c(e,t){return p(e,t)-p(d(e),t)}function m(e){return Y(e)-Y(d(e))}function d(e){return _===e&&y||_}function p(e,t){return(b(e)?e.changedTouches[0]:e)["page"+U(t?"Y":"X")]}function b(e){return"undefined"!=typeof TouchEvent&&e instanceof TouchEvent}function E(e){v=e}var f,_,y,g,A,C,v,T,h=Z(e),I=h.on,R=h.emit,S=h.bind,O=h.unbind,x=e.state,M=t.Move,L=t.Scroll,D=t.Controller,B=t.Elements.track,P=t.Media.reduce,k=t.Direction,U=k.resolve,F=k.orient,H=M.getPosition,j=M.exceededLimit,G=!1;return{mount:function(){S(B,fn,xe,Cn),S(B,_n,xe,Cn),S(B,En,a,Cn),S(B,"click",l,{capture:!0}),S(B,"dragstart",w),I([Ve,et],r)},disable:E,isDragging:function(){return A}}},Keyboard:function(e,t,n){function r(){var e=n.keyboard;e&&(l="global"===e?window:p,m(l,Tn,o))}function a(){d(l,Tn)}function i(){var e=u;u=!0,Oe(function(){u=e})}function o(t){if(!u){var n=J(t);n===b(gt)?e.go("<"):n===b(At)&&e.go(">")}}var l,u,s=Z(e),c=s.on,m=s.bind,d=s.unbind,p=e.root,b=t.Direction.resolve;return{mount:function(){r(),c(et,a),c(et,r),c(qe,i)},destroy:a,disable:function(e){u=e}}},LazyLoad:function(e,t,n){function a(){r(y),t.Slides.forEach(function(e){F(e.slide,Rn).forEach(function(t){var r,a,i,o=L(t,hn),l=L(t,In);o===t.src&&l===t.srcset||(r=n.classes.spinner,i=A(a=t.parentElement,"."+r)||S("span",r,a),y.push([t,e,i]),t.src||x(t,"none"))})}),E?s():(d(_),m(_,o),o())}function o(){(y=y.filter(function(t){var r=n.perPage*((n.preloadPages||1)+1)-1;return!t[1].isWithin(e.index,r)||l(t)})).length||d(_)}function l(e){var t=e[0];f(e[1].slide,cn),p(t,"load error",i(u,e)),R(t,"src",L(t,hn)),R(t,"srcset",L(t,In)),I(t,hn),I(t,In)}function u(e,t){var n=e[0],r=e[1];H(r.slide,cn),"error"!==t.type&&(P(e[2]),x(n,""),b(_t,n,r),b(tt)),E&&s()}function s(){y.length&&l(y.shift())}var c=Z(e),m=c.on,d=c.off,p=c.bind,b=c.emit,E="sequential"===n.lazyLoad,_=[Ke,lt],y=[];return{mount:function(){n.lazyLoad&&(a(),m(Je,a))},destroy:i(r,y),check:o}},Pagination:function(e,t,n){function o(){d&&(P(O?a(d.children):d),H(d,p),r(L),d=null),b.destroy()}function l(e){T(">"+e,!0)}function u(e,t){var n,r=L.length,a=J(t),i=s(),o=-1;a===h(At,!1,i)?o=++e%r:a===h(gt,!1,i)?o=(--e+r)%r:"Home"===a?o=0:"End"===a&&(o=r-1),(n=L[o])&&(M(n.button),T(">"+o),w(t,!0))}function s(){return n.paginationDirection||n.direction}function c(e){return L[A.toPage(e)]}function m(){var e,t,n=c(v(!0)),r=c(v());n&&(H(e=n.button,on),I(e,Mt),R(e,Rt,-1)),r&&(f(t=r.button,on),R(t,Mt,!0),R(t,Rt,"")),_(dt,{list:d,items:L},n,r)}var d,p,b=Z(e),E=b.on,_=b.emit,y=b.bind,N=t.Slides,g=t.Elements,A=t.Controller,C=A.hasFocus,v=A.getIndex,T=A.go,h=t.Direction.resolve,O=g.pagination,L=[];return{items:L,mount:function t(){o(),E([et,Je,yt],t);var r=n.pagination;O&&x(O,r?"":"none"),r&&(E([qe,ot,lt],m),function(){var t,r,a,o,c,m=e.length,b=n.classes,E=n.i18n,_=n.perPage,v=C()?A.getEnd()+1:Ye(m/_);for(f(d=O||S("ul",b.pagination,g.track.parentElement),p=Jt+"--"+s()),R(d,It,"tablist"),R(d,Lt,E.select),R(d,Pt,s()===Tt?"vertical":""),t=0;t<v;t++)r=S("li",null,d),a=S("button",{class:b.page,type:"button"},r),o=N.getIn(t).map(function(e){return e.slide.id}),c=!C()&&_>1?E.pageX:E.slideX,y(a,"click",i(l,t)),n.paginationKeyboard&&y(a,"keydown",i(u,t)),R(r,It,"presentation"),R(a,It,"tab"),R(a,Ot,o.join(" ")),R(a,Lt,X(c,t+1)),R(a,Rt,-1),L.push({li:r,button:a,page:t})}(),m(),_(mt,{list:d,items:L},c(e.index)))},destroy:o,getAt:c,update:m}},Sync:function(e,t,n){function a(){var t,n;e.splides.forEach(function(t){t.isParent||(l(e,t.splide),l(t.splide,e))}),m&&((n=(t=Z(e)).on)(Xe,s),n("sk",c),n([Ve,et],u),b.push(t),t.emit(pt,e.splides))}function o(){b.forEach(function(e){e.destroy()}),r(b)}function l(e,t){var n=Z(e);n.on(qe,function(e,n,r){t.go(t.is(Nn)?r:e)}),b.push(n)}function u(){R(t.Elements.list,Pt,n.direction===Tt?"vertical":"")}function s(t){e.go(t.index)}function c(e,t){p(Sn,J(t))&&(s(e),w(t))}var m=n.isNavigation,d=n.slideFocus,b=[];return{setup:i(t.Media.set,{slideFocus:Be(d)?m:d},!0),mount:a,destroy:o,remount:function(){o(),a()}}},Wheel:function(e,t,n){function r(r){if(r.cancelable){var a=r.deltaY,o=a<0,l=Y(r),u=n.wheelMinThreshold||0,s=n.wheelSleep||0;je(a)>u&&l-i>s&&(e.go(o?"<":">"),i=l),function(r){return!n.releaseWheel||e.state.is(4)||-1!==t.Controller.getAdjacent(r)}(o)&&w(r)}}var a=Z(e).bind,i=0;return{mount:function(){n.wheel&&a(t.Elements.track,"wheel",r,Cn)}}},Live:function(e,t,n){function r(e){R(l,Ut,e),e?(_(l,s),c.start()):(P(s),c.cancel())}function a(e){u&&R(l,wt,e?"off":"polite")}var o=Z(e).on,l=t.Elements.track,u=n.live&&!n.isNavigation,s=S("span",rn),c=$(90,i(r,!1));return{mount:function(){u&&(a(!t.Autoplay.isPaused()),R(l,Ft,!0),s.textContent="…",o(bt,i(a,!0)),o(ft,i(a,!1)),o([Ke,lt],i(r,!0)))},disable:a,destroy:function(){I(l,[wt,Ft,Ut]),P(s)}}}}),xn={type:"slide",role:"region",speed:400,perPage:1,cloneStatus:!0,arrows:!0,pagination:!0,paginationKeyboard:!0,interval:5e3,pauseOnHover:!0,pauseOnFocus:!0,resetProgress:!0,easing:"cubic-bezier(0.25, 1, 0.5, 1)",drag:!0,direction:"ltr",trimSpace:!0,focusableNodes:"a, button, textarea, input, select, iframe",live:!0,classes:bn,i18n:{prev:"Previous slide",next:"Next slide",first:"Go to first slide",last:"Go to last slide",slideX:"Go to slide %s",pageX:"Go to page %s",play:"Start autoplay",pause:"Pause autoplay",carousel:"carousel",slide:"slide",select:"Select a slide to show",slideLabel:"%s of %s"},reducedMotion:{speed:0,rewindSpeed:0,autoplay:"pause"}},Mn=function(){function e(t,n){var r,a;this.event=Z(),this.Components={},this.state=(r=1,{set:function(e){r=e},is:function(e){return p(m(e),r)}}),this.splides=[],this._o={},this._E={},G(a=De(t)?U(document,t):t,a+" is invalid."),this.root=a,n=T({label:L(a,Lt)||"",labelledby:L(a,Dt)||""},xn,e.defaults,n||{});try{T(n,JSON.parse(L(a,we)))}catch(e){G(!1,"Invalid JSON")}this._o=Object.create(T({},n))}var t,n,i=e.prototype;return i.mount=function(e,t){var n=this,r=this.state,a=this.Components;return G(r.is([1,7]),"Already mounted!"),r.set(1),this._C=a,this._T=t||this._T||(this.is(gn)?ee:te),this._E=e||this._E,C(v({},On,this._E,{Transition:this._T}),function(e,t){var r=e(n,a,n._o);a[t]=r,r.setup&&r.setup()}),C(a,function(e){e.mount&&e.mount()}),this.emit(Ve),f(this.root,an),r.set(3),this.emit(ze),this},i.sync=function(e){return this.splides.push({splide:e}),e.splides.push({splide:this,isParent:!0}),this.state.is(3)&&(this._C.Sync.remount(),e.Components.Sync.remount()),this},i.go=function(e){return this._C.Controller.go(e),this},i.on=function(e,t){return this.event.on(e,t),this},i.off=function(e){return this.event.off(e),this},i.emit=function(e){var t;return(t=this.event).emit.apply(t,[e].concat(a(arguments,1))),this},i.add=function(e,t){return this._C.Slides.add(e,t),this},i.remove=function(e){return this._C.Slides.remove(e),this},i.is=function(e){return this._o.type===e},i.refresh=function(){return this.emit(Je),this},i.destroy=function(e){void 0===e&&(e=!0);var t=this.event,n=this.state;return n.is(1)?Z(this).on(ze,this.destroy.bind(this,e)):(C(this._C,function(t){t.destroy&&t.destroy(e)},!0),t.emit(ut),t.destroy(),e&&r(this.splides),n.set(7)),this},t=e,(n=[{key:"options",get:function(){return this._o},set:function(e){this._C.Media.set(e,!0,!0)}},{key:"length",get:function(){return this._C.Slides.getLength(!0)}},{key:"index",get:function(){return this._C.Controller.getIndex()}}])&&function(e,t){var n,r;for(n=0;n<t.length;n++)(r=t[n]).enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Mn.defaults={},Mn.STATES={CREATED:1,MOUNTED:2,IDLE:3,MOVING:4,SCROLLING:5,DRAGGING:6,DESTROYED:7},Ln=[[Ve,"onMounted"],[ze,"onReady"],[qe,"onMove"],[Ke,"onMoved"],[Xe,"onClick"],[We,"onActive"],[Qe,"onInactive"],[Ze,"onVisible"],[$e,"onHidden"],[Je,"onRefresh"],[et,"onUpdated"],[tt,"onResize"],[nt,"onResized"],[rt,"onDrag"],[at,"onDragging"],[it,"onDragged"],[ot,"onScroll"],[lt,"onScrolled"],[ut,"onDestroy"],[st,"onArrowsMounted"],[ct,"onArrowsUpdated"],[mt,"onPaginationMounted"],[dt,"onPaginationUpdated"],[pt,"onNavigationMounted"],[bt,"onAutoplayPlay"],[Et,"onAutoplayPlaying"],[ft,"onAutoplayPause"],[_t,"onLazyLoadLoaded"]],Dn=({children:e,className:t,...n})=>Ae().createElement("div",{className:ne("splide__track",t),...n},Ae().createElement("ul",{className:"splide__list"},e)),Bn=class extends Ae().Component{constructor(){super(...arguments),this.splideRef=Ae().createRef(),this.slides=[]}componentDidMount(){const{options:e,extensions:t,transition:n}=this.props,{current:r}=this.splideRef;r&&(this.splide=new Mn(r,e),this.bind(this.splide),this.splide.mount(t,n),this.options=ie({},e||{}),this.slides=this.getSlides())}componentWillUnmount(){this.splide&&(this.splide.destroy(),this.splide=void 0),this.options=void 0,this.slides.length=0}componentDidUpdate(){if(!this.splide)return;const{options:e}=this.props;e&&!ae(this.options,e)&&(this.splide.options=e,this.options=ie({},e));const t=this.getSlides();var n,r;n=this.slides,r=t,(n.length!==r.length||n.some((e,t)=>e!==r[t]))&&(this.splide.refresh(),this.slides=t)}sync(e){var t;null==(t=this.splide)||t.sync(e)}go(e){var t;null==(t=this.splide)||t.go(e)}getSlides(){var e;if(this.splide){const t=null==(e=this.splide.Components.Elements)?void 0:e.list.children;return t&&Array.prototype.slice.call(t)||[]}return[]}bind(e){Ln.forEach(([t,n])=>{const r=this.props[n];"function"==typeof r&&e.on(t,(...t)=>{r(e,...t)})})}omit(e,t){return t.forEach(t=>{Object.prototype.hasOwnProperty.call(e,t)&&delete e[t]}),e}render(){const{className:e,tag:t="div",hasTrack:n=!0,children:r,...a}=this.props;return Ae().createElement(t,{className:ne("splide",e),ref:this.splideRef,...this.omit(a,["options",...Ln.map(e=>e[1])])},n?Ae().createElement(Dn,null,r):r)}};const Ba=(0,Ne.createContext)({trackMarginStyle:{marginLeft:"0px",marginRight:"0px"},trackPaddingStyle:{paddingLeft:"0px",paddingRight:"0px"}}),Pa=({children:e,config:t,id:n,"aria-label":r,"aria-labelledby":a,slideRole:i,slideLabel:o="%s of %s",paginationLabel:l="",paginationRole:u,paginationButtonLabel:s,paginationButtonCurrent:c,hasGradientEffect:m={desktop:!0}})=>{const{mobile:d,tablet:p,desktop:b}=t,E=(0,Ne.useRef)(null),{width:f}=fa(),[_,y]=(0,Ne.useState)({}),[N,g]=(0,Ne.useState)({marginLeft:"0px",marginRight:"0px"}),[A,C]=(0,Ne.useState)({paddingLeft:"0px",paddingRight:"0px"}),v=d?.isActive??!0,T=p?.isActive??!0,h=b?.isActive??!0;(0,Ne.useEffect)(()=>{const{splide:e}=E.current||{},t=E.current?.splide?.index,n=E.current?.splide?.length||0,r="after:brui-absolute after:brui-h-full after:!brui-w-[90px] after:brui-top-0 after:-brui-right-16 sm:after:-brui-right-30 md:after:-brui-right-16 brui-carousel-right-gradient before:brui-absolute before:!brui-h-full before:!brui-top-0 before:-brui-left-16 sm:before:-brui-left-30 md:before:-brui-left-16 brui-carousel-left-gradient before:brui-z-1".split(" ");e&&(window.innerWidth<768?(y(0===t?{left:"16px",right:"32px"}:t===n-1?{left:"32px",right:"16px"}:{left:"24px",right:"24px"}),g({marginLeft:"-16px",marginRight:"-16px"}),m?.mobile?r.map(t=>{e.root?.classList.add(t)}):r.map(t=>{e.root?.classList.remove(t)})):window.innerWidth>=768&&window.innerWidth<992?(y(0===t?{left:"32px",right:"42px"}:t===n-2?{left:"42px",right:"32px"}:{left:"32px",right:"32px"}),g({marginLeft:"-32px",marginRight:"-32px"}),m?.tablet?r.map(t=>{e.root?.classList.add(t)}):r.map(t=>{e.root?.classList.remove(t)})):(y({left:"0px",right:"0px"}),g({marginLeft:"0px",marginRight:"0px"}),m?.desktop?r.map(t=>{e.root?.classList.add(t)}):r.map(t=>{e.root?.classList.remove(t)})))},[f]),(0,Ne.useEffect)(()=>{const e=E.current?.splide;return e&&(I(),e.on("move",()=>{I(),setTimeout(()=>{R()},1)})),()=>{e&&e.off("move")}},[f]),(0,Ne.useEffect)(()=>{const e=E.current?.splide;if(e&&void 0!==i&&""!==i&&(()=>{const t=e.Components?.Elements?.slides;t&&t.forEach(e=>{e.setAttribute("role",i)})})(),e){const t=()=>{if(null!=u&&""!==u){const t=e.Components?.Elements?.pagination;t?.setAttribute("role",u)}},n=()=>{t(),R()};e.on("pagination:mounted",n)}return()=>{e&&e.off("pagination:mounted")}},[f,E]);const I=()=>{const e=E.current?.splide?.index,t=E.current?.splide?.length||0;window.innerWidth<768&&v?C(0===e?{paddingLeft:"16px",paddingRight:"32px"}:e===t-1?{paddingLeft:"32px",paddingRight:"16px"}:{paddingLeft:"24px",paddingRight:"24px"}):window.innerWidth>=768&&window.innerWidth<992&&T?C(0===e?{paddingLeft:"32px",paddingRight:"42px"}:e===t-2?{paddingLeft:"42px",paddingRight:"32px"}:{paddingLeft:"32px",paddingRight:"32px"}):window.innerWidth>=992&&h&&C({paddingLeft:"0px",paddingRight:"0px"})},R=()=>{const e=E.current?.splide;if(e){const t=e.Components?.Elements?.pagination,n=t?.querySelectorAll(".splide__pagination__page");n&&n.forEach((e,t)=>{if(e.removeAttribute("role"),e.removeAttribute("tabindex"),e.removeAttribute("aria-controls"),e.removeAttribute("aria-selected"),e.classList.contains("is-active")&&c&&s){const r=s.replace("{current}",`${t+1}`).replace("{total}",`${n.length}`);e.setAttribute("aria-label",`${r} ${c}`)}else s&&e.setAttribute("aria-label",s.replace("{current}",`${t+1}`).replace("{total}",`${n.length}`))})}};return Ae().createElement(Ba.Provider,{value:{trackMarginStyle:N,trackPaddingStyle:A}},Ae().createElement(Bn,{id:`${n}-parent`,ref:E,className:"brui-h-full","aria-label":r,"aria-labelledby":a,hasTrack:!1,options:{i18n:{slideLabel:o,select:l},padding:_,mediaQuery:"min",destroy:!1,focusableNodes:"button, input",paginationKeyboard:!1,classes:{arrows:"splide__arrows",arrow:"splide__arrow",prev:"splide__arrow--prev !brui-h-40 !brui-w-40 !brui-bg-white !brui-border-solid !brui-border-1 !brui-border-gray-8 !brui-text-blue !brui-border-rounded-60 hover:!brui-border-blue-1 !-brui-left-10 focus:!brui-outline-blue focus:!brui-outline focus:!brui-outline-2 focus:!brui-outline-offset-3 !brui-opacity-100 brui-shadow-6sm bi_brui bi_chevron_left",next:"splide__arrow--next !brui-h-40 !brui-w-40 !brui-bg-white !brui-border-solid !brui-border-1 !brui-border-gray-8 !brui-text-blue !brui-border-rounded-60 hover:!brui-border-blue-1 !-brui-right-10 focus:!brui-outline-blue focus:!brui-outline focus:!brui-outline-2 focus:!brui-outline-offset-3 !brui-opacity-100 brui-shadow-6sm bi_brui bi_chevron",pagination:"splide__pagination",page:"splide__pagination__page !brui-bg-white !brui-block !brui-border-solid !brui-border-2 !brui-border-gray-10 focus:!brui-outline-blue focus:!brui-outline focus:!brui-outline-2 focus:!brui-outline-offset-3 !brui-opacity-100"},breakpoints:{320:{perPage:d?.perPage,perMove:d?.perMove,arrows:d?.arrows,gap:d?.gap,destroy:!v},768:{perPage:p?.perPage,perMove:p?.perMove,arrows:p?.arrows,gap:p?.gap,destroy:!T},992:{perPage:b?.perPage,perMove:b?.perMove,arrows:b?.arrows,gap:b?.gap,destroy:!h}}},onMoved:()=>{m&&(()=>{const{splide:e}=E.current||{},t=E.current?.splide?.index||0,n=E.current?.splide?.length||1,r=E.current?.splide?.options.perPage||1;e&&(e.root.classList.add("before:!brui-w-[90px]","after:!brui-w-[90px]"),0===t&&e.root.classList.remove("before:!brui-w-[90px]"),t+r-1>=n-1&&e.root.classList.remove("after:!brui-w-[90px]"))})()}},e))},ka=()=>Ae().createElement("div",{className:"splide__arrows"}),wa=({children:e,className:t})=>{const{trackMarginStyle:n,trackPaddingStyle:r}=(0,Ne.useContext)(Ba),a={...n,...r};return Ae().createElement("div",{className:["splide__track splide__track-custom",t].join(" ").trim(),style:a},Ae().createElement("div",{className:"splide__list"},e))},Ua=({children:e})=>Ae().createElement("div",{className:"splide__slide"},e),Fa=({className:e})=>Ae().createElement("ul",{className:["splide__pagination",e].join(" ").trim()}),Ha={default:"group-has-[:focus-visible]/inputcheckbox:brui-outline-blue group-has-[:focus-visible]/inputcheckbox:brui-outline group-has-[:focus-visible]/inputcheckbox:brui-outline-2 group-has-[:focus-visible]/inputcheckbox:brui-outline-offset-3",boxedInMobile:"group-has-[:focus-visible]/inputcheckbox:sm:brui-outline-blue group-has-[:focus-visible]/inputcheckbox:sm:brui-outline group-has-[:focus-visible]/inputcheckbox:sm:brui-outline-2 group-has-[:focus-visible]/inputcheckbox:sm:brui-outline-offset-3",alignMiddle:"group-has-[:focus-visible]/inputcheckbox:brui-outline-blue group-has-[:focus-visible]/inputcheckbox:brui-outline group-has-[:focus-visible]/inputcheckbox:brui-outline-2 group-has-[:focus-visible]/inputcheckbox:brui-outline-offset-3"},Ya=(0,Ne.forwardRef)(function({id:e,name:t,value:n,hasError:r=!1,variant:a,...i},o){return Ae().createElement("div",{className:"brui-relative brui-group/inputcheckbox"},Ae().createElement("input",{type:"checkbox",id:e,name:t,value:n,className:"brui-absolute brui-size-full brui-opacity-0 enabled:brui-cursor-pointer disabled:brui-cursor-default brui-z-10",...i,ref:o}),Ae().createElement("div",null,Ae().createElement("div",{className:["brui-size-24 brui-rounded-2 brui-border group-has-[:disabled]/inputcheckbox:brui-bg-white group-has-[:disabled]/inputcheckbox:brui-border-gray-5 group-has-[:checked]/inputcheckbox:brui-bg-blue group-has-[:checked]/inputcheckbox:brui-border-blue group-has-[:checked]/label:brui-font-bold group-has-[:disabled]/inputcheckbox:brui-opacity-40",r?"brui-border-red":"brui-border-gray-2",Ha[a]].join(" ").trim()}),Ae().createElement(Aa,{className:"group-has-[:checked]/inputcheckbox:brui-block brui-absolute brui-hidden brui-text-white group-has-[:checked:disabled]/inputcheckbox:brui-text-gray-7 brui-text-12 brui-transform brui-top-1/2 brui-left-1/2 -brui-translate-x-1/2 -brui-translate-y-1/2 group-has-[:disabled]/inputcheckbox:brui-opacity-40",iconClass:"bi_brui",iconName:"bi_arrow_chekcbox"})))}),ja=({elementType:e,children:t,className:n,role:r,...a})=>{const i=e||"span";return Ae().createElement(i,{className:n,role:r,...a},t)},Ga=({id:e,iconName:t="bi_brui",iconClass:n="bi_error_bl_bg_cf",errorMessage:r,className:a="",show:i=!0})=>i?Ae().createElement("div",{className:["brui-flex brui-items-center brui-mt-10",a].join(" ").trim()},Ae().createElement(Aa,{iconClass:n,iconName:t,className:"brui-text-16 brui-text-red brui-mr-10"}),Ae().createElement(ja,{id:e,elementType:"div",className:"brui-box-border brui-text-red brui-text-12 brui-leading-14"},r)):null,Va=(0,Ne.forwardRef)(function({id:e,name:t,value:n,children:r,variant:a,hasError:i=!1,"aria-describedby":o,errorMessage:l,boldLabelOnCheck:u,...s},c){const m={default:{wrapper:"brui-group/label brui-relative brui-inline-flex brui-w-full brui-items-start brui-content-start brui-border-0",label:"brui-pl-12 brui-text-14 brui-leading-19"},boxedInMobile:{wrapper:i?"brui-group/label brui-relative brui-inline-flex brui-w-full brui-border-2 brui-border-red brui-items-center brui-content-center brui-rounded-6 brui-border-1 sm:brui-border-0 has-[:checked]:sm:brui-border-0 has-[:checked]:brui-border-2 has-[:checked]:brui-border-blue brui-p-15 sm:brui-p-0":"brui-group/label brui-relative brui-inline-flex brui-w-full brui-items-center brui-content-center brui-rounded-6 brui-border-1 sm:brui-border-0 has-[:checked]:sm:brui-border-0 has-[:checked]:brui-border-2 has-[:checked]:brui-border-blue brui-p-15 sm:brui-p-0 brui-border-gray-3 has-[input:focus-visible]:brui-outline has-[input:focus-visible]:brui-outline-2 has-[input:focus-visible]:brui-outline-offset-3 has-[input:focus-visible]:brui-outline-blue has-[input:focus-visible]:sm:brui-outline-0",label:"brui-pl-12 brui-text-14 brui-leading-19"},alignMiddle:{wrapper:"brui-group/label brui-relative brui-inline-flex brui-w-full brui-items-start brui-content-start brui-border-0",label:"brui-pl-12 brui-text-14 brui-leading-19 brui-self-center"}},d=l&&i?`error-${e}`:"",p=(0,Ne.useMemo)(()=>o?[o||"",d].join(" ").trim():d,[o,d]);return Ae().createElement(Ae().Fragment,null,Ae().createElement("div",{className:[m[a].wrapper].join(" ").trim()},Ae().createElement(Ya,{id:e,name:t,value:n,ref:c,hasError:i,"aria-describedby":p||"",variant:a,...s}),Ae().createElement("label",{htmlFor:e,className:["brui-cursor-pointer group-has-[:disabled]/label:brui-cursor-default",m[a].label,u?"group-has-[:checked]/label:brui-font-bold":""].join(" ").trim()},r)),i&&Ae().createElement(Ga,{id:d,iconClass:"vi_warning_c",iconName:"vi_vrui",errorMessage:l||""}))}),za=Va,qa={topRight:"brui-right-24 brui-top-24",topLeft:"sm:brui-top-30 sm:brui-left-30 brui-top-40 brui-left-15",default:"brui-right-24 brui-top-24"},Ka=(0,Ne.forwardRef)(function({checkboxPlacement:e="default",...t},n){return Ae().createElement("div",{className:"brui-group/inputcheckbox brui-absolute brui-right-0 brui-top-0 rui-leading-0 brui-w-full brui-h-full"},Ae().createElement("div",{className:["brui-shadow-4sm group-has-[:checked]/inputcheckbox:brui-shadow-none group-has-[:disabled]/inputcheckbox:brui-shadow-none brui-absolute brui-w-full brui-h-full group-has-[:disabled]/inputcheckbox:brui-border-gray-2 group-has-[:disabled]/inputcheckbox:brui-border-1 group-has-[:checked]/inputcheckbox:brui-border-2 brui-border group-has-[:focus-visible]/inputcheckbox:brui-outline-blue group-has-[:focus-visible]/inputcheckbox:brui-outline group-has-[:focus-visible]/inputcheckbox:brui-outline-2 group-has-[:focus-visible]/inputcheckbox:brui-outline-offset-3 brui-rounded-20 transition-all",t.disabled&&(t.checked||t.defaultChecked)?"":"group-has-[:checked]/inputcheckbox:brui-border-blue"].join(" ").trim()}),Ae().createElement("input",{type:"checkbox",ref:n,...t,className:"brui-absolute brui-left-0 brui-top-0 brui-w-full brui-h-full brui-z-10 brui-opacity-0"}),Ae().createElement("div",{className:["brui-absolute",qa[e]].join(" ").trim()},Ae().createElement("div",{className:["brui-w-24 brui-h-24 brui-rounded-2 brui-border  ",t.disabled&&(t.checked||t.defaultChecked)?"brui-border-gray-7 group-has-[:disabled]/inputcheckbox:brui-bg-white":"brui-border-gray-2 group-has-[:checked]/inputcheckbox:brui-bg-blue group-has-[:checked]/inputcheckbox:brui-border-blue"].join(" ").trim()}),Ae().createElement(Aa,{className:["group-has-[:checked]/inputcheckbox:brui-block brui-absolute brui-hidden brui-text-12 brui-transform brui-top-1/2 brui-left-1/2 -brui-translate-x-1/2 -brui-translate-y-1/2",t.disabled&&(t.checked||t.defaultChecked)?"brui-text-gray-1":"brui-text-white"].join(" ").trim(),iconClass:"bi_brui",iconName:"bi_arrow_chekcbox"})))}),Xa={topRight:"brui-py-32 brui-px-24",topLeft:"brui-p-16 sm:brui-p-32",default:"brui-py-32 brui-px-24"},Wa=(0,Ne.forwardRef)(function({children:e,className:t,"aria-labelledby":n,"aria-describedby":r,name:a,checkboxPlacement:i="topRight",defaultPadding:o,...l},u){return Ae().createElement("div",{className:["brui-group/checkboxcard brui-rounded-20 brui-relative",o?Xa[i]:"",t,l.disabled&&(l.checked||l.defaultChecked)?"brui-bg-gray-3":""].join(" ").trim()},Ae().createElement(Ka,{"aria-labelledby":n,"aria-describedby":r,name:a,ref:u,checkboxPlacement:i,...l}),e)}),Qa=Wa,Za=({children:e,className:t,...n})=>Ae().createElement("div",{className:["brui-mb-24",t].join(" ").trim(),...n},e),$a=({children:e,className:t,...n})=>Ae().createElement("div",{className:["brui-mt-auto",t].join(" ").trim(),...n},e),Ja=({children:e,className:t,...n})=>Ae().createElement("div",{className:["brui-px-16 sm:brui-px-30 md:brui-px-16 lg:brui-container",t].join(" ").trim(),...n},e),ei=({direction:e="horizontal",width:t=2,className:n,style:r,...a})=>Ae().createElement("div",{className:["brui-bg-gray-1","horizontal"===e?"brui-w-full":"brui-flex",n].join(" ").trim(),style:{..."horizontal"===e?{height:t}:{width:t},...r||{}},...a}),ti=(0,Ne.createContext)({visibility:!1}),ni=({children:e})=>{const[t,n]=(0,Ne.useState)(!1),r=(0,Ne.useRef)(null),a=Na(0),i=()=>{if(r.current){const e=r.current.getBoundingClientRect();n(e.top>=0&&e.bottom<=window.innerHeight)}};return(0,Ne.useEffect)(()=>{i()},[a]),(0,Ne.useEffect)(()=>(window.addEventListener("scroll",i),()=>window.removeEventListener("scroll",i)),[]),Ae().createElement(ti.Provider,{value:{visibility:!t}},Ae().createElement("div",{ref:r},e))},ri=({children:e,className:t,...n})=>{const{visibility:r}=(0,Ne.useContext)(ti);return Ae().createElement("div",{className:["brui-fixed brui-left-0 brui-right-0 brui-bottom-0 brui-z-50 "+(r?"":"brui-hidden"),t].join(" ").trim(),...n},e)},ai=({children:e,className:t,...n})=>Ae().createElement("div",{className:t,...n},e),ii={default:"brui-flex-col"},oi=({variant:e="default",className:t,children:n,...r})=>Ae().createElement("div",{className:[ii[e],"brui-flex brui-box-border brui-w-full",t].join(" ").trim(),...r},n),li=(0,Ne.createContext)(void 0),ui=({children:e,hasError:t,errorMessage:n,className:r})=>{const a=(0,Ne.useId)();return Ae().createElement(li.Provider,{value:{formGroupHasError:t,formGroupErrorMessage:n,inputErrorId:a}},Ae().createElement("div",{className:r},e),t&&void 0!==n&&""!==n&&Ae().createElement(Ga,{id:a,iconClass:"bi_error_bl_bg_cf",iconName:"bi_brui",errorMessage:n||""}))},si={xs:"brui-text-18 brui-leading-20",sm:"brui-text-20 brui-leading-22 md:brui-text-22 md:brui-leading-28",md:"brui-text-22 brui-leading-24 sm:brui-text-24 sm:brui-leading-26 md:brui-text-24 lg:brui-text-24 md:brui-leading-[31px]",lg:"brui-text-26 brui-leading-28 -brui-tracking-0.3 sm:brui-text-32 sm:brui-leading-36 sm:-brui-tracking-0.6 md:brui-text-32 md:brui-leading-38",xl:"brui-text-30 brui-leading-36 -brui-tracking-0.4 sm:brui-text-34 sm:brui-leading-40 sm:-brui-tracking-0.6 md:brui-text-40 md:brui-leading-46",default:""},ci=({level:e,variant:t,children:n,className:r,...a})=>Ae().createElement(Ae().Fragment,null,Ae().createElement(e,{className:[si[t],r,"brui-font-bellslim-black"].join(" "),...a},n)),mi=({title:e,subtitle:t,status:n,hideSubtitle:r,children:a,className:i,editButton:o,variant:l="default",disableSrOnlyText:u,autoScrollActiveStep:s=!0,...c})=>{const m="active"===n?{containerStyles:{default:"brui-flex brui-flex-col brui-pt-32 brui-pb-48",leftAlign:"brui-flex brui-flex-col brui-pt-40 brui-pb-24 sm:brui-py-48",leftAlignNoStep:"brui-flex brui-flex-col sm:brui-pt-45 brui-pt-45"},headingTitle:{default:["brui-text-center brui-text-darkblue",i].join(" ").trim(),leftAlign:"",leftAlignNoStep:""},headingSubtitle:{default:"brui-text-14 brui-text-blue",leftAlign:"brui-text-14 brui-text-blue",leftAlignNoStep:"brui-text-14 brui-text-blue"},headingContainer:{default:"brui-flex brui-justify-center",leftAlign:"brui-flex brui-justify-start",leftAlignNoStep:"brui-flex brui-justify-start"}}:"inactive"===n?{containerStyles:{default:"brui-flex brui-flex-col brui-pt-32 brui-pb-48",leftAlign:"brui-flex brui-flex-col brui-py-24 sm:brui-py-48",leftAlignNoStep:"brui-flex brui-flex-col brui-py-45",leftAlignHeading:"brui-flex brui-flex-col"},headingTitle:{default:["brui-text-center brui-text-gray-2",i].join(" ").trim(),leftAlign:"brui-text-gray-7 !brui-text-22 !brui-leading-24",leftAlignNoStep:"brui-text-gray-2 sm:brui-text-32 sm:brui-text-26",leftAlignHeading:"brui-text-gray-2 sm:brui-text-24 sm:brui-text-24 md:brui-text-24 lg:brui-text-24"},headingSubtitle:{default:"brui-text-14 brui-text-gray-2",leftAlign:"brui-text-14 brui-text-gray-7",leftAlignNoStep:"brui-text-14 brui-text-gray-2",leftAlignHeading:"brui-text-14 brui-text-gray-2"},headingContainer:{default:"brui-flex brui-justify-center",leftAlign:"",leftAlignNoStep:""}}:"complete"===n?{containerStyles:{default:"brui-flex brui-flex-col brui-pt-32 brui-pb-48",leftAlign:"brui-flex brui-flex-col brui-py-24 sm:brui-pt-48",leftAlignNoStep:"brui-flex brui-flex-col brui-pt-45 sm:brui-pt-45"},headingTitle:{default:["brui-text-darkblue !brui-text-20 !brui-leading-22 md:!brui-text-22 md:!brui-leading-28",i].join(" ").trim(),leftAlign:"brui-text-gray-2 !brui-text-22 !brui-leading-24",leftAlignNoStep:["brui-text-gray-2 sm:!brui-text-24 !brui-text-26 !brui-leading-28 sm:!brui-leading-26 !brui-tracking-[-0.4px]",i].join(" ").trim()},headingSubtitle:{default:"brui-text-14 brui-text-gray-4",leftAlign:"brui-text-14 brui-text-gray-2",leftAlignNoStep:"brui-text-14 brui-text-gray-2"},headingContainer:{default:"brui-flex brui-justify-between brui-gap-x-10 md:brui-mx-80",leftAlign:"brui-flex brui-justify-between brui-gap-x-10",leftAlignNoStep:"brui-flex brui-justify-between brui-gap-x-10"}}:{containerStyles:{default:"",leftAlign:"",leftAlignNoStep:"",leftAlignHeading:""},headingTitle:{default:"",leftAlign:"",leftAlignNoStep:"",leftAlignHeading:""},headingSubtitle:{default:"",leftAlign:"",leftAlignNoStep:"",leftAlignHeading:""},headingContainer:{default:"",leftAlign:"",leftAlignNoStep:"",leftAlignHeading:""}},d=(0,Ne.useRef)(null);return(0,Ne.useEffect)(()=>{"active"===n&&s&&d.current?.scrollIntoView({behavior:"smooth"})},[n]),Ae().createElement("div",{ref:d,className:m.containerStyles[l]},Ae().createElement("div",{className:m.headingContainer[l]},Ae().createElement(ci,{level:"h2",variant:"lg",className:m.headingTitle[l],...c},Ae().createElement("span",{className:"brui-flex brui-flex-col"},r?null:Ae().createElement(ja,{elementType:"span",className:m.headingSubtitle[l]},t," "),e,u?null:Ae().createElement(Ra,null," ",{active:"(Current Step)",inactive:"(Disabled: Please click the next step button above to proceed)",complete:"(Complete Step)"}[n]))),"complete"===n?o:null),"active"===n||"complete"===n?a:null)},di={solidBlue:"brui-bg-blue-1 brui-text-white brui-border-blue-1 brui-border-2 brui-border-solid brui-rounded-4 hover:brui-bg-blue hover:brui-border-blue focus:brui-border-blue focus:brui-bg-blue focus:brui-outline focus:brui-outline-blue-2 focus:brui-outline-2 focus:brui-outline-offset-2",underline:"",boldLink:"brui-text-blue focus:brui-outline-blue-2 brui-font-bold hover:brui-underline focus:brui-underline brui-inline-block hover:brui-text-blue-1 focus:brui-text-blue-1 !brui-p-5",outlinedBlue:"",solidRed:"brui-font-sans brui-rounded-4 brui-bg-red brui-text-white brui-border-red brui-border-2 brui-border-solid hover:brui-bg-red-1 hover:brui-border-red-1 focus:brui-bg-red-1 focus:brui-border-red-1 focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 brui-inline-block",outlinedBlack:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-darkblue  brui-border-darkblue brui-border-2 brui-border-solid hover:brui-bg-transparent-1 focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 brui-inline-block",solidWhite:"brui-font-sans brui-rounded-4 brui-bg-white brui-text-darkblue brui-border-white brui-border-2 brui-border-solid hover:brui-bg-pink hover:brui-border-pink focus:brui-bg-pink focus:brui-border-pink focus:brui-outline-white focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 brui-inline-block",outlinedWhite:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-white  brui-border-white brui-border-2 brui-border-solid hover:brui-bg-transparent-1 focus:brui-bg-transparent-1 focus:brui-outline-white focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 brui-inline-block",textRed:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-red brui-underline brui-underline-offset-2 hover:brui-text-red-1 hover:brui-no-underline focus:brui-text-red-1 focus:brui-no-underline focus:brui-outline-blue-2 focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 !brui-p-0",textBlue:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-blue brui-underline brui-underline-offset-2 hover:brui-text-blue-1 hover:brui-no-underline focus:brui-text-blue-1 focus:brui-no-underline focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 !brui-p-0",textWhite:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-white brui-underline brui-underline-offset-2 hover:brui-text-pink hover:brui-no-underline focus:brui-text-pink focus:brui-no-underline focus:brui-outline-white focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 !brui-p-0",default:"brui-font-sans brui-rounded-4 brui-bg-transparent brui-text-blue brui-underline brui-underline-offset-2 hover:brui-text-blue-1 hover:brui-no-underline focus:brui-text-blue-1 focus:brui-no-underline focus:brui-outline-blue focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 !brui-p-0"},pi={regular:"brui-text-16 brui-py-8 brui-px-30 brui-leading-17",small:"brui-text-14 brui-py-5 brui-px-14 brui-leading-17",default:""},bi=(0,Ne.forwardRef)(function({variant:e="default",size:t="regular",className:n,href:r,...a},i){const o={linkStyle:[pi[t],di[e],n].join(" ").trim()};return Ae().createElement("a",{className:o.linkStyle,href:r,ref:i,...a})}),Ei=bi,fi=({icon:e,text:t,className:n,position:r,href:a,...i})=>{const o=(0,Ne.useMemo)(()=>"right"===r?Ae().createElement(Ae().Fragment,null,Ae().createElement(ja,{elementType:"span",className:"group-hover:brui-underline brui-mr-8"},t),e):Ae().createElement(Ae().Fragment,null,e,Ae().createElement(ja,{elementType:"span",className:"group-hover:brui-underline brui-ml-8"},t)),[r,e,t]);return Ae().createElement(Ei,{className:["brui-group brui-no-underline",n,"right"===r?"brui-inline-block":"brui-inline-flex brui-items-center"].join(" ").trim(),href:a,...i},o)},_i={default:"brui-bg-white"},yi={defaultInput:"brui-font-normal brui-text-gray-9 brui-rounded brui-border-solid brui-border-2 brui-box-border placeholder:brui-text-gray brui-text-14 brui-leading-18 brui-h-44 brui-w-full brui-py-13 brui-px-10 hover:brui-border-gray focus:brui-border-gray focus:brui-outline-2 focus:brui-outline-blue-2 focus:brui-outline-offset-4 disabled:brui-border-gray-2 disabled:brui-text-gray-2"},Ni=(0,Ne.forwardRef)(function({id:e,variant:t="default",errorMessage:n,className:r,"aria-describedby":a,iconClass:i="bi_error_bl_bg_cf",iconName:o="bi_brui",isError:l,errorMessageClassName:u="",...s},c){const m=n&&l?`error-${e}`:"",d=(0,Ne.useMemo)(()=>a?[a||"",m].join(" ").trim():m,[a,m]);return Ae().createElement("div",{className:"brui-flex brui-flex-col"},Ae().createElement("div",{className:"brui-flex brui-items-center"},Ae().createElement("input",{className:[yi.defaultInput,_i[t],l?"invalid:brui-border-red brui-border-red":"brui-border-gray-7",r].join(" ").trim(),type:"text","aria-describedby":d||"",ref:c,id:e,...s})),l&&n&&Ae().createElement(Ga,{id:m,iconClass:i,iconName:o,errorMessage:n||"",className:u}))}),gi={defaultLabel:"disabled:brui-text-gray-2 brui-font-semibold brui-text-14 brui-leading-18"},Ai=({children:e,required:t,className:n,isError:r,htmlFor:a,overrideClassNames:i,...o})=>{const l=Ae().createElement("span",{className:["brui-mr-8 brui-text-black"].join(" ").trim(),"aria-hidden":"true"},"*");return Ae().createElement(Ae().Fragment,null,Ae().createElement("label",{htmlFor:a,className:i||[gi.defaultLabel,r?"brui-text-red":"brui-text-darkblue",n].join(" ").trim(),...o},t&&l,e))},Ci=({children:e,...t})=>Ae().createElement("li",{...t},e);!function(e){e.ENTER="Enter",e.ESCAPE="Escape",e.SPACE="Space"}(Pn||(Pn={})),kn="data-focus-lock",wn="data-focus-lock-disabled",Un="undefined"!=typeof window?Ne.useLayoutEffect:Ne.useEffect,Fn=new WeakMap,Hn={width:"1px",height:"0px",padding:0,overflow:"hidden",position:"fixed",top:"1px",left:"1px"},Yn=function(){return Yn=Object.assign||function(e){var t,n,r,a;for(n=1,r=arguments.length;n<r;n++)for(a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},Yn.apply(this,arguments)},Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError,jn=ce({},function(e){return{target:e.target,currentTarget:e.currentTarget}}),Gn=ce(),Vn=ce(),zn=function(e){void 0===e&&(e={});var t=se(null);return t.options=Yn({async:!0,ssr:!1},e),t}({async:!0,ssr:"undefined"!=typeof document}),qn=(0,Ne.createContext)(void 0),Kn=[],Xn=Ne.forwardRef(function(e,t){var n,r,a,i,o,l=Ne.useState(),u=l[0],s=l[1],c=Ne.useRef(),m=Ne.useRef(!1),d=Ne.useRef(null),p=Ne.useState({})[1],b=e.children,E=e.disabled,f=void 0!==E&&E,_=e.noFocusGuards,y=void 0!==_&&_,N=e.persistentFocus,g=void 0!==N&&N,A=e.crossFrame,C=void 0===A||A,v=e.autoFocus,T=void 0===v||v,h=(e.allowTextSelection,e.group),I=e.className,R=e.whiteList,S=e.hasPositiveIndices,O=e.shards,x=void 0===O?Kn:O,M=e.as,L=void 0===M?"div":M,D=e.lockProps,B=void 0===D?{}:D,P=e.sideCar,k=e.returnFocus,w=void 0!==k&&k,U=e.focusOptions,F=e.onActivation,H=e.onDeactivation,Y=Ne.useState({})[0],j=Ne.useCallback(function(e){var t,n,r=e.captureFocusRestore;d.current||(n=null==(t=document)?void 0:t.activeElement,d.current=n,n!==document.body&&(d.current=r(n))),c.current&&F&&F(c.current),m.current=!0,p()},[F]),G=Ne.useCallback(function(){m.current=!1,H&&H(c.current),p()},[H]),V=Ne.useCallback(function(e){var t,n,r,a=d.current;a&&(t=("function"==typeof a?a():a)||document.body,(n="function"==typeof w?w(t):w)&&(r="object"==typeof n?n:void 0,d.current=null,e?Promise.resolve().then(function(){return t.focus(r)}):t.focus(r)))},[w]),z=Ne.useCallback(function(e){m.current&&jn.useMedium(e)},[]),q=Gn.useMedium,K=Ne.useCallback(function(e){c.current!==e&&(c.current=e,s(e))},[]),X=oe(((n={})[wn]=f&&"disabled",n[kn]=h,n),B),W=!0!==y,Q=W&&"tail"!==y,Z=(r=[t,K],a=function(e){return r.forEach(function(t){return le(t,e)})},(i=(0,Ne.useState)(function(){return{value:null,callback:a,facade:{get current(){return i.value},set current(e){var t=i.value;t!==e&&(i.value=e,i.callback(e,t))}}}})[0]).callback=a,o=i.facade,Un(function(){var e,t,n,a=Fn.get(o);a&&(e=new Set(a),t=new Set(r),n=o.current,e.forEach(function(e){t.has(e)||le(e,null)}),t.forEach(function(t){e.has(t)||le(t,n)})),Fn.set(o,r)},[r]),o),$=Ne.useMemo(function(){return{observed:c,shards:x,enabled:!f,active:m.current}},[f,m.current,x,u]);return Ne.createElement(Ne.Fragment,null,W&&[Ne.createElement("div",{key:"guard-first","data-focus-guard":!0,tabIndex:f?-1:0,style:Hn}),S?Ne.createElement("div",{key:"guard-nearest","data-focus-guard":!0,tabIndex:f?-1:1,style:Hn}):null],!f&&Ne.createElement(P,{id:Y,sideCar:zn,observed:u,disabled:f,persistentFocus:g,crossFrame:C,autoFocus:T,whiteList:R,shards:x,onActivation:j,onDeactivation:G,returnFocus:V,focusOptions:U,noFocusGuards:y}),Ne.createElement(L,oe({ref:Z},X,{className:I,onBlur:q,onFocus:z}),Ne.createElement(qn.Provider,{value:$},b)),Q&&Ne.createElement("div",{"data-focus-guard":!0,tabIndex:f?-1:0,style:Hn}))}),Xn.propTypes={};const vi=Xn;Wn=function(e){for(var t=Array(e.length),n=0;n<e.length;++n)t[n]=e[n];return t},Qn=function(e){return Array.isArray(e)?e:[e]},Zn=function(e){return Array.isArray(e)?e[0]:e},$n=function(e){return e.parentNode&&e.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE?e.parentNode.host:e.parentNode},Jn=function(e){return e===document||e&&e.nodeType===Node.DOCUMENT_NODE},er=function(e,t){var n,r=e.get(t);return void 0!==r?r:(n=function(e,t){return!e||Jn(e)||!function(e){if(e.nodeType!==Node.ELEMENT_NODE)return!1;var t=window.getComputedStyle(e,null);return!(!t||!t.getPropertyValue||"none"!==t.getPropertyValue("display")&&"hidden"!==t.getPropertyValue("visibility"))}(e)&&!function(e){return e.hasAttribute("inert")}(e)&&t($n(e))}(t,er.bind(void 0,e)),e.set(t,n),n)},tr=function(e,t){var n,r=e.get(t);return void 0!==r?r:(n=function(e,t){return!(e&&!Jn(e))||!!ir(e)&&t($n(e))}(t,tr.bind(void 0,e)),e.set(t,n),n)},nr=function(e){return e.dataset},rr=function(e){return"INPUT"===e.tagName},ar=function(e){return rr(e)&&"radio"===e.type},ir=function(e){var t=e.getAttribute("data-no-autofocus");return![!0,"true",""].includes(t)},or=function(e){var t;return Boolean(e&&(null===(t=nr(e))||void 0===t?void 0:t.focusGuard))},lr=function(e){return!or(e)},ur=function(e){return Boolean(e)},sr=function(e,t){var n=Math.max(0,e.tabIndex),r=Math.max(0,t.tabIndex),a=n-r,i=e.index-t.index;if(a){if(!n)return 1;if(!r)return-1}return a||i},cr=function(e,t,n){return Wn(e).map(function(e,t){var r=function(e){return e.tabIndex<0&&!e.hasAttribute("tabindex")?0:e.tabIndex}(e);return{node:e,index:t,tabIndex:n&&-1===r?(e.dataset||{}).focusGuard?0:-1:r}}).filter(function(e){return!t||e.tabIndex>=0}).sort(sr)},mr=["button:enabled","select:enabled","textarea:enabled","input:enabled","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]","[tabindex]","[contenteditable]","[autofocus]"].join(","),dr="".concat(mr,", [data-focus-guard]"),pr=function(e,t){return Wn((e.shadowRoot||e).children).reduce(function(e,n){return e.concat(n.matches(t?dr:mr)?[n]:[],pr(n))},[])},br=function(e,t){return e.reduce(function(e,n){var r,a=pr(n,t),i=(r=[]).concat.apply(r,a.map(function(e){return function(e,t){var n;return e instanceof HTMLIFrameElement&&(null===(n=e.contentDocument)||void 0===n?void 0:n.body)?br([e.contentDocument.body],t):[e]}(e,t)}));return e.concat(i,n.parentNode?Wn(n.parentNode.querySelectorAll(mr)).filter(function(e){return e===n}):[])},[])},Er=function(e,t){return Wn(e).filter(function(e){return er(t,e)}).filter(function(e){return function(e){return!((rr(e)||function(e){return"BUTTON"===e.tagName}(e))&&("hidden"===e.type||e.disabled))}(e)})},fr=function(e,t){return void 0===t&&(t=new Map),Wn(e).filter(function(e){return tr(t,e)})},_r=function(e,t,n){return cr(Er(br(e,n),t),!0,n)},yr=function(e,t){return cr(Er(br(e),t),!1)},Nr=function(e,t){return e.shadowRoot?Nr(e.shadowRoot,t):!(void 0===Object.getPrototypeOf(e).contains||!Object.getPrototypeOf(e).contains.call(e,t))||Wn(e.children).some(function(e){var n,r;return e instanceof HTMLIFrameElement?!!(r=null===(n=e.contentDocument)||void 0===n?void 0:n.body)&&Nr(r,t):Nr(e,t)})},gr=function(e){if(void 0===e&&(e=document),e&&e.activeElement){var t=e.activeElement;return t.shadowRoot?gr(t.shadowRoot):t instanceof HTMLIFrameElement&&function(){try{return t.contentWindow.document}catch(e){return}}()?gr(t.contentWindow.document):t}},Ar=function(e){return e.parentNode?Ar(e.parentNode):e},Cr=function(e){return Qn(e).filter(Boolean).reduce(function(e,t){var n=t.getAttribute(kn);return e.push.apply(e,n?function(e){var t,n,r,a,i;for(t=new Set,n=e.length,r=0;r<n;r+=1)for(a=r+1;a<n;a+=1)((i=e[r].compareDocumentPosition(e[a]))&Node.DOCUMENT_POSITION_CONTAINED_BY)>0&&t.add(a),(i&Node.DOCUMENT_POSITION_CONTAINS)>0&&t.add(r);return e.filter(function(e,n){return!t.has(n)})}(Wn(Ar(t).querySelectorAll("[".concat(kn,'="').concat(n,'"]:not([').concat(wn,'="disabled"])')))):[t]),e},[])},vr=function(e,t){return void 0===t&&(t=gr(Zn(e).ownerDocument)),!(!t||t.dataset&&t.dataset.focusGuard)&&Cr(e).some(function(e){return Nr(e,t)||function(e,t){return Boolean(Wn(e.querySelectorAll("iframe")).some(function(e){return function(e,t){return e===t}(e,t)}))}(e,t)})},Tr=function(e,t){e&&("focus"in e&&e.focus(t),"contentWindow"in e&&e.contentWindow&&e.contentWindow.focus())},hr=function(e,t){return ar(e)&&e.name?function(e,t){return t.filter(ar).filter(function(t){return t.name===e.name}).filter(function(e){return e.checked})[0]||e}(e,t):e},Ir=function(e){return e[0]&&e.length>1?hr(e[0],e):e[0]},Rr=function(e,t){return e.indexOf(hr(t,e))},Sr="NEW_FOCUS",Or=function(e,t,n){var r,a=e.map(function(e){return e.node}),i=fr(a.filter((r=n,function(e){var t,n=null===(t=nr(e))||void 0===t?void 0:t.autofocus;return e.autofocus||void 0!==n&&"false"!==n||r.indexOf(e)>=0})));return i&&i.length?Ir(i):Ir(fr(t))},xr=function(e,t){return void 0===t&&(t=[]),t.push(e),e.parentNode&&xr(e.parentNode.host||e.parentNode,t),t},Mr=function(e,t){var n,r,a,i;for(n=xr(e),r=xr(t),a=0;a<n.length;a+=1)if(i=n[a],r.indexOf(i)>=0)return i;return!1},Lr=function(e,t,n){var r=Qn(e),a=Qn(t),i=r[0],o=!1;return a.filter(Boolean).forEach(function(e){o=Mr(o||e,e)||o,n.filter(Boolean).forEach(function(e){var t=Mr(i,e);t&&(o=!o||Nr(t,o)?t:Mr(t,o))})}),o},Dr=function(e,t){return e.reduce(function(e,n){return e.concat(function(e,t){return Er((n=e.querySelectorAll("[".concat("data-autofocus-inside","]")),Wn(n).map(function(e){return br([e])}).reduce(function(e,t){return e.concat(t)},[])),t);var n}(n,t))},[])},Br=function(e,t){var n,r,a,i,o,l,u,s,c,m=gr(Qn(e).length>0?document:Zn(e).ownerDocument),d=Cr(e).filter(lr),p=Lr(m||e,e,d),b=new Map,E=yr(d,b),f=E.filter(function(e){var t=e.node;return lr(t)});if(f[0])return i=yr([p],b).map(function(e){return e.node}),n=i,r=f,a=new Map,r.forEach(function(e){return a.set(e.node,e)}),o=n.map(function(e){return a.get(e)}).filter(ur),l=o.map(function(e){return e.node}),u=o.filter(function(e){return e.tabIndex>=0}).map(function(e){return e.node}),s=function(e,t,n,r,a){var i,o,l,u,s,c,m,d,p,b,E,f,_=e.length,y=e[0],N=e[_-1],g=or(r);if(!(r&&e.indexOf(r)>=0))return i=void 0!==r?n.indexOf(r):-1,o=a?n.indexOf(a):i,l=a?e.indexOf(a):-1,-1===i?-1!==l?l:Sr:-1===l?Sr:(c=i-o,m=n.indexOf(y),d=n.indexOf(N),u=n,s=new Set,u.forEach(function(e){return s.add(hr(e,u))}),p=u.filter(function(e){return s.has(e)}),b=(void 0!==r?p.indexOf(r):-1)-(a?p.indexOf(a):i),!c&&l>=0||0===t.length?l:(E=Rr(e,t[0]),f=Rr(e,t[t.length-1]),i<=m&&g&&Math.abs(c)>1?f:i>=d&&g&&Math.abs(c)>1?E:c&&Math.abs(b)>1?l:i<=m?f:i>d?E:c?Math.abs(c)>1?l:(_+l+c)%_:void 0))}(l,u,i,m,t),s===Sr?(c=Or(E,u,Dr(d,b))||Or(E,l,Dr(d,b)))?{node:c}:void 0:void 0===s?s:o[s]},Pr=0,kr=!1,wr=function(e,t,n){void 0===n&&(n={});var r=Br(e,t);if(!kr&&r){if(Pr>2)return kr=!0,void setTimeout(function(){kr=!1},1);Pr++,Tr(r.node,n.focusOptions),Pr--}},Ur=function(e){var t=function(e){if(!e)return null;for(var t=[],n=e;n&&n!==document.body;)t.push({current:pe(n),parent:pe(n.parentElement),left:pe(n.previousElementSibling),right:pe(n.nextElementSibling)}),n=n.parentElement;return{element:pe(e),stack:t,ownerDocument:e.ownerDocument}}(e);return function(){return function(e){var t,n,r,a,i,o,l,u,s,c,m,d,p,b,E,f,_,y,N,g,A;if(e)for(o=e.stack,l=e.ownerDocument,u=new Map,s=0,c=o;s<c.length;s++)if((d=null===(t=(m=c[s]).parent)||void 0===t?void 0:t.call(m))&&l.contains(d)){for(p=null===(n=m.left)||void 0===n?void 0:n.call(m),b=m.current(),E=d.contains(b)?b:void 0,f=null===(r=m.right)||void 0===r?void 0:r.call(m),_=_r([d],u),y=null!==(i=null!==(a=null!=E?E:null==p?void 0:p.nextElementSibling)&&void 0!==a?a:f)&&void 0!==i?i:p;y;){for(N=0,g=_;N<g.length;N++)if(A=g[N],null==y?void 0:y.contains(A.node))return A.node;y=y.nextElementSibling}if(_.length)return _[0].node}}(t)}},Fr=function(e,t,n){var r,a,i;void 0===t&&(t={}),r=function(e){return Object.assign({scope:document.body,cycle:!0,onlyTabbable:!0},e)}(t),a=function(e,t,n){var r,a,i;return e&&t?(r=Qn(t)).every(function(t){return!Nr(t,e)})?{}:(i=(a=n?_r(r,new Map):yr(r,new Map)).findIndex(function(t){return t.node===e}),-1!==i?{prev:a[i-1],next:a[i+1],first:a[0],last:a[a.length-1]}:void 0):{}}(e,r.scope,r.onlyTabbable),a&&(i=n(a,r.cycle))&&Tr(i.node,r.focusOptions)},Hr=function(e,t,n){var r,a,i,o,l=(a=e,i=null===(r=t.onlyTabbable)||void 0===r||r,{first:(o=i?_r(Qn(a),new Map):yr(Qn(a),new Map))[0],last:o[o.length-1]})[n];l&&Tr(l.node,t.focusOptions)},Yr=function(e){return e&&"current"in e?e.current:e},jr=function(){return document&&document.activeElement===document.body},Gr=null,Vr=null,zr=function(){return null},qr=null,Kr=!1,Xr=!1,Wr=function(){return!0},Qr=function e(t,n,r){return n&&(n.host===t&&(!n.activeElement||r.contains(n.activeElement))||n.parentNode&&e(t,n.parentNode,r))},Zr=function(e){return yr(e,new Map)},$r=function(){var e,t,n,r,a,i,o,l,u,s,c,m,d,p,b,E,f,_,y,N,g,A,C=!1;return Gr&&(u=(l=Gr).observed,s=l.persistentFocus,c=l.autoFocus,m=l.shards,d=l.crossFrame,p=l.focusOptions,b=l.noFocusGuards,E=u||qr&&qr.portaledElement,!jr()||!Vr||document.body.contains(Vr)&&Zr([(o=Vr).parentNode]).some(function(e){return e.node===o})||(Vr=null,(f=zr())&&f.focus()),_=document&&document.activeElement,E&&(y=[E].concat(m.map(Yr).filter(Boolean)),_&&!function(e){return(Gr.whiteList||Wr)(e)}(_)||(s||function(){if(!(d?Boolean(Kr):"meanwhile"===Kr)||!b||!Vr||Xr)return!1;var e=Zr(y),t=e.findIndex(function(e){return e.node===Vr});return 0===t||t===e.length-1}()||!(jr()||function(e){void 0===e&&(e=document);var t=gr(e);return!!t&&Wn(e.querySelectorAll("[".concat("data-no-focus-lock","]"))).some(function(e){return Nr(e,t)})}())||!Vr&&c)&&(E&&!(vr(y)||_&&function(e,t){return t.some(function(t){return Qr(e,t,t)})}(_,y)||(i=_,qr&&qr.portaledElement===i))&&(document&&!Vr&&_&&!c?(_.blur&&_.blur(),document.body.focus()):(C=wr(y,Vr,{focusOptions:p}),qr={})),Kr=!1,Vr=document&&document.activeElement,zr=Ur(Vr)),document&&_!==document.activeElement&&document.querySelector("[data-focus-auto-guard]")&&(N=document&&document.activeElement,t=Cr(e=y).filter(lr),n=Lr(e,e,t),r=cr(br([n],!0),!0,!0),a=br(t,!1),g=r.map(function(e){var t=e.node;return{node:t,index:e.index,lockItem:a.indexOf(t)>=0,guard:or(t)}}),A=g.map(function(e){return e.node}).indexOf(N),A>-1&&(g.filter(function(e){var t=e.guard,n=e.node;return t&&n.dataset.focusAutoGuard}).forEach(function(e){return e.node.removeAttribute("tabIndex")}),Ee(A,g.length,1,g),Ee(A,-1,-1,g))))),C},Jr=function(e){$r()&&e&&(e.stopPropagation(),e.preventDefault())},ea=function(){return be($r)},ta=function(){Xr=!0},na=function(){Xr=!1,Kr="just",be(function(){Kr="meanwhile"})},ra={moveFocusInside:wr,focusInside:vr,focusNextElement:function(e,t){void 0===t&&(t={}),Fr(e,t,function(e,t){var n=e.next,r=e.first;return n||t&&r})},focusPrevElement:function(e,t){void 0===t&&(t={}),Fr(e,t,function(e,t){var n=e.prev,r=e.last;return n||t&&r})},focusFirstElement:function(e,t){void 0===t&&(t={}),Hr(e,t,"first")},focusLastElement:function(e,t){void 0===t&&(t={}),Hr(e,t,"last")},captureFocusRestore:Ur},jn.assignSyncMedium(function(e){var t=e.target,n=e.currentTarget;n.contains(t)||(qr={observerNode:n,portaledElement:t})}),Gn.assignMedium(ea),Vn.assignMedium(function(e){return e(ra)});const Ti=(aa=function(e){return e.filter(function(e){return!e.disabled})},ia=function(e){var t,n,r=e.slice(-1)[0];r&&!Gr&&(document.addEventListener("focusin",Jr),document.addEventListener("focusout",ea),window.addEventListener("focus",ta),window.addEventListener("blur",na)),n=(t=Gr)&&r&&r.id===t.id,Gr=r,t&&!n&&(t.onDeactivation(),e.filter(function(e){return e.id===t.id}).length||t.returnFocus(!r)),r?(Vr=null,n&&t.observed===r.observed||r.onActivation(ra),$r(),be($r)):(document.removeEventListener("focusin",Jr),document.removeEventListener("focusout",ea),window.removeEventListener("focus",ta),window.removeEventListener("blur",na),Vr=null)},function(e){function t(){n=aa(o.map(function(e){return e.props})),ia(n)}var n,r,a,i,o=[],l=function(r){function a(){return r.apply(this,arguments)||this}var i,l,u=r;return(i=a).prototype=Object.create(u.prototype),i.prototype.constructor=i,me(i,u),a.peek=function(){return n},(l=a.prototype).componentDidMount=function(){o.push(this),t()},l.componentDidUpdate=function(){t()},l.componentWillUnmount=function(){var e=o.indexOf(this);o.splice(e,1),t()},l.render=function(){return Ae().createElement(e,this.props)},a}(Ne.PureComponent);return r=l,a="displayName",i="SideEffect("+function(e){return e.displayName||e.name||"Component"}(e)+")",(a=function(e){var t=function(e){var t,n;if("object"!=de(e)||!e)return e;if(void 0!==(t=e[Symbol.toPrimitive])){if("object"!=de(n=t.call(e,"string")))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==de(t)?t:t+""}(a))in r?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i,l})(function(){return null});oa=Ne.forwardRef(function(e,t){return Ne.createElement(vi,oe({sideCar:Ti,ref:t},e))}),(la=vi.propTypes||{}).sideCar,function(e,t){var n,r;if(null==e)return{};for(r in n={},e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}}(la,["sideCar"]),oa.propTypes={};const hi=oa;ua=n(514);const Ii=({container:e=document.body,children:t})=>(0,ua.createPortal)(t,e),Ri=(0,Ne.createContext)({variant:"default",ariaLabelledby:""}),Si=({isNestedModal:e=!1,variant:t="default","aria-labelledby":n,"aria-describedby":r,onOverlayClick:a,onEscapeKeyPressed:i,children:o,className:l,focusLockOptions:u={returnFocus:!0,lockProps:{tabIndex:-1}},hasBackdrop:s=!0,modalWidth:c={mobile:"100%",tablet:"645px",desktop:"645px"},...m})=>{const d=n||(0,Ne.useId)(),{width:p}=fa(100),b=(0,Ne.useRef)(null);((e,t)=>{(0,Ne.useEffect)(()=>{if(t)return;const n=document.body.style.overflow;let r;document.body.style.overflow="hidden";const a=e.current;return a&&(r=Re(a)),()=>{document.body.style.overflow=n,r&&r()}},[t])})(b,e),Ea(e=>{e.stopPropagation(),e.key===Pn.ESCAPE&&i?.(e)});const E=(0,Ne.useMemo)(()=>p<768?c.mobile:p>=768&&p<992?c.tablet:c.desktop,[p,c]);return Ae().createElement(Ri.Provider,{value:{variant:t,ariaLabelledby:d,ariaDescribedBy:r}},Ae().createElement(Ii,null,Ae().createElement(hi,{as:"div",...u||{}},Ae().createElement("div",{className:["brui-bg-black-1 brui-transition-opacity brui-overflow-y-auto","brui-h-dvh brui-w-screen","brui-flex brui-items-start","brui-fixed brui-inset-0 brui-m-auto brui-z-[9998]",s?"brui-bg-opacity-50":"brui-bg-opacity-0",l||"brui-justify-center"].join(" ").trim(),onClick:a,role:"dialog","aria-modal":!0,"aria-labelledby":d,tabIndex:-1,...m},Ae().createElement("div",{className:["brui-relative brui-z-10","brui-w-full","brui-flex"].join(" ").trim(),ref:b,role:"document",style:{maxWidth:E}},o)))))},Oi={default:"brui-modal-body brui-flex brui-flex-col brui-items-start brui-bg-white brui-gap-16"},xi=({isDefaultPadding:e=!0,className:t,children:n,...r})=>{const{variant:a}=(0,Ne.useContext)(Ri),i=(0,Ne.useMemo)(()=>({modalBodyStyle:[Oi[a],e&&"brui-py-30 brui-px-15 sm:brui-px-30",t].join(" ").trim()}),[a]);return Ae().createElement("div",{className:i.modalBodyStyle,...r},n)},Mi={default:["brui-min-w-full brui-inline-block brui-bg-white brui-text-left brui-overflow-hidden brui-shadow-xl brui-transform brui-transition-all","brui-flex brui-flex-col","brui-relative brui-opacity-0 sm:brui-align-middle"].join(" ").trim()},Li={top:"brui-self-start",bottom:"brui-self-end",center:"brui-self-center",left:"",right:""},Di={top:"brui-rounded-b-10 -brui-translate-y-[50px]",bottom:"brui-rounded-t-10 brui-translate-y-[50px]",center:"brui-rounded-10 -brui-translate-y-[50px]",centerRadius10:"brui-rounded-10 -brui-translate-y-[50px]",left:"brui-rounded-r-10 brui-h-full",right:"brui-rounded-l-10 brui-h-full"},Bi=({className:e,children:t,verticalAlignment:n={mobile:"bottom",tablet:"center",desktop:"center"},useDefaultRadius:r=!0})=>{const{variant:a}=(0,Ne.useContext)(Ri),{width:i}=fa(100),{verticalAlignmentBreakpoint:o,radiusBreakpoint:l}=((e,t,n,r)=>{const a=(0,Ne.useMemo)(()=>{let a,i;return e<768?(a=t[r.mobile],i=n[r.mobile]):e>=768&&e<992?(a=t[r.tablet],i=n[r.tablet]):(a=t[r.desktop],i=n[r.desktop]),{verticalAlignmentBreakpoint:a,radiusBreakpoint:i}},[e,t,n,r]);return a})(i,Li,Di,n),[u,s]=(0,Ne.useState)(!1),c=(0,Ne.useMemo)(()=>({modalContentStyle:[Mi[a],u?"brui-transition-all brui-duration-300 brui-ease-out brui-delay-50 brui-opacity-100 brui-transform-none":"",o,r?l:"",e].join(" ").trim()}),[a,e,u]);return(0,Ne.useEffect)(()=>{s(!0)},[]),Ae().createElement("div",{className:"brui-flex brui-justify-center brui-w-full "+("center"===n.mobile?"brui-min-h-dvh brui-px-16":"brui-min-h-dvh")},Ae().createElement("div",{onClick:e=>e.stopPropagation(),className:c.modalContentStyle},t))},Pi={variant:{gray:"brui-bg-gray-5",transparent:"",lightGray:"brui-bg-gray-3"}},ki=({variant:e="gray",children:t,className:n,isDefaultPadding:r=!0,...a})=>Ae().createElement("div",{className:["brui-flex sm:brui-gap-32","brui-justify-center sm:brui-justify-start brui-flex-col sm:brui-flex-row",r&&"brui-ps-24 brui-pe-16 brui-py-16 brui-gap-16 sm:brui-px-32 sm:brui-py-24",Pi.variant[e],n].join(" ").trim(),...a},t),wi={variant:{grayBar:"brui-bg-gray-5",transparent:"",none:"",lightGrayBar:"brui-bg-gray-3"}},Ui=({variant:e="transparent",headerIcon:t,title:n,rightButtonIcon:r,onRightButtonClicked:a,rightButtonLabel:i="Close Modal",className:o,children:l,isDefaultPadding:u=!0})=>{const{ariaLabelledby:s}=(0,Ne.useContext)(Ri);return Ae().createElement("div",{className:["brui-modal-header brui-flex brui-flex-col",wi.variant[e],u&&"brui-py-25 brui-px-15 sm:brui-px-30",o].join(" ").trim()},Ae().createElement("div",{className:"brui-flex brui-justify-between brui-gap-16"},Ae().createElement("div",{className:["brui-flex brui-items-start  sm:brui-items-center brui-justify-center sm:brui-justify-start brui-gap-20 brui-flex-col sm:brui-flex-row"].join(" ").trim()},t||null,Ae().createElement(ci,{className:"brui-text-22 brui-leading-24 sm:brui-text-24 sm:brui-leading-26 brui-text-black brui-tracking-[-0.4px]",id:s||"modal=dialog-title",level:"h2",variant:"default"},n)),r?Ae().createElement("div",{className:"brui-pl-30"},Ae().createElement("button",{type:"button","aria-label":i,onClick:()=>a?.(),className:"brui-flex brui-rounded-2 brui-text-blue hover:brui-text-blue-1 focus:!brui-outline-blue focus:!brui-outline focus:brui-outline-2 focus:brui-outline-offset-3",id:`${s}-close-button`||"modal-dialog-close-button"},"default"===r?Ae().createElement(Aa,{className:"brui-text-20",iconClass:"bi_brui",iconName:"bi_close"}):null,Ae().createElement(Ra,null,i))):null),l)},Fi={ordinaryPrice:{divClassName:null,spanClassName:null,forwardSlashClassName:null},defaultPrice:{divClassName:"brui-font-bold brui-text-18 brui-text-blue",spanClassName:null,forwardSlashClassName:null},bigPrice:{divClassName:"brui-font-bellslim-heavy brui-text-28 brui-leading-25 sm:brui-text-40 sm:brui-leading-40 -brui-tracking-1 brui-whitespace-nowrap",spanClassName:"brui-text-16 brui-leading-16 sm:brui-text-20 sm:brui-leading-30 -brui-tracking-0.45 brui-relative brui-top-0 sm:-brui-top-3 brui-align-top",forwardSlashClassName:"brui-text-[11px] brui-leading-16 sm:brui-text-[13px] sm:brui-leading-20 -brui-tracking-0.45 brui-relative brui-top-0 sm:brui-top-3 brui-align-top"},smallPrice:{divClassName:"brui-font-bellslim-heavy brui-text-28 brui-leading-25 -brui-tracking-1 brui-whitespace-nowrap",spanClassName:"brui-text-16 brui-leading-16 -brui-tracking-0.45 brui-relative brui-align-top",forwardSlashClassName:"brui-text-[11px] brui-leading-16 -brui-tracking-0.45 brui-relative brui-align-top"}},Hi={strikeText:{en:"previous price",fr:"précédent Coût"},decimalPointText:{en:".",fr:","},perMonth:{visibleText:{en:"MO.",fr:"MOIS"},screenReaderText:{en:" per month",fr:" par mois"}},perDay:{visibleText:{en:"DAY",fr:"JOUR"},screenReaderText:{en:" per day",fr:" par jour"}}},Yi={CR:{en:"Credit",fr:"Crédit"},"-":{en:"Negative",fr:"Négatif"}},ji=({price:e,variant:t="defaultPrice",strike:n,reverse:r,language:a="en",suffixText:i,className:o,negativeIndicator:l="CR",showZeroDecimalPart:u,srText:s})=>{if(!e&&0!==e)return null;const c=e<0,m=Math.abs(e),d=m.toFixed(2).toString().split("."),p=d[0];let b=d[1]||"";b=parseFloat(p)===m?"00":b,i&&"00"===b&&!u&&(b="");const E=(0,Ne.useMemo)(()=>{let e="",o="",u="";const c="fr"===a,m=n?"brui-line-through":"";let d,E,f;return s?u=`${s} `:null!==l&&(u=`${Yi[l][a]} `),"bigPrice"!==t&&"smallPrice"!==t||(e=c?"":"brui-mr-px",(b||i)&&(o=c?"":"brui-ml-[2px]")),d=r?"brui-text-white":n?"brui-text-gray":"ordinaryPrice"===t?"":"brui-text-darkblue",c?(E=Ae().createElement(Ae().Fragment,null,Ae().createElement("span",{className:m},p),b?Ae().createElement("span",{className:[Fi[t].spanClassName,o,m].join(" ").trim()},Hi.decimalPointText.fr+b):null,Ae().createElement("span",{className:[Fi[t].spanClassName,e].join(" ").trim()}," $")),f=`${p}${b?Hi.decimalPointText.fr+b:""} $`):(E=Ae().createElement(Ae().Fragment,null,Ae().createElement("span",{className:[Fi[t].spanClassName,e].join(" ").trim()},"$"),Ae().createElement("span",{className:m},p),b?Ae().createElement("span",{className:[Fi[t].spanClassName,o,m].join(" ").trim()},Hi.decimalPointText.en+b):null),f=`$${p}${b?Hi.decimalPointText.en+b:""}`),{dollarMargin:e,leftMargin:o,strikeClass:m,priceTextColor:d,priceMarkup:E,priceScreenReaderText:f,negativeText:u}},[p,t,b,i,r,n,a,s]);return Ae().createElement(Ae().Fragment,null,Ae().createElement("div",{className:[Fi[t].divClassName,E.priceTextColor,o].join(" ").trim(),"aria-hidden":"true"},c&&`${l?.toString()} `,E.priceMarkup,i?Ae().createElement(Ae().Fragment,null,Ae().createElement("span",{className:[Fi[t].forwardSlashClassName,E.leftMargin].join(" ").trim()},"/"),Ae().createElement("span",{className:[Fi[t].spanClassName,E.leftMargin].join(" ").trim()},Hi[i].visibleText[a])):null),n&&Ae().createElement("span",{className:"brui-sr-only"},Hi.strikeText[a]," "),Ae().createElement("span",{className:"brui-sr-only"},c&&E.negativeText,E.priceScreenReaderText,i?Hi[i].screenReaderText[a]:null))},Gi={default:"group-has-[:focus-visible]/inputradiobutton:brui-outline-blue group-has-[:focus-visible]/inputradiobutton:brui-outline group-has-[:focus-visible]/inputradiobutton:brui-outline-2 group-has-[:focus-visible]/inputradiobutton:brui-outline-offset-3",boxedInMobile:"group-has-[:focus-visible]/inputradiobutton:sm:brui-outline-blue group-has-[:focus-visible]/inputradiobutton:sm:brui-outline group-has-[:focus-visible]/inputradiobutton:sm:brui-outline-2 group-has-[:focus-visible]/inputradiobutton:sm:brui-outline-offset-3"},Vi=(0,Ne.forwardRef)(function({id:e,name:t,value:n,hasError:r=!1,variant:a,...i},o){return Ae().createElement("div",{className:"brui-relative brui-group/inputradiobutton"},Ae().createElement("input",{type:"radio",id:e,name:t,value:n,className:"brui-absolute brui-size-full brui-opacity-0 enabled:brui-cursor-pointer disabled:brui-cursor-default brui-z-10",...i,ref:o}),Ae().createElement("div",{className:"brui-relative"},Ae().createElement("div",{className:["brui-size-24 brui-rounded-12 brui-border group-has-[:disabled]/inputradiobutton:brui-opacity-40 group-has-[:checked]/inputradiobutton:brui-bg-blue-1  group-has-[:checked]/inputradiobutton:brui-border-blue-1",r?"brui-border-red":"brui-border-gray-2",Gi[a]].join(" ").trim()}),Ae().createElement("div",{className:["brui-scale-0 group-has-[:checked]/inputradiobutton:brui-scale-100 brui-absolute brui-w-12 brui-h-12 brui-bg-white brui-rounded-6 brui-top-1/2 brui-left-1/2 brui-transform -brui-translate-x-1/2 -brui-translate-y-1/2 brui-transition-transform group-has-[:disabled]/inputradiobutton:brui-opacity-40",r?"brui-bg-red":"brui-bg-blue"].join(" ").trim()})))}),zi=(0,Ne.forwardRef)(function({id:e,name:t,value:n,children:r,variant:a,hasError:i=!1,RadioButtonWrapperClass:o,...l},u){const s={default:{wrapper:"brui-group/label brui-relative brui-inline-flex brui-w-full brui-border-0",label:"brui-pl-12 enabled:brui-text-14 enabled:brui-leading-19 brui-cursor-pointer brui-self-center group-has-[:disabled]/label:brui-text-gray-7 group-has-[:disabled]/label:brui-cursor-default group-has-[:checked]/label:brui-font-bold group-has-[:checked:disabled]/label:brui-font-normal"},boxedInMobile:{wrapper:"brui-group/label brui-relative brui-inline-flex brui-w-full brui-rounded-4 brui-border-1 sm:brui-border-0 has-[:checked]:sm:brui-border-0 has-[:checked]:brui-border-2 brui-p-15 sm:brui-p-0 has-[input:focus-visible]:brui-outline has-[input:focus-visible]:brui-outline-2 has-[input:focus-visible]:brui-outline-offset-3 has-[input:focus-visible]:brui-outline-blue-2 has-[input:focus-visible]:sm:brui-outline-0 has-[:checked:disabled]:brui-border-[#BABEC2] brui-shadow-4sm sm:brui-shadow-none"+(i?" brui-border-red has-[:checked]:brui-border-red":" brui-border-[#d7d7d7] has-[:checked]:brui-border-blue"),label:"brui-pl-12 brui-text-14 brui-leading-19 enabled:brui-cursor-pointer group-has-[:checked]/label:brui-font-bold group-has-[:checked:disabled]/label:brui-font-normal brui-self-center group-has-[:disabled]/label:brui-text-gray-7 group-has-[:disabled]/label:brui-cursor-default group-has-[:checked:disabled]/label:brui-font-normal"}};return Ae().createElement(Ae().Fragment,null,Ae().createElement("div",{className:[s[a].wrapper,o].join(" ").trim()},Ae().createElement(Vi,{id:e,name:t,value:n,ref:u,hasError:i,variant:a,...l}),Ae().createElement("label",{htmlFor:e,className:[s[a].label].join(" ").trim()},r)))}),qi=zi,Ki={topRight:"brui-right-24 brui-top-24",topLeft:"brui-top-16 brui-left-16 sm:brui-top-32 sm:brui-left-32",leftCenter:"brui-left-12 brui-transform -brui-translate-y-1/2 brui-top-1/2"},Xi=(0,Ne.forwardRef)(function({radioPlacement:e="topRight",radioPlacementMobile:t,borderRadiusClassName:n,cursorPointer:r=!1,...a},i){const{width:o}=fa(),l=(0,Ne.useMemo)(()=>t&&o<768?t:e,[o]);return Ae().createElement("div",{className:"brui-group/inputradio brui-absolute brui-right-0 brui-top-0 brui-leading-0 brui-w-full brui-h-full"},Ae().createElement("div",{className:["brui-absolute brui-w-full brui-h-full group-has-[:checked]/inputradio:brui-border-2 group-has-[:checked]/inputradio:brui-border-blue group-has-[:focus-visible]/inputradio:brui-outline-blue-2 group-has-[:focus-visible]/inputradio:brui-outline group-has-[:focus-visible]/inputradio:brui-outline-2 group-has-[:focus-visible]/inputradio:brui-outline-offset-3 transition-all brui-shadow-4sm group-has-[:checked]/inputradio:brui-shadow-none",n||"brui-rounded-20"].join(" ").trim()}),Ae().createElement("input",{type:"radio",className:"brui-absolute brui-left-0 brui-top-0 brui-w-full brui-h-full brui-z-10 brui-opacity-0 "+(r?"brui-cursor-pointer":""),ref:i,...a}),Ae().createElement("div",{className:["brui-absolute",Ki[l]].join(" ").trim()},Ae().createElement("div",{className:"brui-w-24 brui-h-24 brui-rounded-12 brui-border brui-border-gray-7 group-has-[:checked]/inputradio:brui-bg-blue-1 group-has-[:checked]/inputradio:brui-border-blue-1"}),Ae().createElement("div",{className:"brui-scale-0 group-has-[:checked]/inputradio:brui-scale-100 brui-absolute brui-w-12 brui-h-12 brui-bg-white brui-rounded-6 brui-top-1/2 brui-left-1/2 brui-transform -brui-translate-x-1/2 -brui-translate-y-1/2 brui-transition-transform"})))}),Wi={topRight:"brui-py-32 brui-px-24",topLeft:"brui-p-16 sm:brui-p-32",leftCenter:"brui-p-12 brui-pl-48"},Qi=(0,Ne.forwardRef)(function({children:e,className:t,"aria-labelledby":n,"aria-describedby":r,name:a,radioPlacement:i="topRight",radioPlacementMobile:o,borderRadiusClassName:l,defaultPadding:u=!0,disabled:s,...c},m){return Ae().createElement("div",{className:["brui-group brui-border brui-relative",u&&Wi[i],l||"brui-rounded-20",t,s&&"brui-bg-gray-5 brui-border-gray-3 brui-opacity-60"].join(" ").trim()},Ae().createElement(Xi,{"aria-labelledby":n,"aria-describedby":r,name:a,ref:m,radioPlacement:i,radioPlacementMobile:o,borderRadiusClassName:l,disabled:s,...c}),e)}),Zi=Qi,$i=({children:e,className:t,...n})=>Ae().createElement("div",{className:["brui-hidden group-has-[:checked]:brui-block",t].join(" ").trim(),...n},e),Ji=({children:e,className:t,...n})=>Ae().createElement("div",{className:["brui-mb-24",t].join(" ").trim(),...n},e),eo=({children:e,className:t,...n})=>Ae().createElement("div",{className:["brui-mt-auto",t].join(" ").trim(),...n},e),to=(0,Ne.createContext)({sameHeightGroups:[],updateSameHeightGroups:()=>{},deleteItem:()=>{}}),no=({children:e})=>{const[t,n]=(0,Ne.useState)([]),r=(0,Ne.useCallback)((e,t)=>{n(n=>n.some(t=>t.groupIndex===e)?n.map(n=>n.groupIndex===e?n.items.some(e=>e.index===t.index)?{...n,items:n.items.map(e=>e.index===t.index?t:e)}:{...n,items:[...n.items,t]}:n):[...n,{groupIndex:e,items:[t]}])},[]);return Ae().createElement(to.Provider,{value:{sameHeightGroups:t,updateSameHeightGroups:r,deleteItem:(e,t)=>{n(n=>n.map(n=>n.groupIndex===e?{...n,items:n.items.filter(e=>e.index!==t)}:n))}}},e)},ro=({children:e,index:t,groupIndex:n})=>{const r=(0,Ne.useRef)(null),[a,i]=(0,Ne.useState)(null),{width:o}=fa(100),l=Na(),[u,s]=(0,Ne.useState)(o<768),c=(0,Ne.useContext)(to);if(!c)return null;const{sameHeightGroups:m,updateSameHeightGroups:d,deleteItem:p}=c;return(0,Ne.useEffect)(()=>{const e=m.find(e=>e.groupIndex===n),t=e?.items.map(e=>e.height);s(o<768),i(t?Math.max(...t):null)},[o,l,m]),(0,Ne.useEffect)(()=>{if(r.current){const e=r.current.offsetHeight;m&&d(n,{index:t,height:e})}return()=>{p(n,t)}},[l]),Ae().createElement("div",{ref:r,style:{minHeight:u?0:a?`${a}px`:"auto"},"data-id":t},e)},ao=(0,Ne.createContext)(null),io=()=>{const e=(0,Ne.useContext)(ao);if(null===e)throw new Error("Select components must be wrapped in <Select />");return e},oo=ao,lo={...ge};lo.useInsertionEffect,"undefined"!=typeof document?Ne.useLayoutEffect:Ne.useEffect,lo.useId;const uo=(0,Ne.forwardRef)(function({options:e,name:t,onChange:n,id:r,...a},i){const{selectedOptionValue:o,selectRef:l}=io(),u=function(e){return Ne.useMemo(()=>e.every(e=>null==e)?null:t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})},e)}([i,l]);return Ae().createElement("select",{id:`hidden-${r}`,ref:u,name:t,value:o,className:"brui-sr-only","aria-hidden":"true",tabIndex:-1,onChange:n,...a},Ae().createElement("option",{value:""}),e.map(e=>Ae().createElement("option",{key:e.id,value:e.value})))}),so="brui-text-left brui-font-normal brui-text-darkblue focus:brui-text-darkblue brui-text-14 brui-leading-18 disabled:hover:brui-bg-gray-5 brui-rounded disabled:brui-border-gray-3 brui-h-44 brui-w-full brui-py-13 brui-px-10 brui-border-2 brui-flex brui-items-center brui-justify-between focus-visible:brui-outline-1 focus-visible:brui-outline-blue-2 focus-visible:brui-outline-offset-4",co=({"aria-controls":e,id:t,"aria-labelledby":n,"aria-describedby":r,className:a,hasError:i,disableDropdownIcon:o,"aria-required":l})=>{const{selectedOptionValue:u,buttonRef:s,options:c,isOpen:m,selectedOptionDisplayName:d,navigatorIndex:p,setIsNavigating:b,setSelectedOptionValue:E,setSelectedOptionDisplayName:f,setIsOpen:_,setNavigatorIndex:y,selectRef:N}=io(),g=e=>{const t=document.getElementById(`${e}`);t&&t.scrollIntoView({behavior:"smooth",block:"nearest"})};return Ae().createElement("button",{id:t,ref:s,type:"button",onClick:()=>{_(!m)},onKeyDown:e=>{let t;if("ArrowDown"===e.key||"ArrowUp"===e.key)e.preventDefault(),_(!0),b(!0),"ArrowDown"===e.key?(t=null===p?0:p+1,y(t>c.length-1?c.length-1:t),g(c[t>c.length-1?c.length-1:t].id)):"ArrowUp"===e.key&&(t=null===p?0:p-1,y(t<0?0:t),g(c[t<0?0:t].id));else if("Enter"===e.key&&m&&null!==p)u!==c[p].value&&(E(c[p].value),f(c[p].displayName),setTimeout(()=>{if(N.current){const e=new Event("change",{bubbles:!0});N.current.dispatchEvent(e)}},0)),b(!1);else if("Escape"===e.key){_(!1),b(!1);const e=c.findIndex(e=>e.value===u);y(e)}},onBlur:()=>{_(!1)},"aria-controls":e,"aria-haspopup":"listbox","aria-expanded":m,"aria-labelledby":n,"aria-describedby":r,"aria-activedescendant":m&&c[p]?c[p].id:void 0,role:"combobox",className:[so,a,i?"brui-border-red focus:brui-border-red":"brui-border-gray-7 focus:brui-text-gray-9"].join(" ").trim(),"aria-required":l},d,Ae().createElement(Aa,{className:["brui-text-15 brui-ml-auto brui-text-blue"].join(" ").trim(),iconClass:"bi_brui",iconName:o?"":m?"bi_small_select_trigger_half_open":"bi_small_select_trigger_half_f"}))},mo="brui-bg-white brui-text-14 brui-leading-18 brui-rounded-b-4 brui-shadow-2sm brui-w-full brui-z-[1001] brui-overflow-y-auto brui-scrollbar-2 brui-border-gray-4 brui-max-h-[230px]",po=({id:e,children:t})=>{const{isOpen:n}=io();return Ae().createElement("div",{id:e,className:"brui-absolute brui-w-full brui-top-[44px] brui-z-[1001]"},Ae().createElement("ul",{role:"listbox",className:[mo,n?"brui-absolute":"brui-hidden"].join(" ").trim()},t))},bo=(0,Ne.forwardRef)(function({defaultValue:e,placeHolder:t,children:n,"aria-labelledby":r,"aria-describedby":a,name:i,id:o,onChange:l,className:u,hasError:s,errorMessage:c,"aria-required":m,disableDropdownIcon:d,dropDownHeight:p,...b},E){const{formGroupHasError:f,formGroupErrorMessage:_,inputErrorId:y}=(0,Ne.useContext)(li)||{},N=void 0!==_?_:c,[g,A]=(0,Ne.useState)([]),[C,v]=(0,Ne.useState)(null),[T,h]=(0,Ne.useState)(e),[I,R]=(0,Ne.useState)(t),[S,O]=(0,Ne.useState)(!1),[x,M]=(0,Ne.useState)(!1),L=(0,Ne.useRef)(null),D=(0,Ne.useRef)(null),B=`dropdown-${(0,Ne.useId)()}`;(0,Ne.useEffect)(()=>{const t=g.findIndex(t=>t.value===e);e&&g.length>0&&t>-1&&(h(g[t].value),v(t),R(g[t].displayName))},[g,e]);const P=(0,Ne.useMemo)(()=>a||y?y?[a||"",y].join(" ").trim():[a||"",`${o}-error`].join(" ").trim():"",[a,y,o]);return Ae().createElement(oo.Provider,{value:{selectedOptionValue:T,selectedOptionDisplayName:I,isOpen:S,buttonRef:L,selectRef:D,isNavigating:x,options:g,navigatorIndex:C,setSelectedOptionValue:h,setSelectedOptionDisplayName:R,setIsOpen:O,setIsNavigating:M,setNavigatorIndex:v,initializeOptions:e=>{A(t=>[...t,e])}}},Ae().createElement("div",{className:"brui-relative"},Ae().createElement(co,{"aria-controls":B,id:o,"aria-labelledby":r,"aria-describedby":P,className:u,hasError:f||s,"aria-required":m,disableDropdownIcon:d}),Ae().createElement(po,{id:B,dropDownHeight:p||void 0},n),Ae().createElement(uo,{options:g,id:o,name:i,onChange:l,ref:E,...b}),void 0===f&&s&&N&&Ae().createElement(Ga,{id:y||`${o}-error`,iconClass:"bi_error_bl_bg_cf",iconName:"bi_brui",errorMessage:N||""})))}),Eo=bo,fo=({displayName:e,value:t,id:n,...r})=>{const{selectedOptionValue:a,buttonRef:i,selectRef:o,isNavigating:l,options:u,navigatorIndex:s,setSelectedOptionValue:c,setSelectedOptionDisplayName:m,setIsOpen:d,setNavigatorIndex:p,initializeOptions:b}=io();(0,Ne.useEffect)(()=>{u.findIndex(e=>e.value===t)<0&&b({value:t,id:n,displayName:e})},[]);const E=["brui-px-18 brui-py-10 hover:brui-bg-gray-1",a===t&&!l||l&&u[s].id===n?"brui-bg-gray-1":""].join(" ").trim();return Ae().createElement("li",{value:t,id:n,onMouseDown:()=>{((e,t)=>{if(a!==e){c(e),m(t);const n=u.findIndex(t=>t.value===e);p(n),setTimeout(()=>{if(o.current){const e=new Event("change",{bubbles:!0});o.current.dispatchEvent(e)}},0)}d(!1),setTimeout(()=>{i.current?.focus()},1)})(t,e)},role:"option",className:E,...r},e)},_o=({text:e,variant:t,className:n,children:r,...a})=>{const i=(0,Ne.useMemo)(()=>({tagStyle:["brui-font-semibold brui-text-12 brui-leading-14 brui-rounded-6 brui-py-4 brui-px-8",{solidRedTag:"brui-bg-red brui-text-white",solidBlueTag:"brui-bg-blue brui-text-white",solidWhiteTag:"brui-bg-white brui-text-red",solidGreyTag:"brui-bg-gray-4 brui-text-white",solidBlackTag:"brui-bg-darkblue brui-text-white",solidGradientDarkBlueTag:"brui-bg-gradient-to-l brui-from-darkblue brui-from-0% brui-to-blue brui-to-100% brui-text-white",outlinedBlackTag:"brui-text-black brui-bg-transparent brui-border-1 brui-border-solid brui-border-black",outlinedWhiteTag:"brui-text-white brui-bg-transparent brui-border-1 brui-border-solid brui-border-white",outlinedRedTag:"brui-text-red brui-bg-transparent brui-border-1 brui-border-solid brui-border-red"}[t],n].join(" ").trim()}),[t]);return Ae().createElement("span",{className:i.tagStyle,...a},e," ",r)},yo=(0,Ne.createContext)({activeTabId:"",updateActiveTabID:()=>{},mode:"manual",isFocusableTabPanel:!1}),No=({mode:e,children:t,isFocusableTabPanel:n,...r})=>{const[a,i]=(0,Ne.useState)("");return Ae().createElement(yo.Provider,{value:{activeTabId:a,updateActiveTabID:e=>{i(e)},mode:e,isFocusableTabPanel:n},...r},t)},go="brui-inline-flex brui-w-full hover:after:brui-content-[''] hover:after:brui-w-full hover:after:brui-h-full hover:after:brui-absolute hover:after:-brui-bottom-0 hover:after:brui-border-solid hover:after:brui-border-b-4 hover:after:brui-border-blue focus:!brui-outline-blue focus:!brui-outline focus:!brui-outline-2 focus:!brui-outline-offset-3 focus:!brui-rounded-6 brui-text-blue",Ao=({id:e,active:t,activeByDefault:n,useDefaultFontStyle:r=!0,className:a,onClick:i,children:o,...l})=>{const{mode:u,activeTabId:s,updateActiveTabID:c}=(0,Ne.useContext)(yo);(0,Ne.useEffect)(()=>{n&&!s&&c(e)},[n,s,c,e]),(0,Ne.useEffect)(()=>{t&&c(e)},[t]);const m=s===e;return Ae().createElement("div",{className:"brui-inline-flex brui-relative brui-mr-24 last:brui-mr-5 brui-py-20 brui-shrink-0"},Ae().createElement("button",{id:e,className:[a,go,m&&"active focus:after:brui-content-[''] focus:after:brui-w-full focus:after:brui-h-full focus:after:brui-absolute focus:after:-brui-bottom-0 focus:after:brui-border-solid focus:after:brui-border-b-4 focus:after:brui-border-blue after:brui-w-full after:brui-h-full after:brui-absolute after:-brui-bottom-0 after:brui-border-solid after:brui-border-b-4 after:brui-border-blue !brui-text-black",r,r&&"brui-text-18 brui-leading-22"].join(" "),onClick:t=>{c(e),i&&i(t)},onKeyDown:e=>{if("ArrowRight"===e.key||"ArrowLeft"===e.key){e.preventDefault();const t=e.currentTarget.parentElement;if(!t)return;const n="ArrowRight"===e.key?t.nextElementSibling:t.previousElementSibling;if(!n)return;const r=Array.from(n.children).filter(e=>"BUTTON"===e.tagName),a=r.find(e=>-1===e.tabIndex);a&&(a.focus(),"automatic"===u&&a.click())}},role:"tab","aria-selected":m?"true":"false",tabIndex:m?0:-1,...l},o))},Co=({className:e,"aria-labelledby":t,children:n})=>{const r=(0,Ne.useRef)(null);return Ae().createElement("div",{className:"brui-relative sm:-brui-mx-5"},Ae().createElement("div",{ref:r,className:"brui-tablist-overflow brui-px-5 brui-relative brui-w-full brui-overflow-x-auto brui-scroll-smooth brui-scrollbar-width-none brui-whitespace-nowrap"},Ae().createElement("div",{className:e,role:"tablist","aria-labelledby":t},n)))},vo=({id:e,tabId:t,children:n,"aria-labelledby":r,activeByDefault:a})=>{const{activeTabId:i,updateActiveTabID:o,isFocusableTabPanel:l}=(0,Ne.useContext)(yo);(0,Ne.useEffect)(()=>{a&&!i&&o(t)},[a,i,o,t]);const u=i===t,s=l?0:void 0;return Ae().createElement("div",{id:e,role:"tabpanel",className:"focus-visible:brui-outline-blue focus-visible:brui-outline focus-visible:brui-outline-2 focus-visible:brui-outline-offset-3 focus-visible:brui-rounded-6 "+(u?"brui-block":"brui-hidden"),"aria-labelledby":r,"data-tabid":t,tabIndex:s},n)},To={Accordion:da,AccordionContent:ga,AccordionIcon:Ca,AccordionItem:ba,AccordionToggleTitle:va,AccordionTrigger:Ta,Alert:Oa,Button:Da,Card:Ia,Carousel:Pa,CarouselArrows:ka,CarouselContent:wa,CarouselItem:Ua,CarouselPagination:Fa,Checkbox:za,CheckboxCard:Qa,CheckboxCardBody:Za,CheckboxCardInput:Ka,CheckboxCardPrice:$a,Container:Ja,Divider:ei,DockBar:ni,Fixed:ri,FormControl:oi,FormGroup:ui,Heading:ci,HeadingStep:mi,Icon:Aa,IconLink:fi,InputError:Ga,InputText:Ni,Label:Ai,Link:Ei,ListItem:Ci,Modal:Si,ModalBody:xi,ModalContent:Bi,ModalFooter:ki,ModalHeader:Ui,Portal:Ii,Price:ji,RadioButton:qi,RadioCard:Zi,DynamicRadioContent:$i,RadioCardBody:Ji,RadioCardInput:Xi,RadioCardPrice:eo,SameHeightGroup:no,SameHeightItem:ro,Select:Eo,SelectContext:oo,SelectCustom:co,SelectDropdown:po,SelectNativeHidden:uo,SelectOption:fo,SrOnly:Ra,Static:ai,Tag:_o,Tab:Ao,Tabs:No,TabList:Co,TabPanel:vo,Text:ja,useHeightResizeObserver:_a,useKeyboardListener:Ea,useResponsiveHeight:ya,useWindowResize:fa,useBodyHeightObserver:Na};return ye})(),e.exports=r(n(1),n(11))},function(e){"use strict";e.exports=u}],d={};return s.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return s.d(t,{a:t}),t},s.d=function(e,t){for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c={},function(){"use strict";function e(e,t){function n(){this.constructor=e}if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");p(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function t(e,t){var n,r,a={};for(n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]]);return a}function n(e,t,n,r){var a,i,o=arguments.length,l=o<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)l=Reflect.decorate(e,t,n,r);else for(i=e.length-1;i>=0;i--)(a=e[i])&&(l=(o<3?a(l):o>3?a(t,n,l):a(t,n))||l);return o>3&&l&&Object.defineProperty(t,n,l),l}function r(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function a(e,t){var n,r,a,i,o="function"==typeof Symbol&&e[Symbol.iterator];if(!o)return e;n=o.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=n.next()).done;)a.push(r.value)}catch(l){i={error:l}}finally{try{r&&!r.done&&(o=n.return)&&o.call(n)}finally{if(i)throw i.error}}return a}function i(e,t,n){if(n||2===arguments.length)for(var r,a=0,i=t.length;a<i;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||Array.prototype.slice.call(t))}function o(e,t,n){if(e===w.Mobility)return t?n.MyBill:n.Mobility;switch(e){case w.OneBill:return n.OneBill;case w.TV:return n.TV;case w.Internet:return n.Internet;case w.HomePhone:return n.HomePhone;case w.MobilityAndOneBill:return n.MobilityAndOneBill;case w.SingleBan:return n.SingleBan;default:return"Unknown"}}function l(e){switch(e){case U.VI:return"VI";case U.MC:return"MC";case U.AX:return"AX";default:return""}}function u(e){switch(e){case U.VI:return"VISA";case U.MC:return"Mastercard";case U.AX:return"American Express";case U.DC:return"Diners Club";default:return"Unknown"}}function m(e,t){var n=t.filter(function(t){return t.Ban===e});return n&&n[0].TransactionId}var d,p,b,E,f,_,y,N,g,A,C,v,T,h,I,R,S,O,x,M,L,D,B,P,k,w,U,F,H,Y,j,G,V,z,q,K,X,W,Q,Z,$,J,ee,te,ne,re,ae,ie,oe,le,ue,se,ce,me,de,pe,be,Ee,fe,_e,ye,Ne,ge,Ae,Ce,ve,Te,he,Ie,Re,Se,Oe,xe,Me,Le,De,Be,Pe,ke,we,Ue,Fe,He,Ye,je,Ge,Ve,ze,qe,Ke,Xe,We,Qe,Ze,$e,Je,et,tt,nt,rt,at,it,ot,lt,ut,st,ct,mt,dt,pt,bt,Et,ft,_t,yt,Nt,gt,At,Ct,vt,Tt,ht,It,Rt,St,Ot,xt,Mt,Lt,Dt,Bt,Pt,kt,wt,Ut,Ft,Ht,Yt,jt,Gt,Vt,zt,qt,Kt,Xt,Wt,Qt,Zt,$t,Jt,en,tn,nn,rn,an,on,ln,un,sn,cn,mn,dn,pn,bn,En,fn,_n,yn,Nn,gn,An,Cn,vn,Tn,hn,In,Rn,Sn,On,xn,Mn,Ln,Dn,Bn,Pn,kn,wn,Un,Fn,Hn,Yn,jn,Gn,Vn,zn,qn,Kn,Xn,Wn,Qn,Zn,$n,Jn,er,tr,nr,rr,ar,ir,or,lr,ur,sr,cr,mr,dr,pr,br,Er,fr,_r,yr,Nr,gr,Ar,Cr,vr,Tr,hr,Ir,Rr,Sr,Or,xr,Mr,Lr,Dr,Br,Pr,kr,wr,Ur,Fr,Hr,Yr,jr,Gr,Vr,zr,qr,Kr,Xr,Wr,Qr,Zr,$r,Jr,ea,ta,na,ra,aa,ia,oa,la,ua,sa,ca,ma;s.r(c),s.d(c,{default:function(){return ca}}),d={},s.r(d),s.d(d,{OmnitureOnApiFailure:function(){return We},OmnitureOnBoxNameLightBox:function(){return ze},OmnitureOnConfirmation:function(){return Ge},OmnitureOnConfirmationFailure:function(){return Ke},OmnitureOnCurrentBalance:function(){return Ye},OmnitureOnFindTransactionLightBox:function(){return Ve},OmnitureOnInteracFailure:function(){return Qe},OmnitureOnLoad:function(){return Fe},OmnitureOnOneTimePaymentFailure:function(){return Xe},OmnitureOnPaymentSelect:function(){return He},OmnitureOnReview:function(){return je},OmnitureOnSecurityCodeLightBox:function(){return qe},cardTokenizationError:function(){return Oe},cardTokenizationSuccess:function(){return xe},clearCardNumber:function(){return Se},createMultiPaymentAction:function(){return Ee},createMultiPaymentCompleted:function(){return fe},createMultiPaymentFailed:function(){return _e},createPaymentAction:function(){return oe},createPaymentCompleted:function(){return le},createPaymentFailed:function(){return ue},fetchPaymentItems:function(){return W},fetchPaymentItemsFailed:function(){return Z},getConfig:function(){return J},getInteracBankInfo:function(){return Be},getPassKey:function(){return he},getRedirectUrl:function(){return Me},interacBankInfoFailure:function(){return ke},interacBankInfoSuccess:function(){return Pe},onCardHolderNameChange:function(){return te},onCreditCardExpiryDateChange:function(){return re},onCreditCardNumberChange:function(){return ee},onSecurityCodeChange:function(){return ne},redirectUrlFailure:function(){return De},redirectUrlSuccess:function(){return Le},resetValidationErrors:function(){return ie},setConfig:function(){return $},setCreditCardInfo:function(){return Re},setInteractBankInfoFailure:function(){return Ue},setIsLoading:function(){return we},setPassKey:function(){return Ie},setPaymentItems:function(){return Q},setValidationErrors:function(){return ae},submitMultiOrderPaymentAction:function(){return Ae},submitMultiOrderPaymentActionCompleted:function(){return Ce},submitMultiOrderPaymentActionFailed:function(){return ve},submitOrderPaymentAction:function(){return de},submitOrderPaymentActionCompleted:function(){return pe},submitOrderPaymentActionFailed:function(){return be},tokenizeAndPropagateFormValues:function(){return Te},validateMultiOrderPaymentAction:function(){return ye},validateMultiOrderPaymentActionCompleted:function(){return Ne},validateMultiOrderPaymentActionFailed:function(){return ge},validateOrderPaymentAction:function(){return se},validateOrderPaymentActionCompleted:function(){return ce},validateOrderPaymentActionFailed:function(){return me}}),p=function(e,t){return p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},p(e,t)},b=function(){return b=Object.assign||function(e){var t,n,r,a;for(n=1,r=arguments.length;n<r;n++)for(a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},b.apply(this,arguments)},Object.create,Object.create,E=function(e){return E=Object.getOwnPropertyNames||function(e){var t,n=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},E(e)},"function"==typeof SuppressedError&&SuppressedError,f=s(1),_=s.n(f),y=s(2),N=s(3),g=s(4),A=s(5),C=N.CommonFeatures.BaseLocalization,v=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return e(r,t),Object.defineProperty(r.prototype,"defaultMessages",{get:function(){return s(6)},enumerable:!1,configurable:!0}),n([N.Injectable],r)}(C),T=N.CommonFeatures.BaseConfig,h=N.CommonFeatures.configProperty,I=function(t){function a(){return null!==t&&t.apply(this,arguments)||this}return e(a,t),n([h("en"),r("design:type",String)],a.prototype,"language",void 0),n([h(N.LoggerSeverityLevel.All),r("design:type",Number)],a.prototype,"logLevel",void 0),n([h("/UXP.Services/eCare/Ordering/Mobility/Onebill/OrderForm/PreAuthorizePayment"),r("design:type",String)],a.prototype,"createPaymentURL",void 0),n([h("/UXP.Services/eCare/Ordering/Mobility/Onebill/OrderMultiForm/PreAuthorizePayment"),r("design:type",String)],a.prototype,"createMultiPaymentURL",void 0),n([h("B"),r("design:type",String)],a.prototype,"brand",void 0),n([h("BELLCAEXT"),r("design:type",String)],a.prototype,"channel",void 0),n([h("ON"),r("design:type",String)],a.prototype,"province",void 0),n([h("b14bc3rcGo"),r("design:type",String)],a.prototype,"userID",void 0),n([h(""),r("design:type",String)],a.prototype,"CSRFToken",void 0),n([h(""),r("design:type",Array)],a.prototype,"getPaymentItem",void 0),n([h(""),r("design:type",String)],a.prototype,"pagetitle",void 0),n([h(""),r("design:type",Object)],a.prototype,"DTSTokenization",void 0),n([h(""),r("design:type",String)],a.prototype,"paymentApiUrl",void 0),n([h(""),r("design:type",Array)],a.prototype,"getBankList",void 0),n([h(""),r("design:type",Array)],a.prototype,"transactionIdArray",void 0),n([h(""),r("design:type",String)],a.prototype,"RedirectUrl",void 0),n([h(""),r("design:type",String)],a.prototype,"BankInfoUrl",void 0),n([h(""),r("design:type",String)],a.prototype,"currentUrl",void 0),n([h(""),r("design:type",Array)],a.prototype,"creditCardAutopayOffers",void 0),n([h(""),r("design:type",Array)],a.prototype,"debitCardAutopayOffers",void 0),n([h(""),r("design:type",String)],a.prototype,"IsInteracEnabled",void 0),n([h(""),r("design:type",String)],a.prototype,"IsSingleClickEnabled",void 0),n([h(""),r("design:type",String)],a.prototype,"IsAutopayCreditEnabled",void 0),n([h(""),r("design:type",String)],a.prototype,"userProfileProvince",void 0),n([h("/"),r("design:type",String)],a.prototype,"flowInitUrl",void 0),n([N.Injectable],a)}(T),R=I,function(e){e[e.Creditcard=0]="Creditcard",e[e.Debit=1]="Debit",e[e.ExistingCreditcard=2]="ExistingCreditcard"}(S||(S={})),function(e){e[e.ChangeCreditCardInfo=0]="ChangeCreditCardInfo",e[e.SwitchToBankAccount=1]="SwitchToBankAccount",e[e.UnEnroll=2]="UnEnroll"}(O||(O={})),function(e){e[e.ChangeBankAccountInfo=0]="ChangeBankAccountInfo",e[e.SwitchToCreditCard=1]="SwitchToCreditCard",e[e.UnEnroll=2]="UnEnroll"}(x||(x={})),function(e){e[e.Regular=0]="Regular",e[e.CreditCard=1]="CreditCard",e[e.PreAuthBank=2]="PreAuthBank",e[e.PreAuthCreditCard=3]="PreAuthCreditCard",e[e.Invoice=4]="Invoice",e[e.ECoupon=5]="ECoupon",e[e.Certificate=6]="Certificate"}(M||(M={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(L||(L={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(D||(D={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(B||(B={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(P||(P={})),function(e){e[e.Unknown=0]="Unknown",e[e.Active=1]="Active",e[e.Suspended=2]="Suspended",e[e.Cancelled=3]="Cancelled"}(k||(k={})),function(e){e[e.OneBill=0]="OneBill",e[e.Mobility=1]="Mobility",e[e.TV=2]="TV",e[e.Internet=3]="Internet",e[e.HomePhone=4]="HomePhone",e[e.MobilityAndOneBill=5]="MobilityAndOneBill",e[e.SingleBan=6]="SingleBan"}(w||(w={})),function(e){e[e.DC=0]="DC",e[e.VI=1]="VI",e[e.MC=2]="MC",e[e.AX=3]="AX"}(U||(U={})),function(e){e[e.OneBill=0]="OneBill",e[e.Mobility=1]="Mobility",e[e.TV=2]="TV"}(F||(F={})),function(e){e[e.Unknown=0]="Unknown",e[e.Success=1]="Success",e[e.Failed=2]="Failed",e[e.NetworkError=3]="NetworkError"}(H||(H={})),function(e){e.CardNumber="CREDIT_CARD_NUMBER",e.CardHolderName="CREDIT_CARD_HOLDER_NAME",e.ExpirationDate="CREDIT_CARD_EXPIRE_DATE",e.SecurityCode="CREDIT_CARD_SECURITY_CODE",e.BankName="BANK_NAME",e.BankAccountHolderName="BANK_ACCOUNT_HOLDER_NAME",e.BankTransitCode="BANK_TRANSIT_CODE",e.BankAccountNumber="BANK_ACCOUNT_NUM",e.ServerValidation="SERVER_VALIDATION"}(Y||(Y={})),function(e){e[e.Default=0]="Default",e[e.SelectBills=1]="SelectBills",e[e.PaymentMethod=2]="PaymentMethod",e[e.CurrentBalance=3]="CurrentBalance",e[e.TermsAndCondition=4]="TermsAndCondition",e[e.Confirmation=5]="Confirmation"}(j||(j={})),function(e){e[e.Default=0]="Default",e.SelectBills="SelectBills",e.PaymentMethod="PaymentMethod",e.CurrentBalance="CurrentBalance",e.TermsAndCondition="TermsAndCondition",e.Confirmation="Confirmation"}(G||(G={})),U.VI,V=function(){},z={CreditCardNumber:"",CreditCardNumberMasked:"",CardholderName:"",ExpireYear:"",ExpireMonth:"",SecurityCode:""},function(e){e.ONCHANGE_CREDITCARD_NUMBER="ONCHANGE_CREDITCARD_NUMBER",e.ONCHANGE_CARDHOLDER_NAME="ONCHANGE_CARDHOLDER_NAME",e.ONCHANGE_EXPIRY_MONTH="ONCHANGE_EXPIRY_MONTH",e.ONCHANGE_EXPIRY_YEAR="ONCHANGE_EXPIRY_YEAR",e.ONCHANGE_EXPIRY_DATE="ONCHANGE_EXPIRY_DATE",e.ONCHANGE_SECURITY_CODE="ONCHANGE_SECURITY_CODE",e.SET_CREDIT_CARD_DEFAULT="SET_CREDIT_CARD_DEFAULT",e.SET_CREDIT_CARD_VALIDATION="SET_CREDIT_CARD_VALIDATION",e.RESET_CREDIT_CARD_VALIDATION="RESET_CREDIT_CARD_VALIDATION"}(q||(q={})),K={PaymentMethod:"",AccountHolder:"",BankName:"",TransitNumber:"",AccountNumber:""},X={cardNumber:"",cardType:"",cardName:"",expiryDate:""},S.Debit,W=(0,A.createAction)("FETCH_PREAUTHORIZED_PAYMENT"),Q=(0,A.createAction)("SET_PREAUTHORIZED_PAYMENT"),Z=(0,A.createAction)("FETCH_PREAUTHORIZED_PAYMENT_FAILED"),$=(0,A.createAction)("SET_CONFIG"),J=(0,A.createAction)("GET_CONFIG"),ee=(0,A.createAction)(q.ONCHANGE_CREDITCARD_NUMBER),te=(0,A.createAction)(q.ONCHANGE_CARDHOLDER_NAME),ne=(0,A.createAction)(q.ONCHANGE_SECURITY_CODE),re=(0,A.createAction)(q.ONCHANGE_EXPIRY_DATE),ae=(0,A.createAction)(q.SET_CREDIT_CARD_VALIDATION),ie=(0,A.createAction)(q.RESET_CREDIT_CARD_VALIDATION),oe=(0,A.createAction)("CREATE_PAYMENT"),le=(0,A.createAction)("CREATE_PAYMENT_COMPLETED"),ue=(0,A.createAction)("CREATE_PAYMENT_FAILED"),se=(0,A.createAction)("VALIDATE_ORDER_PAYMENT"),ce=(0,A.createAction)("VALIDATE_ORDER_PAYMENT_COMPLETED"),me=(0,A.createAction)("VALIDATE_ORDER_PAYMENT_FAILED"),de=(0,A.createAction)("SUBMIT_ORDER_PAYMENT"),pe=(0,A.createAction)("SUBMIT_ORDER_PAYMENT_COMPLETED"),be=(0,A.createAction)("SUBMIT_ORDER_PAYMENT_FAILED"),Ee=(0,A.createAction)("CREATE_MULTI_PAYMENT"),fe=(0,A.createAction)("CREATE_MULTI_PAYMENT_COMPLETED"),_e=(0,A.createAction)("CREATE_MULTI_PAYMENT_FAILED"),ye=(0,A.createAction)("VALIDATE_MULTI_ORDER_PAYMENT"),Ne=(0,A.createAction)("VALIDATE_MULTI_ORDER_PAYMENT_COMPLETED"),ge=(0,A.createAction)("VALIDATE_MULTI_ORDER_PAYMENT_FAILED"),Ae=(0,A.createAction)("SUBMIT_MULTI_ORDER_PAYMENT"),Ce=(0,A.createAction)("SUBMIT_MULTI_ORDER_PAYMENT_COMPLETED"),ve=(0,A.createAction)("SUBMIT_MULTI_ORDER_PAYMENT_FAILED"),Te=(0,A.createAction)("TOKENIZE_AND_PROPAGE_FORM_VALUES"),he=(0,A.createAction)("GET_PASSKEY"),Ie=(0,A.createAction)("SET_PASSKEY"),Re=(0,A.createAction)("SET_STATE",function(e){return e.cardNumberToken&&e.cardNumberToken.length>0?e.cardNumberToken.length>16?e.maskdCardNumber=e.creditCardNumber?e.creditCardNumber.replace(/\d(?=\d{4})/g,"*"):"":e.maskdCardNumber=e.cardNumberToken.replace(/\d(?=\d{4})/g,"*"):e.maskdCardNumber="",e.expiration&&e.expiration.indexOf("/")>-1&&(e.expirationMonth=e.expiration.split("/")[0],e.expirationYear=e.expiration.split("/")[1]),e}),Se=(0,A.createAction)("CLEAR_CARD_NUMBER"),Oe=(0,A.createAction)("TOKENIZATION_ERROR"),xe=(0,A.createAction)("TOKENIZATION_SUCCESS"),Me=(0,A.createAction)("GET_REDIRECT_URL"),Le=(0,A.createAction)("GET_REDIRECT_URL_SUCCESS"),De=(0,A.createAction)("GET_REDIRECT_URL_FAILED"),Be=(0,A.createAction)("GET_INTERAC_BANK_INFO"),Pe=(0,A.createAction)("GET_INTERAC_BANK_INFO_SUCCESS"),ke=(0,A.createAction)("GET_INTERAC_BANK_INFO_FAILED"),we=(0,A.createAction)("SET_IS_LOADING"),Ue=(0,A.createAction)("RESET_FAILED_INTERACT_BANK_INFO"),Fe=(0,A.createAction)("OMNITURE_ON_LOAD"),He=(0,A.createAction)("OMNITURE_ON_PAYMENT_SELECT"),Ye=(0,A.createAction)("OMNITURE_ON_CURRENT_BALANCE"),je=(0,A.createAction)("OMNITURE_ON_REVIEW"),Ge=(0,A.createAction)("OMNITURE_ON_CONFIRMATION"),Ve=(0,A.createAction)("OMNITURE_ON_FIND_TRANSACTION_LIGHT_BOX"),ze=(0,A.createAction)("OMNITURE_ON_BOX_NAME_LIGHT_BOX"),qe=(0,A.createAction)("OMNITURE_ON_SECURITY_CODE_LIGHT_BOX"),Ke=(0,A.createAction)("OMNITURE_ON_CONFIRMATION_FAILURE"),Xe=(0,A.createAction)("OMNITURE_ON_ONE_TIME_PAYMENT_FAILURE"),We=(0,A.createAction)("OMNITURE_ON_VALIDATION_FAILURE"),Qe=(0,A.createAction)("OMNITURE_ON_INTERAC_FAILURE"),function(e){e[e.IDLE=0]="IDLE",e[e.PENDING=1]="PENDING",e[e.COMPLETED=2]="COMPLETED",e[e.FAILED=3]="FAILED"}(Ze||(Ze={})),$e=s(7),Je=function(e,t){return e},et=function(e,t){return t.payload},tt=function(e,t){var n=t.payload;return e&&n?b(b({},e),{CreditCardNumber:n.CreditCardNumber}):e},nt=function(e,t){var n=t.payload;return e&&n?b(b({},e),{CardholderName:n.CardholderName}):e},rt=function(e,t){var n=t.payload;return e&&n?b(b({},e),{SecurityCode:n.SecurityCode}):e},at=function(e,t){var n=t.payload;return e&&n?b(b({},e),{ExpireMonth:n.ExpireMonth,ExpireYear:n.ExpireYear}):e},it=function(e,t){switch(t.type){case q.SET_CREDIT_CARD_VALIDATION:return e.errors.map(function(e){return!!t.payload.errors.some(function(t){return t.field===e.field})}).filter(function(e){return!0===e}).length>0?(e.errors.map(function(e){return t.payload.errors.find(function(t){return t.field===e.field})?b(b({},e),t.payload.errors):e}),e):b(b({},e),{errors:i(i([],a(e.errors),!1),a(t.payload.errors),!1)});case q.RESET_CREDIT_CARD_VALIDATION:return b(b({},e),{errors:[]});default:return e}},ot=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},lt=function(e){return function(t,n){return n.payload,e||Ze.IDLE}},ut=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},st=function(e){return function(t,n){return n.payload,e||Ze.IDLE}},ct=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},mt=function(e){return function(t,n){return n.payload,e||Ze.IDLE}},dt=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},pt=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},bt=function(e,t){return t.payload},Et=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},ft=function(e){return function(t,n){return n.payload,e||Ze.IDLE}},_t=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},yt=function(e){return function(t,n){return n.payload,e||Ze.IDLE}},Nt=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},gt=function(e){return function(t,n){return n.payload,e||Ze.IDLE}},At=function(){return function(e,t){var n=t.payload;return b(b({},e),n)}},Ct=function(e){return function(t,n){return n.payload,e||Ze.IDLE}},vt=s(8),Tt=N.CommonFeatures.BaseClient,ht=function(t){function a(e,n){var r=t.call(this,e)||this;return r.config=n,r}return e(a,t),Object.defineProperty(a.prototype,"options",{get:function(){return{cache:!1,credentials:"include",headers:{"Content-Type":"application/json",brand:this.config.brand,channel:this.config.channel,Province:this.config.province,userID:this.config.userID,"accept-language":this.config.language,"X-CSRF-TOKEN":this.config.CSRFToken}}},enumerable:!1,configurable:!0}),Object.defineProperty(a.prototype,"optionsOneBill",{get:function(){return{cache:!1,credentials:"include",headers:{"Content-Type":"application/json",brand:this.config.brand,channel:this.config.channel,Province:this.config.province,userID:this.config.userID,"accept-language":this.config.language,"X-CSRF-TOKEN":this.config.CSRFToken,PM:!0}}},enumerable:!1,configurable:!0}),a.prototype.createMultiOrderFormData=function(e,t,n,r){var a=t?this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(null)):this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(r)),i=this.getBanSpecificTransactionId(e),o=a+"?TransactionId=".concat(i,"&province=").concat(this.config.province),l=b({},t?this.optionsOneBill:this.options);return this.post(o,{AccountInputValues:n},l)},a.prototype.validateMultiOrderForm=function(e,t,n,r,a,i,o){var l=t?this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(null)):this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(i)),u=this.getBanSpecificTransactionId(e),s=l+"/ValidatePayment?TransactionId=".concat(u,"&province=").concat(this.config.province),c=b({},t?this.optionsOneBill:this.options);return this.post(s,a?{AccountInputValues:r,ValidatePADPACInput:{SelectedPaymentMethod:n.SelectedPaymentMethod,BankName:n.BankName,AccountNumber:n.AccountNumber,HolderName:n.HolderName,BankCode:n.BankCode,TransitCode:n.TransitCode}}:{AccountInputValues:r,ValidatePADPACInput:{SelectedPaymentMethod:n.SelectedPaymentMethod,CardholderName:n.CardholderName,CreditCardToken:o,CreditCardType:n.CreditCardType,ExpiryYear:n.ExpiryYear,ExpiryMonth:n.ExpiryMonth,SecurityCode:n.SecurityCode}},c)},a.prototype.submitMultiOrderForm=function(e,t,n,r,a,i,o){var l,u,s,c=t?this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(null)):this.config.createMultiPaymentURL.replace("Onebill","".concat(e,"/").concat(o)),m=this.getBanSpecificTransactionId(e),d="".concat(c,"/Submit?TransactionId=").concat(m,"&province=").concat(this.config.province),p=b({},t?this.optionsOneBill:this.options),E=n,f=E?a:r,_=E?this.config.debitCardAutopayOffers:this.config.creditCardAutopayOffers,y=E?"D":"C";if(i&&Array.isArray(i))for(u=function(e){var t,n=i[e];f?n.incentiveDiscountDetails=[]:(t=(null!==(l=null==_?void 0:_.filter(function(e){return e.Ban===n.accountNumber}))&&void 0!==l?l:[]).reduce(function(e,t){var n;return e.concat(null!==(n=t.AutopayEligibleSubscribers)&&void 0!==n?n:[])},[]).map(function(e){var t,n,r;return{mdn:null===(t=e.subscriberTelephoneNumber)||void 0===t?void 0:t.replace(/\D/g,""),autopayOffers:null!==(r=null===(n=e.autopayOffers)||void 0===n?void 0:n.map(function(e){var t,n,r;return{newDiscountAmount:null!==(t=e.currentdiscountAmount)&&void 0!==t?t:0,currentDiscountAmount:null!==(n=e.discountAmount)&&void 0!==n?n:0,offerImpact:null!==(r=e.action)&&void 0!==r?r:""}}))&&void 0!==r?r:[]}}),n.incentiveDiscountDetails=[{autopayEligibleSubscribers:t,selectedPaymentMethod:y}])},s=0;s<i.length;s++)u(s);return this.post(d,{AccountInputValues:i},p)},a.prototype.getPassKeyRepsonse=function(e){var t=this.config.paymentApiUrl+"".concat(e.payload.ban,"/").concat(e.payload.sub,"/payment/CreditCard/PassKey"),n=b({},this.options);return this.get(t,null,n)},a.prototype.getBanSpecificTransactionId=function(e){var t=this.config.transactionIdArray.filter(function(t){return t.Ban===e});return t&&t[0].TransactionId},a.prototype.getRedirectUrl=function(){var e=this.config.RedirectUrl,t=b({},this.options);return this.post(e,{OneTimeCode:"",RedirectUrl:this.config.currentUrl},t)},a.prototype.getInteracBankInfo=function(e){var t=this.config.BankInfoUrl,n=b({},this.options);return this.post(t,{RedirectUrl:this.config.currentUrl,OneTimeCode:e},n)},a.prototype.createOrderFormData=function(e,t,n){var r=t?this.config.createPaymentURL.replace("Onebill","".concat(e)):this.config.createPaymentURL.replace("Onebill","".concat(e,"/").concat(n)),a=this.getBanSpecificTransactionId(e),i=r+"?TransactionId=".concat(a,"&province=").concat(this.config.province),o=b({},t?this.optionsOneBill:this.options);return this.post(i,null,o)},a.prototype.validateOrderForm=function(e,t,n,r,a,i){var o=t?this.config.createPaymentURL.replace("Onebill","".concat(e)):this.config.createPaymentURL.replace("Onebill","".concat(e,"/").concat(a)),l=this.getBanSpecificTransactionId(e),u=o+"/ValidatePayment?TransactionId=".concat(l,"&province=").concat(this.config.province),s=b({},t?this.optionsOneBill:this.options);return this.post(u,r?{SelectedPaymentMethod:n.SelectedPaymentMethod,BankName:n.BankName,AccountNumber:n.AccountNumber,HolderName:n.HolderName,BankCode:n.BankCode,TransitCode:n.TransitCode}:{SelectedPaymentMethod:n.SelectedPaymentMethod,CardholderName:n.CardholderName,CreditCardToken:i,CreditCardType:n.CreditCardType,ExpiryYear:n.ExpiryYear,ExpiryMonth:n.ExpiryMonth,SecurityCode:n.SecurityCode},s)},a.prototype.submitOrderForm=function(e,t,n){var r=t?this.config.createPaymentURL.replace("Onebill","".concat(e)):this.config.createPaymentURL.replace("Onebill","".concat(e,"/").concat(n)),a=this.getBanSpecificTransactionId(e),i=r+"/Submit?TransactionId=".concat(a,"&province=").concat(this.config.province),o=b({},t?this.optionsOneBill:this.options);return this.post(i,null,o)},n([N.Injectable,r("design:paramtypes",[N.AjaxServices,R])],a)}(Tt),It=function(){function e(e,t){this.client=e,this.config=t}return e.prototype.combineEpics=function(){return(0,$e.combineEpics)(this.createPaymentEpic,this.validateOrderPaymentEpic,this.submitOrderPaymentEpic,this.createMultiPaymentEpic,this.validateMultiOrderPaymentEpic,this.submitMultiOrderPaymentEpic,this.tokenizeAndPropagateFormValues,this.fetchPassKey,this.getRedirectUrl,this.getInteracBankInfo)},Object.defineProperty(e.prototype,"tokenizeAndPropagateFormValues",{get:function(){var e=this;return function(t,n){return t.ofType(Te.toString()).mergeMap(function(t){return(r=e.config.DTSTokenization,a=n.getState().passKey,i=new DTSTokenizationPlugin,o=r.consumerId,l=r.applicationId,u=r.systemTransactionID,s=r.userID,c=r.timeout,i.setUserID(s),i.setSystemTransactionID(u),i.setApplicationID(l),i.setConsumerID(o),i.setPassKey(a),i.setPanElementID("card-number"),i.setTimeout(c),vt.Observable.create(function(e){i.setSuccessHandler(function(t,n){return e.next(t),e.complete()}),i.setErrorHandler(function(t){return e.error(t),e.complete()}),i.tokenize()})).mergeMap(function(e){return[he({ban:t.payload.BillName,sub:t.payload.subscriberId}),xe(e.token)]}).catch(function(e){return[Oe("string"==typeof e&&e.length>0?e:"TOKENIZATIONERROR")]});var r,a,i,o,l,u,s,c}).catch(function(e){return[Oe("TOKENIZATIONERROR")]})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fetchPassKey",{get:function(){var e=this;return function(t,n){return t.ofType(he.toString()).mergeMap(function(t){return e.client.getPassKeyRepsonse(t).map(function(e){var t;return Ie(null===(t=null==e?void 0:e.data)||void 0===t?void 0:t.PassKey)})}).catch(function(e){return[Oe("TOKENIZATIONERROR")]})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"createMultiPaymentEpic",{get:function(){var e=this;return function(t,n){return t.ofType(Ee.toString()).mergeMap(function(t){var n=t.payload;return e.client.createMultiOrderFormData(null==n?void 0:n.ban,null==n?void 0:n.type,null==n?void 0:n.details,null==n?void 0:n.sub).map(function(e){var t=e.data;return fe(t)}).catch(function(e){return vt.Observable.of(b(b({},_e(e)),{error:!0}))})})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"validateMultiOrderPaymentEpic",{get:function(){var e=this;return function(t,n){return t.ofType(ye.toString()).mergeMap(function(t){var r=t.payload;return e.client.validateMultiOrderForm(null==r?void 0:r.ban,null==r?void 0:r.type,null==r?void 0:r.details,null==r?void 0:r.accountInputValue,null==r?void 0:r.isBankPaymentSelected,null==r?void 0:r.sub,n.getState().cardTokenizationSuccess).map(function(e){var t=e.data;return Ne(t)}).catch(function(e){return vt.Observable.of(b(b({},ge(e)),{error:!0}))})})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"submitMultiOrderPaymentEpic",{get:function(){var e=this;return function(t,n){return t.ofType(Ae.toString()).mergeMap(function(t){var n=t.payload;return e.client.submitMultiOrderForm(null==n?void 0:n.ban,null==n?void 0:n.type,null==n?void 0:n.isbankSelected,null==n?void 0:n.sorryCredit,null==n?void 0:n.sorryDebit,null==n?void 0:n.details,null==n?void 0:n.sub).map(function(e){var t=e.data;return t.length>0&&t.find(function(e){return"Confirmation"===e.OrderFormStatus})?Ce(t):ve({error:!0})}).catch(function(e){return vt.Observable.of(b(b({},ve(e)),{error:!0}))})})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"getRedirectUrl",{get:function(){var e=this;return function(t,n){return t.ofType(Me.toString()).mergeMap(function(t){return e.client.getRedirectUrl().map(function(e){var t=e.data;return Le(t)}).catch(function(e){return vt.Observable.of(b(b({},De(e)),{error:!0}))})})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"getInteracBankInfo",{get:function(){var e=this;return function(t,n){return t.ofType(Be.toString()).mergeMap(function(t){var r=t.payload;return n.dispatch(we(!0)),e.client.getInteracBankInfo(null==r?void 0:r.code).map(function(e){var t=e.data;return n.dispatch(we(!1)),Pe(t)}).catch(function(e){return n.dispatch(we(!1)),vt.Observable.of(b(b({},ke(e)),{error:!0}))})})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"createPaymentEpic",{get:function(){var e=this;return function(t,n){return t.ofType(oe.toString()).mergeMap(function(t){var n=t.payload;return e.client.createOrderFormData(null==n?void 0:n.ban,null==n?void 0:n.type,null==n?void 0:n.sub).map(function(e){var t=e.data;return le(t)}).catch(function(e){return vt.Observable.of(b(b({},ue(e)),{error:!0}))})})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"validateOrderPaymentEpic",{get:function(){var e=this;return function(t,n){return t.ofType(se.toString()).mergeMap(function(t){var r=t.payload;return e.client.validateOrderForm(null==r?void 0:r.ban,null==r?void 0:r.type,null==r?void 0:r.details,null==r?void 0:r.isBankPaymentSelected,null==r?void 0:r.sub,n.getState().cardTokenizationSuccess).map(function(e){var t=e.data;return ce(t)}).catch(function(e){return vt.Observable.of(b(b({},me(e)),{error:!0}))})})}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"submitOrderPaymentEpic",{get:function(){var e=this;return function(t,n){return t.ofType(de.toString()).mergeMap(function(t){var n=t.payload;return e.client.submitOrderForm(null==n?void 0:n.ban,null==n?void 0:n.type,null==n?void 0:n.sub).map(function(e){var t=e.data;return pe(t)}).catch(function(e){return vt.Observable.of(b(b({},be(e)),{error:!0}))})})}},enumerable:!1,configurable:!0}),n([N.Injectable,r("design:paramtypes",[ht,R])],e)}(),Rt=N.CommonFeatures.BaseStore,St=(0,N.CommonFeatures.actionsToComputedPropertyName)(d),Ot=St.setConfig,xt=St.getConfig,Mt=St.onCreditCardNumberChange,Lt=St.onCardHolderNameChange,Dt=St.onCreditCardExpiryDateChange,Bt=St.onSecurityCodeChange,Pt=St.setValidationErrors,kt=St.resetValidationErrors,wt=St.createPaymentAction,Ut=St.createPaymentCompleted,Ft=St.createPaymentFailed,Ht=St.validateOrderPaymentAction,Yt=St.validateOrderPaymentActionCompleted,jt=St.validateOrderPaymentActionFailed,Gt=St.submitOrderPaymentAction,Vt=St.submitOrderPaymentActionCompleted,zt=St.submitOrderPaymentActionFailed,qt=St.createMultiPaymentAction,Kt=St.createMultiPaymentCompleted,Xt=St.createMultiPaymentFailed,Wt=St.validateMultiOrderPaymentAction,Qt=St.validateMultiOrderPaymentActionCompleted,Zt=St.validateMultiOrderPaymentActionFailed,$t=St.submitMultiOrderPaymentAction,Jt=St.submitMultiOrderPaymentActionCompleted,en=St.submitMultiOrderPaymentActionFailed,tn=St.setPassKey,nn=St.cardTokenizationError,rn=St.cardTokenizationSuccess,an=St.tokenizeAndPropagateFormValues,on=St.redirectUrlSuccess,ln=St.interacBankInfoSuccess,un=St.setIsLoading,sn=St.interacBankInfoFailure,cn=function(t){function a(e,n,r,a){var i=t.call(this,e)||this;return i.config=n,i.localization=r,i.epics=a,i}return e(a,t),Object.defineProperty(a.prototype,"reducer",{get:function(){var e,t,n,r,a,i,o,l,u,s,c,m,d,p,b,E,f,_,y,N,C,v,T;return(0,g.combineReducers)({localization:this.localization.createReducer(),config:(0,A.handleActions)((e={},e[Ot]=et,e[xt]=Je,e),this.config),creditCardDetails:(0,A.handleActions)((t={},t[Mt]=tt,t[Lt]=nt,t[Bt]=rt,t[Dt]=at,t),z),validationErrors:(0,A.handleActions)((n={},n[Pt]=it,n[kt]=it,n),{errors:[]}),createPaymentStatus:(0,A.handleActions)((r={},r[wt]=lt(Ze.PENDING),r[Ft]=lt(Ze.FAILED),r[Ut]=lt(Ze.COMPLETED),r),Ze.IDLE),createPayment:(0,A.handleActions)((a={},a[Ut]=ot(),a),{}),validateOrderFormStatus:(0,A.handleActions)((i={},i[Ht]=st(Ze.PENDING),i[jt]=st(Ze.FAILED),i[Yt]=st(Ze.COMPLETED),i),Ze.IDLE),validateOrderPayment:(0,A.handleActions)((o={},o[Yt]=ut(),o),{}),submitOrderFormStatus:(0,A.handleActions)((l={},l[Gt]=mt(Ze.PENDING),l[zt]=mt(Ze.FAILED),l[Vt]=mt(Ze.COMPLETED),l),Ze.IDLE),submitOrderPayment:(0,A.handleActions)((u={},u[Vt]=ct(),u),{}),createMultiPaymentStatus:(0,A.handleActions)((s={},s[qt]=ft(Ze.PENDING),s[Xt]=ft(Ze.FAILED),s[Kt]=ft(Ze.COMPLETED),s),Ze.IDLE),createMultiPayment:(0,A.handleActions)((c={},c[Kt]=Et(),c),{}),validateMultiOrderFormStatus:(0,A.handleActions)((m={},m[Wt]=yt(Ze.PENDING),m[Zt]=yt(Ze.FAILED),m[Qt]=yt(Ze.COMPLETED),m),Ze.IDLE),validateMultiOrderPayment:(0,A.handleActions)((d={},d[Yt]=_t(),d),{}),submitMultiOrderFormStatus:(0,A.handleActions)((p={},p[$t]=gt(Ze.PENDING),p[en]=gt(Ze.FAILED),p[Jt]=gt(Ze.COMPLETED),p),Ze.IDLE),submitMultiOrderPayment:(0,A.handleActions)((b={},b[Jt]=Nt(),b),{}),passKey:(0,A.handleActions)((E={},E[tn]=function(e,t){return t.payload||e},E),""),cardTokenizationError:(0,A.handleActions)((f={},f[nn]=function(e,t){return t.payload},f),""),cardTokenizationSuccess:(0,A.handleActions)((_={},_[rn]=function(e,t){return t.payload},_),""),tokenizeAndPropagateFormValuesStatus:(0,A.handleActions)((y={},y[an]=Ct(Ze.PENDING),y[nn]=Ct(Ze.FAILED),y[rn]=Ct(Ze.COMPLETED),y),Ze.IDLE),redirectUrl:(0,A.handleActions)((N={},N[on]=dt(),N),{status:"",externalRedirectUrl:""}),interacBankInfo:(0,A.handleActions)((C={},C[ln]=pt(),C),{status:"",bankAccountNumber:"",transitNumber:"",bankCode:"",accountHolderName:""}),interactBankFailureInfo:(0,A.handleActions)((v={},v[sn]=At(),v),{}),isLoading:(0,A.handleActions)((T={},T[un]=bt,T),!1)})},enumerable:!1,configurable:!0}),Object.defineProperty(a.prototype,"middlewares",{get:function(){return(0,$e.combineEpics)(this.epics.combineEpics())},enumerable:!1,configurable:!0}),n([N.Injectable,r("design:paramtypes",[N.Store,R,v,It])],a)}(Rt),mn=cn,dn=s(9),pn=s(10),bn=(0,f.forwardRef)(function(e,t){var n=e.id,r=e.name,a=e.defaultChecked,i=e.label,o=e.headingLevel,l=void 0===o?"h4":o,u=e.children,s=e.ariaDescribe,c=e.onChange;return _().createElement(_().Fragment,null,_().createElement("div",{className:"brui-rounded-20 brui-border brui-border-gray-8 transition-all brui-mb-15 brui-drop-shadow-none has-[input[type=radio].brui-size-full:checked]:payment-shadow-3sm"},_().createElement("label",{className:"payment-peer/paymentradio payment-group/paymentradio brui-flex brui-items-center brui-p-30 payment-pl-15 sm:payment-pl-30 brui-cursor-pointer"},_().createElement(pn.RadioButton,{id:n,name:r,value:i,variant:"default",defaultChecked:a,"aria-describedby":s||void 0,ref:t,onChange:c},_().createElement("div",{className:"brui-relative brui-flex brui-justify-between"},_().createElement(pn.Heading,{level:l,variant:"default",id:n+"-label",className:"brui-font-sans brui-mb-0 brui-text-16 brui-leading-20 brui-mt-3 sm:brui-mr-64 payment-text-gray group-has-[:checked]/paymentradio:payment-text-black"},i)))),_().createElement("div",{className:"payment-hidden peer-has-[input[type=radio]:checked]/paymentradio:payment-block payment-px-15 payment-pb-30 sm:payment-px-30"},_().createElement(pn.Divider,{direction:"horizontal",width:1,className:"brui-mb-30 brui-bg-gray-4"}),u)))}),En=(0,f.forwardRef)(function(e,t){var n=e.id,r=e.label,a=e.name,i=e.describe,o=e.defaultChecked,l=void 0!==o&&o,u=e.isInterac,s=e.headingLevel,c=void 0===s?"h4":s,m=e.children,d=e.onChange,p=e.interactIconPath;return _().createElement(_().Fragment,null,_().createElement(pn.RadioCard,{className:"payment-manage-radio-bank brui-border brui-border-gray-3 payment-py-30 sm:!brui-px-30 payment-group/radiocard",id:n,name:a,defaultChecked:l,"aria-labelledby":"label-"+n,"aria-describedby":i?"desc-"+n:"",radioPlacement:"topLeft",ref:t,onChange:d},_().createElement("div",{className:"payment-pl-[34px] sm:payment-pr-[30px] brui-relative brui-flex brui-justify-between"},_().createElement(pn.Heading,{level:c,variant:"default",className:"brui-font-sans group-has-[input[type=radio]:checked]/radiocard:payment-font-bold brui-mb-10 sm:brui-mb-5 brui-text-16 brui-leading-20 payment-mt-5",id:"label-"+n},r),u&&_().createElement("img",{alt:"",className:"payment-ml-5 payment-relative sm:payment-absolute payment-right-0 payment-top-0 sm:payment-w-40 sm:payment-h-40 payment-w-32 payment-h-32",src:p})),_().createElement("div",{className:"sm:payment-pl-[34px] sm:payment-pr-[45px]"},i&&_().createElement(pn.Text,{id:"desc-"+n,elementType:"p",className:"brui-text-14 brui-leading-18 brui-text-gray"},i),_().createElement("div",{className:"brui-z-10 brui-relative"},_().createElement("div",{className:"payment-hidden group-has-[input[type=radio]:checked]/radiocard:payment-block"},m)))))}),fn=function(e){var t=e.intl,n=e.setOmnitureOnFindTransactionLightBox,r=a((0,f.useState)(!1),2),i=r[0],o=r[1],l=function(e){o(e)},u=t.formatMessage({id:"MODAL_BANK_STATEMENT_DESC"}),s=t.formatMessage({id:"MODAL_TRANSIT_NUMBER_DESC"}),c=t.formatMessage({id:"MODAL_ACCOUNT_NUMBER_DESC"});return _().createElement("div",null,_().createElement(pn.Button,{variant:"textBlue",onClick:function(e){e.preventDefault(),l(!0),setTimeout(function(){n()},1e3)},size:"small",className:"payment-text-14 payment-leading-18 payment-flex"},t.formatMessage({id:"MODAL_TRANSIT_ACCOUNT_NUMBERS_TITLE"})),i&&_().createElement(pn.Modal,{id:"transit-and-account","aria-labelledby":"transit-and-account-title",onEscapeKeyPressed:function(){return l(!1)},onOverlayClick:function(){return l(!1)}},_().createElement(pn.ModalContent,{useDefaultRadius:!1,className:"payment-rounded-10"},_().createElement(pn.ModalHeader,{variant:"lightGrayBar",rightButtonIcon:"default",title:t.formatMessage({id:"MODAL_TRANSIT_ACCOUNT_NUMBERS_TITLE"}),isDefaultPadding:!1,className:"payment-px-15 sm:payment-px-30 payment-py-25",onRightButtonClicked:function(){return l(!1)},rightButtonLabel:t.formatMessage({id:"CTA_CLOSE"})}),_().createElement(pn.ModalBody,{isDefaultPadding:!1,className:"payment-px-15 sm:payment-px-30 payment-py-30"},_().createElement("div",{className:"payment-text-gray payment-text-14 payment-leading-18"},_().createElement("div",{className:"payment-flex payment-flex-col payment-justify-between"},_().createElement("div",{className:"payment-flex payment-flex-col sm:payment-flex-row"},_().createElement("div",{className:"payment-w-full sm:payment-max-w-[293px]"},_().createElement(pn.Heading,{level:"h3",variant:"default",className:"payment-font-sans payment-text-black payment-font-bold"},t.formatMessage({id:"MODAL_BANK_STATEMENT_TITLE"})),_().createElement("div",{className:"payment-pt-15",dangerouslySetInnerHTML:{__html:u}}),_().createElement("div",{className:"payment-pt-15"},_().createElement("div",{dangerouslySetInnerHTML:{__html:s}}),_().createElement("div",{dangerouslySetInnerHTML:{__html:c}}))),_().createElement("div",{className:"payment-w-full payment-pt-30 sm:payment-pt-0 sm:payment-ml-30"},_().createElement("img",{src:t.formatMessage({id:"TRANSIT_ACC_NO_PNG"}),alt:"",className:"payment-w-full"})))))))))},_n=function(e){return{setOmnitureOnFindTransactionLightBox:function(t){return e(Ve({data:t}))}}},yn=(0,y.connect)(null,_n)((0,dn.injectIntl)(fn)),Nn=function(e){var t=e.intl,n=e.setOmnitureOnBoxNameLightBox,r=a((0,f.useState)(!1),2),i=r[0],o=r[1],l=function(e){o(e)};return _().createElement("div",null,_().createElement(pn.Button,{variant:"textBlue",onClick:function(e){e.preventDefault(),l(!0),setTimeout(function(){n()},1e3)},size:"small",className:"payment-text-14 payment-leading-18"},t.formatMessage({id:"MODAL_NO_NAME"})),i&&_().createElement(pn.Modal,{id:"no-name-on-card","aria-labelledby":"no-name-on-card-title",onEscapeKeyPressed:function(){return l(!1)},onOverlayClick:function(){return l(!1)}},_().createElement(pn.ModalContent,{useDefaultRadius:!1,className:"payment-rounded-10"},_().createElement(pn.ModalHeader,{variant:"lightGrayBar",rightButtonIcon:"default",title:t.formatMessage({id:"MODAL_NO_NAME_TITLE"}),isDefaultPadding:!1,className:"payment-px-15 sm:payment-px-30 payment-py-25",onRightButtonClicked:function(){return l(!1)},rightButtonLabel:t.formatMessage({id:"CTA_CLOSE"})}),_().createElement(pn.ModalBody,{isDefaultPadding:!1,className:"payment-px-15 sm:payment-px-30 payment-py-30"},_().createElement("div",{className:"payment-text-gray payment-text-14 payment-leading-18"},t.formatMessage({id:"MODAL_NO_NAME_DESC"}))))))},gn=function(e){return{setOmnitureOnBoxNameLightBox:function(t){return e(ze({data:t}))}}},An=(0,y.connect)(null,gn)((0,dn.injectIntl)(Nn)),Cn=function(e){var t=e.intl,n=e.setOmnitureOnSecurityCodeLightBox,r=a((0,f.useState)(!1),2),i=r[0],o=r[1],l=function(e){o(e)};return _().createElement("div",null,_().createElement(pn.Button,{variant:"textBlue",onClick:function(e){e.preventDefault(),l(!0),setTimeout(function(){n()},1e3)},size:"small",className:"payment-text-14 payment-leading-18"},t.formatMessage({id:"MODAL_SECURITY_CODE"})),i&&_().createElement(pn.Modal,{id:"what-is-security-code","aria-labelledby":"what-is-security-code-title",onEscapeKeyPressed:function(){return l(!1)},onOverlayClick:function(){return l(!1)}},_().createElement(pn.ModalContent,{useDefaultRadius:!1,className:"payment-rounded-10"},_().createElement(pn.ModalHeader,{variant:"lightGrayBar",rightButtonIcon:"default",title:t.formatMessage({id:"MODAL_SECURITY_CODE_TITLE"}),isDefaultPadding:!1,className:"payment-px-15 sm:payment-px-30 payment-py-25",onRightButtonClicked:function(){return l(!1)},rightButtonLabel:t.formatMessage({id:"CTA_CLOSE"})}),_().createElement(pn.ModalBody,{isDefaultPadding:!1,className:"payment-px-15 sm:payment-px-30 payment-py-30"},_().createElement("div",{className:"payment-text-gray payment-text-14 payment-leading-18"},_().createElement("p",null,t.formatMessage({id:"MODAL_SECURITY_CODE_DESC"})),_().createElement("div",{className:"payment-flex payment-flex-col sm:payment-flex-row payment-justify-between payment-pt-15"},_().createElement("div",{className:"payment-w-full sm:payment-max-w-[262.5px]"},_().createElement(pn.Heading,{level:"h3",variant:"default",className:"payment-font-sans payment-text-black payment-font-bold"},t.formatMessage({id:"CARD_TYPE_VISA_MASTERCARD"})),_().createElement("div",{className:"payment-pt-15"},t.formatMessage({id:"CARD_TYPE_VISA_MASTERCARD_DESC"})),_().createElement("div",{className:"payment-pt-15"},_().createElement("img",{src:t.formatMessage({id:"BACK_CC_PNG"}),alt:"",className:"payment-h-[132px]"}))),_().createElement(pn.Divider,{direction:"vertical",width:1,className:"payment-mx-30 payment-hidden sm:payment-block"}),_().createElement(pn.Divider,{direction:"horizontal",width:1,className:"payment-my-30 payment-block sm:payment-hidden"}),_().createElement("div",{className:"payment-w-full sm:payment-max-w-[262.5px]"},_().createElement(pn.Heading,{level:"h3",variant:"default",className:"payment-font-sans payment-text-black payment-font-bold"},t.formatMessage({id:"CARD_TYPE_AMERICAN_EXP"})),_().createElement("div",{className:"payment-pt-15"},t.formatMessage({id:"CARD_TYPE_AMERICAN_EXP_DESC"})),_().createElement("div",{className:"payment-pt-15"},_().createElement("img",{src:t.formatMessage({id:"FRONT_CC_PNG"}),alt:"",className:"payment-h-[130px]"})))))))))},vn=function(e){return{setOmnitureOnSecurityCodeLightBox:function(t){return e(qe({data:t}))}}},Tn=(0,y.connect)(null,vn)((0,dn.injectIntl)(Cn)),hn=function(e){var t=e.children,n=e.legends,r=e.srOnly,i=a((0,f.useState)(function(){var e=!1;return f.Children.forEach(t,function(t){if(f.isValidElement(t)){var n=t.props;n.defaultChecked&&n.showBankFieldsOnChange&&(e=!0)}}),e}),2),o=i[0],l=i[1],u=function(e){l(e)};return f.createElement("fieldset",null,f.createElement("legend",{className:"brui-sr-only"},r),f.createElement("div",{className:"brui-flex brui-flex-col"},f.createElement("div",{className:"brui-flex brui-flex-col sm:brui-flex-row"},f.createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] sm:payment-mr-30 sm:payment-text-right payment-pt-13 payment-pb-13 sm:payment-pb-0 payment-mb-10 sm:payment-mb-0"},f.createElement(pn.Label,{className:"brui-block",required:!0},n)),f.createElement("div",{className:"brui-flex brui-flex-col payment-gap-15"},f.Children.map(t,function(e,t){return f.isValidElement(e)?f.cloneElement(e,{showOnChange:u,childIndex:t}):e})))),o)},In=(0,f.forwardRef)(function(e,t){var n=e.name,r=void 0===n?"bank-payment-radio":n,a=e.value,i=e.hasError,o=(e.showOnChange,e.showBankFieldsOnChange,e.childIndex),l=e.defaultChecked,u=e.className,s=e.label,c=e.onChange,m=e.idPrefix,d=void 0===m?"":m,p=e.getExistingBankPaymentDetails,b=e.paymentDetails;return f.useEffect(function(){b&&l&&p(b||[])},[]),f.createElement(pn.RadioButton,{className:[u,"brui-flex brui-items-center brui-absolute brui-size-full brui-opacity-0 enabled:brui-cursor-pointer disabled:brui-cursor-default brui-z-10"].join(" ").trim(),id:d+"bank-payment-radio-id-"+o,name:r,value:a,variant:"boxedInMobile",hasError:i,defaultChecked:l,ref:t,onChange:c,onClick:function(){"function"==typeof p&&p(b||[])}},f.createElement("div",{className:"brui-text-14 brui-leading-18 brui-mt-3",dangerouslySetInnerHTML:{__html:s}}))}),Rn=function(e){var t;if(13===e.length||16===e.length){if(t=new RegExp("^4"),null!=e.match(t))return"VI";if(t=new RegExp("^(5[1-5]|2(22[1-9]|2[3-9][0-9]|[3-6][0-9]{2}|7[01][0-9]|720))"),null!=e.match(t)&&16===e.length)return"MC"}else if(15===e.length&&(t=new RegExp("^(34|37)"),null!=e.match(t)))return"AX";return""},Sn={VISA:/^4/,MASTERCARD:/^(5[1-5]|2[2-7])/,AMEX:/^3[47]/},On=/^(?:[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{2,}\.?\s)+(?:[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{1}\.?\s)*[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{2,}\.?$/,xn=function(e){return Sn.VISA.test(e)?"VISA":Sn.MASTERCARD.test(e)?"MASTERCARD":Sn.AMEX.test(e)?"AMEX":"default"},Mn={mobile:{maxHeight:"374px"},tablet:{maxHeight:"259px"},desktop:{maxHeight:"259px"}},Ln="INTERNAL_SERVER_ERROR",Dn=(0,f.forwardRef)(function(e,t){var n=e.intl,r=e.isCreditCardPayment,a=void 0!==r&&r,i=e.isBankPayment,o=void 0!==i&&i,l=e.isPreAuth,s=void 0!==l&&l,c=e.paymentItems,m=e.onChange,d=e.getExistingBankPaymentDetails,p=a?n.formatMessage({id:"EXISTING_CC_TITLE"}):o?n.formatMessage({id:"EXISTING_BANK_TITLE"}):null,b="".concat(n.formatMessage({id:"SELECT_REQUIRED_LEGEND"})," ").concat(p," ");return _().createElement(_().Fragment,null,a?_().createElement(_().Fragment,null,_().createElement(hn,{isCreditCardPayment:!0,legends:p,srOnly:b},c.filter(function(e){return e.IsOnPreauthorizedPayments&&null!=e.CreditCardDetails}).map(function(e,r){return e.CreditCardDetails&&_().createElement(In,{idPrefix:"PACC",name:"credit-card-radio",value:u(e.CreditCardDetails.CreditCardType),label:"".concat(u(e.CreditCardDetails.CreditCardType),"<br>************").concat(e.CreditCardDetails.CreditCardNumberMasked,", ").concat(n.formatMessage({id:"CREDIT_CARD_VALID"})," ").concat(e.CreditCardDetails.ExpireYear),defaultChecked:!!s||void 0,ref:t,onChange:m})}),_().createElement(In,{idPrefix:"PACC",name:"credit-card-radio",value:n.formatMessage({id:"NEW_CREDIT_ACCOUNT_LABEL"}),label:n.formatMessage({id:"NEW_CREDIT_ACCOUNT_LABEL"}),onChange:m,defaultChecked:!s&&void 0}))):o?_().createElement(_().Fragment,null,_().createElement(hn,{isBankPayment:!0,legends:p,srOnly:"srOnlyStr"},c.filter(function(e){return e.IsOnPreauthorizedPayments&&null!=e.BankAccountDetails}).map(function(e,n){return e.BankAccountDetails&&_().createElement(In,{idPrefix:"PAD",name:"bank-card-details-radio",value:e.BankAccountDetails.BankName,label:"".concat(e.BankAccountDetails.BankName,"<br>(").concat(e.BankAccountDetails.AccountNumberMaskedDisplayView,")"),defaultChecked:n<=0||void 0,ref:t,onChange:m,getExistingBankPaymentDetails:d,paymentDetails:new Array(e)})}),_().createElement(In,{idPrefix:"PAD",name:"bank-card-details-radio",value:n.formatMessage({id:"BANK_NEW_BANK_ACCOUNT_LABEL"}),label:n.formatMessage({id:"BANK_NEW_BANK_ACCOUNT_LABEL"}),onChange:m,getExistingBankPaymentDetails:d}))):null)}),Bn=(0,dn.injectIntl)(Dn),Pn=function(e){e.target.value=e.target.value.replace(/[^0-9]/gi,"")},kn=function(){return Array.from({length:12},function(e,t){var n=t+1;return t<9&&(n="0"+(n=t+1)),n})},wn=function(){var e,t=parseInt((new Date).getFullYear().toString().substring(2)),n=t+9,r=[];for(e=t;e<=n;e++)r.push(e);return r},Un={greatNews:{className:"brui-text-15 brui-text-blue",iconClass:"bi_brui",iconName:"bi_tag_note-big"},notifCardWarning:{className:"brui-text-20 brui-text-yellow",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"},notifCardInfoAlert:{className:"brui-text-20 brui-text-blue",iconClass:"bi_brui",iconName:"bi_info_notif_small"}},Fn=function(e){var t=e.hasNotifCard,n=void 0!==t&&t,r=e.children,a=e.label,i=e.label1,o=e.label2,l=e.label3,u=e.variant;return _().createElement(pn.Card,{variant:"gray",radius:!0,className:["payment-flex payment-flex-col sm:payment-flex-row payment-p-15 payment-gap-15 payment-rounded-[16px] payment-mb-30",n?"":"payment-hidden"].join(" ").trim()},_().createElement("div",{className:"payment-flex payment-size-20 payment-items-center payment-justify-center payment-self-start sm:payment-self-start "},_().createElement(pn.Icon,{className:["",Un[u].className].join(" ").trim(),iconClass:["",Un[u].iconClass].join(" ").trim(),iconName:["",Un[u].iconName].join(" ").trim()})),_().createElement("div",{id:n?"discount-offer":"",className:"payment-flex-grow"},_().createElement("p",{className:"brui-text-14 brui-leading-18 brui-text-gray brui-mb-10"},_().createElement("span",{className:"payment-font-bold payment-text-black"},a," "),_().createElement("span",{className:"payment-text-black"},i)),r,_().createElement("div",{className:"payment-text-12 payment-text-gray payment-leading-14"},_().createElement("strong",null,o),l)))},Hn=(0,f.forwardRef)(function(e,t){var n,r,i,o,l,u,s,c,m,d,p,E,f,y,N,g,A,C,v,T=e.intl,h=e.Checked,I=e.onChange,R=e.errorBankAccountHolderName,S=e.isInteracSelected,O=e.radioCardRef,x=e.handleBankRadioManualDetailsChange,M=e.isBankManualEnterDetails,L=e.isPreauth,D=e.hasBankAccountDetails,B=e.bankitems,P=e.handleBankRadioChange,k=e.bankListInterac,w=(e.handleInteracSubmit,e.isBankChecked),U=e.inputRefs,F=e.errorBankName,H=e.errorBankTransit,Y=e.errorBankAccountNumber,j=e.radioRef,G=e.bankList,V=e.redirectUrl,z=e.interacBankInfo,q=e.checkedBillItems,K=e.interactBankFailureInfo,X=e.creditCardAutopayOffers,W=e.debitCardAutopayOffers,Q=e.language,Z=e.isInteractEnabled,$=e.IsAutopayCreditEnabled,J=a(_().useState({bankAccountHolderName:z.accountHolderName,bankAccountNumber:z.bankAccountNumber,bankTransitCode:z.transitNumber,bankCode:z.bankCode}),2),ee=J[0],te=J[1],ne=T.formatMessage({id:"BANK_ACCOUNT_LABEL"}),re=function(){var e=[];return W&&(null==W||W.map(function(t){q&&q.map(function(n){t.Ban===n.Ban&&e.push(t)})})),e},ae={label:T.formatMessage({id:"PAYMENT_METHOD"}),credits:B&&B.length>1?re():W};return _().useEffect(function(){null!=z&&"SUCCESS"===z.status&&te({bankAccountHolderName:z.accountHolderName,bankAccountNumber:z.bankAccountNumber,bankTransitCode:z.transitNumber,bankCode:z.bankCode})},[z]),_().useEffect(function(){null!=K&&"error"===K.dataType&&K.data.includes(Ln)&&(O.manualDetails&&O.manualDetails.current&&(O.manualDetails.current.checked=!0),O.interac&&O.interac.current&&(O.interac.current.checked=!1))},[K]),r=function(){return B&&B.length>1?re().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):W&&W.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0)},i=function(){return B&&B.length>1?re().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):X&&X.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0)},o=a(_().useState({bankAccountHolder:"",bankAccountNumber:"",bankAccountNumberMasked:"",bankTransit:"",bankCode:""}),2),l=o[0],u=o[1],s=a(_().useState([]),2),c=s[0],m=s[1],d=function(e){m(e)},_().useEffect(function(){c.length>0&&!S?c.map(function(e){u(function(t){var n,r,a,i,o;return b(b({},t),{bankAccountHolder:(null===(n=e.BankAccountDetails)||void 0===n?void 0:n.CardHolder)||"",bankAccountNumber:(null===(r=e.BankAccountDetails)||void 0===r?void 0:r.AccountNumber)||"",bankAccountNumberMasked:(null===(a=e.BankAccountDetails)||void 0===a?void 0:a.AccountNumberMasked)||"",bankTransit:(null===(i=e.BankAccountDetails)||void 0===i?void 0:i.TransitCode)||"",bankCode:(null===(o=e.BankAccountDetails)||void 0===o?void 0:o.BankCode)||""})})}):0!==c.length||S||u(function(e){return b(b({},e),{bankAccountHolder:"",bankAccountNumber:"",bankAccountNumberMasked:"",bankTransit:"",bankCode:""})})},[c]),p=function(e){var t=e.target.value;u(b(b({},l),{bankAccountHolder:t}))},E=function(e){var t=e.target.value;u(b(b({},l),{bankTransit:t}))},f=function(e){var t=e.target.value;u(b(b({},l),{bankAccountNumber:t}))},y=function(){var e;return(null===(e=null==ae?void 0:ae.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){var t,n;return(null===(t=e.eligiblePaymentMethods)||void 0===t?void 0:t.includes("C"))&&(null===(n=e.eligiblePaymentMethods)||void 0===n?void 0:n.includes("D"))})})}))||!1},N=function(e){var t=e.target.value;u(b(b({},l),{bankAccountNumberMasked:t,bankAccountNumber:t}))},g=Object.values(z).every(function(e){return""===e}),A=T.formatMessage({id:"CTA_INTERAC"}),C=T.formatMessage({id:"CTA_INTERAC_SR"}),v="ON"===Z,_().createElement("div",{className:"brui-mb-15"},_().createElement(bn,{id:"payment-radio-bank",name:"payment-radio",label:ne,headingLevel:"h3",defaultChecked:!!h||void 0,ref:t,onChange:I},$&&_().createElement("div",null,_().createElement(Fn,{hasNotifCard:r()>0,variant:"greatNews",label:T.formatMessage({id:"GREAT_NEWS"}),label1:y()?r()>1?T.formatMessage({id:"LABEL_LOADED_OFFERS_TITLE_TRUEAUTOPAY"}):T.formatMessage({id:"LABEL_LOADED_OFFER_TITLE_TRUEAUTOPAY"}):r()>1?T.formatMessage({id:"LABEL_LOADED_OFFERS_DEBIT_TITLE"}):T.formatMessage({id:"LABEL_LOADED_OFFER_DEBIT_TITLE"}),label2:T.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING"}),label3:r()>1?T.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE"}):T.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE"})},null===(n=null==ae?void 0:ae.credits)||void 0===n?void 0:n.map(function(e,t){var n;return _().createElement(_().Fragment,{key:e.Ban||t},B&&B.length>1&&_().createElement("p",{className:"payment-text-14 payment-text-gray"},e.banInfo&&e.banInfo.nickName,":"),null===(n=e.AutopayEligibleSubscribers)||void 0===n?void 0:n.map(function(e,t){var n;return null===(n=null==e?void 0:e.autopayOffers)||void 0===n?void 0:n.map(function(t,n){return _().createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10",key:"".concat(e.subscriberTelephoneNumber,"-").concat(n)},_().createElement(pn.ListItem,{className:"payment-text-14 payment-text-gray payment-leading-18"},e.subscriberTelephoneNumber," - ",_().createElement(pn.Price,{className:"payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal",showZeroDecimalPart:!1,price:t.discountAmount,variant:"defaultPrice",suffixText:"perMonth",language:Q})))})}))})),W&&W.length>0&&0===r()&&i()>0?_().createElement(pn.Text,{role:"alert",className:"payment-bg-gray-3 payment-flex payment-flex-col sm:payment-flex-row payment-p-15 payment-gap-15 payment-rounded-[16px] payment-mb-10",elementType:"div"},_().createElement("div",{className:"payment-flex payment-size-20 payment-items-center payment-justify-center"},_().createElement(pn.Icon,{className:"payment-text-20 payment-text-blue",iconClass:"bi_brui",iconName:"bi_info_notif_small"})),_().createElement(pn.Text,{id:0===r()?"discount-offer":"",className:"payment-text-14 payment-leading-20 payment-text-gray",elementType:"p"}," ",T.formatMessage({id:"SORRY_MESSAGE"}))):""),_().createElement("div",null,_().createElement("div",{role:"radiogroup","aria-labelledby":"payment-radio-bank"},_().createElement("form",{noValidate:!0},_().createElement(pn.FormControl,null,!S&&_().createElement("div",null,v&&_().createElement("div",null,_().createElement(En,{id:"radio-1",name:"bank-details-radio",label:T.formatMessage({id:"BANK_ACCOUNT_AUTOMATIC_LABEL"}),describe:T.formatMessage({id:"BANK_ACCOUNT_AUTOMATIC_DESCRIPTION"}),isInterac:!0,ref:O.interac,defaultChecked:!0,interactIconPath:T.formatMessage({id:"INTERAC_BOX_LOGO"}),onChange:x},_().createElement("div",null,_().createElement("ul",{className:"max-318:payment-w-auto max-318:payment-h-auto payment-gap-x-15 sm:payment-gap-x-45 payment-w-1/2 sm:payment-w-[250px] payment-flex payment-flex-wrap payment-flex-col payment-h-[90px] payment-list-disc payment-list-inside payment-mt-15 payment-ml-10"},k.map(function(e){return _().createElement(pn.ListItem,{className:"brui-text-gray brui-leading-18"},_().createElement(pn.Text,{className:"payment-mt-10 brui-text-14"}," ",e))})),_().createElement("div",{className:"payment-mt-15"},_().createElement("a",{href:V.externalRedirectUrl,onClick:function(){var e,t=new Array(q.length);q&&q.map(function(e){t.includes(e.Ban)||t.push(e.Ban)}),e=t.filter(function(e){return null!==e}),window.sessionStorage.setItem("itemsChecked",JSON.stringify(e))},className:"brui-text-15 brui-leading-17 brui-py-7 brui-px-30 brui-inline-block brui-rounded-30 \r\n                            brui-bg-blue-1 brui-text-white brui-border-blue-1 brui-border-2 enabled:hover:brui-bg-blue enabled:hover:brui-border-blue focus:brui-outline-blue-2 \r\n                            focus:brui-outline focus:brui-outline-2 focus:brui-outline-offset-3 disabled:brui-bg-gray-2 \r\n                            disabled:brui-text-white disabled:brui-border-gray-2",role:"button"},_().createElement("span",{"aria-hidden":"true",dangerouslySetInnerHTML:{__html:A}}),_().createElement("span",{className:"payment-sr-only",dangerouslySetInnerHTML:{__html:C}})))))),_().createElement("div",{className:"payment-mt-15"},_().createElement(En,{id:"radio-2",name:"bank-details-radio",label:c.length>0?T.formatMessage({id:"EXISTING_BANK_ACCOUNT_MANUAL_DETAILS_LABEL"}):T.formatMessage({id:"BANK_ACCOUNT_MANUAL_DETAILS_LABEL"}),describe:T.formatMessage({id:"BANK_ACCOUNT_MANUAL_DETAILS_DESCRIPTION"}),ref:O.manualDetails,onChange:x,defaultChecked:!v||void 0},_().createElement(pn.Divider,{direction:"horizontal",width:1,className:"payment-my-30 payment-bg-gray-4"}),_().createElement("div",{className:"brui-flex brui-flex-row payment-gap-4 brui-items-center payment-mb-30 md:payment-mb-45"},_().createElement("div",{className:"brui-text-14 brui-text-gray brui-leading-18"},T.formatMessage({id:"BANK_NEED_HELP"})),_().createElement(yn,null)),_().createElement("div",null,(M||!g||!v||"error"===(null==K?void 0:K.dataType))&&_().createElement("form",{noValidate:!0},L&&D&&_().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},_().createElement(Bn,{isCreditCardPayment:!1,isBankPayment:!0,isPreAuth:L,paymentItems:B,ref:j,onChange:P,getExistingBankPaymentDetails:d})),(w||!L&&!w||L||z)&&_().createElement(_().Fragment,null,_().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},_().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},_().createElement(pn.Label,{id:"bank-name-label",htmlFor:"bank-name",isError:F,required:!0,className:F?"payment-error-required":""},T.formatMessage({id:"BANK_NAME_LABEL"}))),""!==l.bankCode&&_().createElement(pn.Select,{className:"sm:!payment-w-[280px] brui-text-gray",id:"bank-name",name:"select-bank-name",hasError:F,errorMessage:T.formatMessage({id:"BANK_NAME_ERROR_LABEL"}),"aria-labelledby":"bank-name-label","aria-describedby":" ",ref:U.inputBankName,defaultValue:l.bankCode,dropDownHeight:Mn,placeHolder:T.formatMessage({id:"SELECT_BANK_PLACEHOLDER"})},G.map(function(e){return""!==e.Text&&_().createElement(pn.SelectOption,{value:e.Value,id:"option-bank-".concat(e.Value),displayName:e.Text})})),""===l.bankCode&&_().createElement(pn.Select,{className:"sm:!payment-w-[280px] brui-text-gray",id:"bank-name",name:"select-bank-name",hasError:F,errorMessage:T.formatMessage({id:"BANK_NAME_ERROR_LABEL"}),"aria-labelledby":"bank-name-label","aria-describedby":" ",ref:U.inputBankName,dropDownHeight:Mn,placeHolder:T.formatMessage({id:"SELECT_BANK_PLACEHOLDER"})},G.map(function(e){return""!==e.Text&&_().createElement(pn.SelectOption,{value:e.Value,id:"option-bank-".concat(e.Value),displayName:e.Text})}))),_().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},_().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},_().createElement(pn.Label,{htmlFor:"bank-holder-name",isError:R,required:!0,className:R?"payment-error-required":""},T.formatMessage({id:"BANK_HOLDER_NAME_LABEL"}))),_().createElement(pn.InputText,{className:"sm:!payment-w-[280px] !brui-text-gray",id:"bank-holder-name",required:!0,isError:R,errorMessage:T.formatMessage({id:"BANK_HOLDER_NAME_ERROR_LABEL"}),minLength:5,maxLength:70,ref:U.inputBankAccountHolder,value:l.bankAccountHolder,onChange:p})),_().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},_().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},_().createElement(pn.Label,{htmlFor:"bank-transit-number",isError:H,required:!0,className:H?"payment-error-required":""},T.formatMessage({id:"BANK_TRANSIT_NUMBER_LABEL"})),_().createElement(pn.Text,{elementType:"div",className:"brui-text-12 leading-14 sm:payment-ml-14 payment-text-gray"},T.formatMessage({id:"BANK_TRANSIT_NUMBER_DESCRIPTION"}))),_().createElement(pn.InputText,{className:"!payment-w-[140px]",id:"bank-transit-number",required:!0,isError:H,errorMessage:T.formatMessage({id:"BANK_TRANSIT_ERROR_LABEL"}),minLength:5,maxLength:5,onInput:function(e){return Pn(e)},ref:U.inputTransitNumber,value:l.bankTransit,onChange:E})),_().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},_().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},_().createElement(pn.Label,{htmlFor:"bank-account-number",isError:Y,required:!0,className:Y?"payment-error-required":""},T.formatMessage({id:"BANK_ACCOUNT_NUMBER_LABEL"})),_().createElement(pn.Text,{elementType:"div",className:"brui-text-12 leading-14 sm:payment-ml-14 payment-text-gray"},T.formatMessage({id:"BANK_ACCOUNT_NUMBER_DESCRIPTION"}))),_().createElement("div",null,c.length>0&&_().createElement(pn.InputText,{className:"!payment-w-[140px]",id:"bank-account-number",required:!0,isError:Y,minLength:5,maxLength:12,errorMessage:T.formatMessage({id:"BANK_ACCOUNT_HOLDER_NUMBER_ERROR_LABEL"}),onInput:function(e){return Pn(e)},value:l.bankAccountNumberMasked,onChange:N}),_().createElement(pn.InputText,{className:"!payment-w-[140px]",id:"bank-account-number",required:!0,isError:!(c.length>0)&&Y,minLength:5,maxLength:12,errorMessage:T.formatMessage({id:"BANK_ACCOUNT_HOLDER_NUMBER_ERROR_LABEL"}),onInput:function(e){return Pn(e)},ref:U.inputBankAccountNumber,value:l.bankAccountNumber,onChange:f,type:c.length>0?"hidden":"text"}))),_().createElement(pn.Text,{className:"brui-text-14 brui-leading-18 brui-text-gray payment-mt-30 sm:payment-mt-45 brui-inline-block"},T.formatMessage({id:"REQUIRED_LABEL"}))))))))))),S&&v&&_().createElement("div",null,_().createElement("form",{noValidate:!0},_().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},_().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},_().createElement(pn.Label,{id:"bank-name-label",htmlFor:"bank-name",isError:!1,required:!0},T.formatMessage({id:"BANK_NAME_LABEL"}))),_().createElement(pn.Select,{className:"sm:!payment-w-[280px] brui-text-gray",id:"bank-name",name:"select-bank-name",hasError:!1,errorMessage:"Please select at least one option","aria-labelledby":"bank-name-label",ref:U.inputBankName,defaultValue:ee.bankCode,dropDownHeight:Mn},G.map(function(e){return""!==e.Text&&_().createElement(pn.SelectOption,{value:e.Value,id:"option-bank-".concat(e.Value),displayName:e.Text})}))),_().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},_().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},_().createElement(pn.Label,{htmlFor:"bank-holder-name",isError:!1,required:!0},T.formatMessage({id:"BANK_HOLDER_NAME_LABEL"}))),_().createElement(pn.InputText,{className:"sm:!payment-w-[280px] !brui-text-gray",id:"bank-holder-name",required:!0,isError:R,errorMessage:T.formatMessage({id:"BANK_HOLDER_NAME_ERROR_LABEL"}),minLength:5,maxLength:70,ref:U.inputBankAccountHolder,value:ee.bankAccountHolderName,onChange:function(e){var t=e.target.value;te(b(b({},ee),{bankAccountHolderName:t}))}})),_().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},_().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},_().createElement(pn.Label,{htmlFor:"bank-transit-number",isError:!1,required:!0},T.formatMessage({id:"BANK_TRANSIT_NUMBER_LABEL"})),_().createElement(pn.Text,{elementType:"div",className:"brui-text-12 leading-14 sm:payment-ml-14 payment-text-gray"},T.formatMessage({id:"BANK_TRANSIT_NUMBER_DESCRIPTION"}))),_().createElement(pn.InputText,{className:"!payment-w-[140px]",id:"bank-transit-number",required:!0,isError:!1,errorMessage:"This is required field",minLength:7,maxLength:12,onInput:function(e){return Pn(e)},ref:U.inputTransitNumber,value:ee.bankTransitCode,onChange:function(e){var t=e.target.value;te(b(b({},ee),{bankTransitCode:t}))}})),_().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},_().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},_().createElement(pn.Label,{htmlFor:"bank-account-number",isError:!1,required:!0},T.formatMessage({id:"BANK_ACCOUNT_NUMBER_LABEL"})),_().createElement(pn.Text,{elementType:"div",className:"brui-text-12 leading-14 sm:payment-ml-14 payment-text-gray"},T.formatMessage({id:"BANK_ACCOUNT_NUMBER_DESCRIPTION"}))),_().createElement("div",null,_().createElement(pn.InputText,{className:"!payment-w-[140px]",id:"bank-account-number",required:!0,isError:!1,errorMessage:"This is required field",onInput:function(e){return Pn(e)},ref:U.inputBankAccountNumber,value:ee.bankAccountNumber,onChange:function(e){var t=e.target.value;te(b(b({},ee),{bankAccountNumber:t}))}}),_().createElement("div",{style:{marginTop:"5px"},className:"brui-flex brui-gap-10 brui-mt-5"},_().createElement("span",{style:{marginRight:"8px"},className:"bi_small_checkmark_full bi_brui brui-text-green",role:"img","aria-hidden":"true","aria-label":" "}),_().createElement("div",{id:"account-fetched",className:"brui-flex brui-flex-col brui-text-12 brui-leading-14"},_().createElement("span",{id:"account-fetched-message",className:"brui-text-black brui-font-bold"},T.formatMessage({id:"INTERAC_FETCHED_LABEL"})),_().createElement("span",{className:"brui-text-gray"},T.formatMessage({id:"INTERAC_FETCHED_SUBTITLE"})))))),_().createElement(pn.Text,{className:"brui-text-14 brui-leading-18 brui-text-gray payment-mt-30 sm:payment-mt-45 brui-inline-block"},T.formatMessage({id:"REQUIRED_LABEL"})))))))}),Yn=function(e){return{redirectUrlAction:function(){e(Me({}))}}},jn=function(e){return{redirectUrl:e.redirectUrl,interacBankInfo:e.interacBankInfo,interactBankFailureInfo:e.interactBankFailureInfo}},Gn=(0,y.connect)(jn,Yn)((0,dn.injectIntl)(Hn)),Vn=(0,f.forwardRef)(function(e,t){var n,r,i,o,l=e.intl,u=e.Checked,s=e.onChange,c=e.isPreauth,m=(e.hasCreditCardDetails,e.bankitems),d=(e.radioRef,e.handleBankRadioChange,e.isBankChecked),p=e.cardNumber,b=e.handleCreditCardChange,E=e.inputRefs,f=e.cardIcons,y=e.cardType,N=e.errorCardNumber,g=e.errorCardName,A=e.errorExpiryDate,C=e.errorSecurityCode,v=e.handleMaskCVV,T=e.CVV,h=e.creditCardAutopayOffers,I=e.debitCardAutopayOffers,R=e.checkedBillItems,S=e.language,O=e.IsAutopayCreditEnabled,x=l.formatMessage({id:"CREDIT_CARD_LABEL"}),M=a(_().useState(l.formatMessage({id:"CREDIT_CARD_YEAR_TEXT"})),2),L=M[0],D=M[1],B=a(_().useState(l.formatMessage({id:"CREDIT_CARD_MONTH_TEXT"})),2),P=B[0],k=B[1],w=l.formatMessage({id:"CREDIT_CARD_EXPIRY_DATE_SR_LABEL"},{month:P,year:L}),U=function(){var e=[];return h&&(null==h||h.map(function(t){R&&(null==R||R.map(function(n){t.Ban===n.Ban&&e.push(t)}))})),e},F={label:l.formatMessage({id:"PAYMENT_METHOD"}),credits:m&&m.length>1?U():h},H=function(){return m&&m.length>1?U().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):h&&h.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0)};return _().createElement("div",{className:"brui-mb-15"},_().createElement(bn,{id:"payment-radio-credit",name:"payment-radio",label:x,headingLevel:"h3",defaultChecked:!!u||void 0,ref:t,onChange:s},O&&_().createElement("div",null,_().createElement(Fn,{hasNotifCard:H()>0,variant:"greatNews",label:l.formatMessage({id:"GREAT_NEWS"}),label1:function(){var e;return(null===(e=null==F?void 0:F.credits)||void 0===e?void 0:e.some(function(e){var t;return null===(t=e.AutopayEligibleSubscribers)||void 0===t?void 0:t.some(function(e){var t;return null===(t=null==e?void 0:e.autopayOffers)||void 0===t?void 0:t.some(function(e){var t,n;return(null===(t=e.eligiblePaymentMethods)||void 0===t?void 0:t.includes("C"))&&(null===(n=e.eligiblePaymentMethods)||void 0===n?void 0:n.includes("D"))})})}))||!1}()?H()>1?l.formatMessage({id:"LABEL_LOADED_OFFERS_TITLE_TRUEAUTOPAY"}):l.formatMessage({id:"LABEL_LOADED_OFFER_TITLE_TRUEAUTOPAY"}):H()>1?l.formatMessage({id:"LABEL_LOADED_OFFERS_CREDIT_TITLE"}):l.formatMessage({id:"LABEL_LOADED_OFFER_CREDIT_TITLE"}),label2:l.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE_HEADING"}),label3:H()>1?l.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDITS_NOTE"}):l.formatMessage({id:"MY_BELL_PREAUTHORIZED_PAYMENT_CREDIT_NOTE"})},null===(n=null==F?void 0:F.credits)||void 0===n?void 0:n.map(function(e,t){var n;return _().createElement(_().Fragment,{key:e.Ban||t},m&&m.length>1&&_().createElement("p",{className:"payment-text-14 payment-text-gray"},e.banInfo&&e.banInfo.nickName,":"),null===(n=e.AutopayEligibleSubscribers)||void 0===n?void 0:n.map(function(e,t){var n;return null===(n=null==e?void 0:e.autopayOffers)||void 0===n?void 0:n.map(function(t,n){return _().createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10",key:"".concat(e.subscriberTelephoneNumber,"-").concat(n)},_().createElement(pn.ListItem,{className:"payment-text-14 payment-text-gray payment-leading-18"},e.subscriberTelephoneNumber," - ",_().createElement(pn.Price,{className:"brui-text-18 brui-text-blue brui-text-darkblue payment-text-blue payment-text-darkblue payment-inline brui-lowercase !payment-text-14 payment-font-normal",showZeroDecimalPart:!1,price:t.discountAmount,variant:"defaultPrice",suffixText:"perMonth",language:S})))})}))})),h&&h.length>0&&0===H()&&(m&&m.length>1?(o=[],I&&(null==I||I.map(function(e){R&&(null==R||R.map(function(t){e.Ban===t.Ban&&o.push(e)}))})),o).reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):I&&I.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0))>0?_().createElement(pn.Text,{role:"alert",className:"payment-bg-gray-3 payment-flex payment-flex-col sm:payment-flex-row payment-p-15 payment-gap-15 payment-rounded-[16px] payment-mb-10",elementType:"div"},_().createElement("div",{className:"payment-flex payment-size-20 payment-items-center payment-justify-center"},_().createElement(pn.Icon,{className:"payment-text-20 payment-text-blue",iconClass:"bi_brui",iconName:"bi_info_notif_small"})),_().createElement(pn.Text,{className:"payment-text-14 payment-leading-20 payment-text-gray",elementType:"p"}," ",l.formatMessage({id:"SORRY_MESSAGE_CREDIT"}))):""),_().createElement("div",null,_().createElement("form",{noValidate:!0},(d||!c&&!d||c)&&_().createElement(_().Fragment,null,_().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},_().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-self-start sm:payment-text-right payment-mr-0 sm:payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},_().createElement("div",null,_().createElement(pn.Label,{htmlFor:"card-number",isError:N,required:!0,className:N?"payment-error-required":""},_().createElement("span",{id:"cc-number-label",className:"brui-sr-only"},l.formatMessage({id:"CREDIT_CARD_NUMBER_SR_LABEL"})),_().createElement("span",{"aria-hidden":!0},l.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL"})))),_().createElement(pn.Text,{"aria-hidden":!!(null===(i=null===(r=E.inputCreditCardNumber)||void 0===r?void 0:r.current)||void 0===i?void 0:i.value),elementType:"div",className:"brui-text-12 leading-14 sm:brui-ml-14 brui-text-gray payment-mt-5"},l.formatMessage({id:"CREDIT_CARD_NUMBER_DESC_INPUT_LABEL"}))),_().createElement("div",{className:"brui-flex brui-flex-col sm:brui-flex-row brui-flex-wrap"},_().createElement(pn.InputText,{value:p,onChange:b,onInput:function(e){return Pn(e)},className:"sm:!payment-w-[280px] payment-mr-30",id:"card-number",required:!0,isError:N,errorMessage:l.formatMessage({id:"ERROR_CREDIT_CARD_NUMBER_INPUT_LABEL"}),ref:E.inputCreditCardNumber,"aria-labelledby":"cc-number-label"}),_().createElement("div",{className:"sm:payment-h-44 payment-ml-0 payment-mt-10 sm:payment-mt-[6px] brui-flex payment-items-baseline brui-gap-15","aria-label":l.formatMessage({id:"CC_IMAGE_SR_LABEL"}),role:"img"},Object.entries(f).map(function(e){var t=a(e,2),n=t[0],r=t[1];return _().createElement("img",{key:n,src:r,alt:"".concat(n," card"),className:"brui-h-32 payment-mr-15 brui-object-contain","aria-hidden":"true",style:{opacity:y===n?1:.5,transition:"opacity 0.3s ease-in-out"}})})))),_().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},_().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex sm:payment-self-start brui-flex-col sm:payment-text-right payment-mr-0 sm:payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},_().createElement("div",null,_().createElement(pn.Label,{htmlFor:"text-2",isError:g,required:!0,className:g?"payment-error-required":""},_().createElement("span",{id:"cc-name-label",className:"brui-sr-only"},l.formatMessage({id:"CREDIT_CARD_NAME_SR_LABEL"})),_().createElement("span",{"aria-hidden":!0},l.formatMessage({id:"CREDIT_CARD_NAME_LABEL"})))),_().createElement(pn.Text,{elementType:"div",className:"brui-text-12 leading-14 sm:brui-ml-14 brui-text-gray"},l.formatMessage({id:"CREDIT_CARD_NAME_DESC_INPUT_LABEL"}))),_().createElement("div",{className:"brui-flex brui-flex-col sm:brui-flex-row brui-flex-wrap"},_().createElement(pn.InputText,{className:"sm:!payment-w-[280px]",id:"text-2","aria-labelledby":"cc-name-label",required:!0,isError:g,errorMessage:l.formatMessage({id:"ERROR_CREDIT_CARD_NAME_INPUT_LABEL"}),minLength:5,maxLength:70,ref:E.inputCreditCardHolderName}),_().createElement("div",{className:"brui-flex payment-items-baseline sm:payment-h-44 sm:payment-ml-10 payment-mt-5 sm:payment-mt-7"},_().createElement(An,null)))),_().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},_().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-0 sm:payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},_().createElement(pn.Label,{id:"label-3",isError:A,required:!0,className:A?"payment-error-required":""},_().createElement("span",{id:"expiry-month-label",className:"brui-sr-only"},w),_().createElement("span",{"aria-hidden":!0},l.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"})))),_().createElement("div",{className:"brui-flex-col"},_().createElement(pn.FormGroup,{className:"brui-flex brui-flex-wrap",hasError:A,errorMessage:l.formatMessage({id:"ERROR_CREDIT_CARD_EXPIRY_INPUT_LABEL"})},_().createElement("div",{className:"payment-w-[75px]"},_().createElement(pn.Select,{name:"month",id:"select-12",defaultValue:"",placeHolder:l.formatMessage({id:"CREDIT_CARD_MONTH_PLACEHOLDER"}),onChange:function(e){k(e.target.value)},disableDropdownIcon:!0,className:"brui-text-gray",ref:E.inputCreditCardExpiryMonth,"aria-required":!0,"aria-labelledby":"expiry-month-label"},kn().map(function(e,t){return _().createElement(pn.SelectOption,{value:e.toString(),id:"option-"+t,displayName:e.toString()})}))),_().createElement("div",{className:"payment-w-[75px] payment-ml-10"},_().createElement(pn.Select,{name:"year",id:"select-2",defaultValue:"",placeHolder:l.formatMessage({id:"CREDIT_CARD_YEAR_PLACEHOLDER"}),onChange:function(e){D(e.target.value)},disableDropdownIcon:!0,className:"brui-text-gray",ref:E.inputCreditCardExpiryYear,"aria-required":!0,"aria-labelledby":"expiry-year-label"},wn().map(function(e,t){return _().createElement(pn.SelectOption,{value:e.toString(),id:"option-year-"+t,displayName:e.toString()})})))))),_().createElement(pn.FormControl,{className:"sm:brui-flex-row payment-mt-30"},_().createElement("div",{className:"sm:payment-w-[174px] sm:payment-min-w-[174px] brui-flex brui-flex-col sm:payment-text-right payment-mr-0 sm:payment-mr-30 brui-justify-center sm:payment-h-44 payment-mb-10 sm:payment-mb-0"},_().createElement(pn.Label,{htmlFor:"text-4",isError:C,required:!0,className:C?"payment-error-required":""},_().createElement("span",{id:"cc-security-code-label",className:"brui-sr-only"},l.formatMessage({id:"CREDIT_CARD_SECURITY_CODE_INPUT_SR_LABEL"})),_().createElement("span",{"aria-hidden":!0},l.formatMessage({id:"CREDIT_CARD_SECURITY_CODE_INPUT_LABEL"})))),_().createElement("div",{className:"brui-flex sm:brui-flex-row brui-flex-wrap"},_().createElement(pn.InputText,{onInput:function(e){return Pn(e)},className:"!payment-w-[75px] payment-mr-10",id:"text-4",required:!0,isError:C,errorMessage:l.formatMessage({id:"ERROR_CREDIT_CARD_SECURITY_CODE_INPUT_LABEL"}),errorMessageClassName:"payment-w-[75px] payment-text-nowrap",type:"password",onChange:v,ref:E.inputCreditCardSecurityCode,"aria-labelledby":"cc-security-code-label"}),_().createElement(pn.InputText,{type:"hidden",id:"text-hidden",value:T}),_().createElement("div",{className:"brui-flex brui-items-center brui-h-44"},_().createElement(Tn,null)))))),_().createElement(pn.Text,{className:"brui-text-14 brui-leading-18 brui-text-gray payment-mt-45 sm:payment-mt-45 brui-inline-block"},l.formatMessage({id:"REQUIRED_LABEL"})))))}),zn=(0,dn.injectIntl)(Vn),qn=function(e){var t=e.label,n=e.value,r=e.className,a=e.needSRText,i=e.srText,o=e.role,l=e.isMultiBan;return f.createElement("div",{className:[r,"payment-mb-5 last:payment-mb-0"].join(" ").trim(),role:o},f.createElement(pn.Text,{elementType:"div",className:l?"brui-flex payment-justify-between sm:payment-justify-normal brui-text-14 brui-leading-18 brui-ml-10":"brui-flex payment-justify-between sm:payment-justify-normal brui-text-14 brui-leading-18"},f.createElement("label",{className:l?"sm:payment-w-[165px] payment-mr-30":"sm:payment-w-[175px] payment-mr-30"},l?t:f.createElement("strong",{className:"payment-leading-18"},t)),f.createElement(pn.Text,{elementType:"div",className:"brui-text-gray brui-text-right sm:brui-text-left","aria-hidden":a},n),a&&f.createElement("span",{className:"brui-sr-only"},i)))},Kn=function(e){var t=e.intl,n=e.isActive,r=(e.className,e.onEditClick),a=[{label:t.formatMessage({id:"CREDIT_CARD_TYPE_LABEL"}),value:"Visa"},{label:t.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL"}),value:"************1234"},{label:t.formatMessage({id:"CREDIT_CARD_NAME_LABEL"}),value:"Jane Doe"},{label:t.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"}),value:"00/0000"}];return f.createElement("div",{className:n?"sm:payment-mb-60 payment-block":"sm:payment-mb-15 payment-border-b payment-border-gray-4 payment-hidden"},f.createElement("div",{className:"payment-flex payment-items-center payment-justify-between"},f.createElement(pn.HeadingStep,{disableSrOnlyText:!0,status:n?"complete":"inactive",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:t.formatMessage({id:"SELECT_PAYMENT_METHOD_HEADING"}),id:"pre-auth-select-payment_method"}),f.createElement("div",{className:"payment-pt-45"},f.createElement(pn.IconLink,{icon:f.createElement(pn.Icon,{iconClass:"bi_brui",iconName:"bi_edit_pencil",className:"brui-text-16"}),text:t.formatMessage({id:"CTA_EDIT"}),variant:"textBlue",size:"regular",href:"",position:"right",className:["payment-flex payment-items-center !payment-text-14 !payment-leading-18",n?"":"payment-hidden"].join(" ").trim(),"aria-describedby":"pre-auth-select-payment_method",onClick:r,"data-test":"test"}))),f.createElement("div",{className:["brui-pb-45",n?"":"brui-hidden"].join(" ").trim()},f.createElement("div",{className:"brui-mt-15"},a.map(function(e,t){return f.createElement(qn,{className:t>0?"brui-mt-5":"",label:e.label,value:e.value})}))))},Xn=(0,dn.injectIntl)(Kn),Wn=function(e){var n=e.label,r=e.className,a=e.labelClassName,i=e.listClassName,o=e.children,l=t(e,["label","className","labelClassName","listClassName","children"]);return _().createElement("div",b({className:["payment-pb-15 last:payment-pb-0 payment-pt-15 first:payment-pt-0 payment-border-b-1 payment-border-b-gray-4 last:payment-border-none",r].join(" ").trim()},l),n&&_().createElement(pn.Text,{className:["payment-block payment-leading-18 payment-text-14 payment-mb-15 payment-text-gray sm:payment-mt-15 payment-mt-5",a].join(" ").trim()},n),_().createElement("div",{role:"list",className:["payment-text-14 payment-leading-18",i].join(" ").trim()},o))},Qn=Wn,Zn={priceList:"payment-flex payment-justify-between sm:payment-justify-normal payment-mb-5 last:payment-mb-0",errorList:"payment-mb-5 last:payment-mb-0 payment-text-red",accountList:"payment-mb-5 last:payment-mb-0 payment-text-gray payment-p-15 payment-pt-10 sm:payment-pt-15 payment-bg-gray-3 payment-rounded-10"},$n=function(e){var n=e.cardDetails,r=e.label,a=e.labelDescription,i=e.priceSettings,o=void 0===i?{showZeroDecimalPart:!0,price:0}:i,l=e.variant,u=e.className,s=(e.children,e.inputRef),c=t(e,["cardDetails","label","labelDescription","priceSettings","variant","className","children","inputRef"]),m=r+" - "+a,d=function(e){e&&e.scrollIntoView({behavior:"smooth",block:"center"})};return _().createElement("div",b({role:"listitem",className:["",Zn[l],u].join(" ").trim()},c),"priceList"===l&&_().createElement(_().Fragment,null,_().createElement(pn.Text,{className:"payment-text-14 payment-leading-18 sm:payment-min-w-[153px]"},_().createElement("strong",null,r)),_().createElement(pn.Price,{language:o.language?o.language:"en",showZeroDecimalPart:o.showZeroDecimalPart,price:"number"==typeof a?a:0,variant:"defaultPrice",className:"!payment-text-14 payment-leading-18  payment-font-normal"})),"errorList"===l&&_().createElement(_().Fragment,null,_().createElement("span",{className:"payment-text-14","aria-hidden":"true"},"•"),_().createElement(pn.Link,{variant:"textRed",size:"small",href:"javascript:void(0)","aria-label":m,className:"payment-font-bold payment-ml-5",onClick:function(){var e,t,n,r;if((null==s?void 0:s.current)instanceof HTMLSelectElement)for(n=null===(e=null==s?void 0:s.current)||void 0===e?void 0:e.previousElementSibling;n;){if("BUTTON"===n.tagName)return d(r=n),void r.focus();n=n.previousElementSibling}else d(null==s?void 0:s.current),null===(t=null==s?void 0:s.current)||void 0===t||t.focus()}},r),_().createElement("span",{className:"payment-text-gray payment-text-14"}," - ",a)),"accountList"===l&&_().createElement("div",{className:"payment-flex payment-flex-wrap payment-justify-between"},_().createElement(pn.Text,{className:"payment-mr-5 payment-mt-5 sm:payment-mt-0","aria-hidden":"true"},_().createElement("strong",{className:"payment-text-black"},r)," ",a),_().createElement(pn.Text,{elementType:"span",className:"payment-mt-5 sm:payment-mt-0"},n)))},Jn=$n,er=[{label:"Account holder name",labelDescription:"This information is required."},{label:"Transit number",labelDescription:"This information is required."},{label:"Bank name",labelDescription:"This information is required."},{label:"Account number",labelDescription:"This information is required."}],(0,dn.injectIntl)(function(e){var t=e.intl;return f.createElement(pn.Alert,{variant:"error",className:"brui-block sm:brui-flex brui-px-0 sm:brui-px-30 payment-py-30 brui-border-x-0 sm:payment-border-x-1 payment-border-y-1 brui-relative brui-rounded-none sm:payment-rounded-20",iconSize:"36",id:"alert-3"},f.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-[7px]"},f.createElement(pn.Heading,{level:"h2",variant:"xs",className:" sm:brui-mt-7 brui-mb-15 brui-font-sans brui-leading-22"},f.createElement("span",{"aria-hidden":"true"},t.formatMessage({id:"ALERT_ERROR_HEADING"})),f.createElement("span",{className:"payment-sr-only"},t.formatMessage({id:"ALERT_ERROR_HEADING_SR"}))),f.createElement("div",null,f.createElement(Qn,null,er.map(function(e){return f.createElement(Jn,{label:e.label,labelDescription:e.labelDescription,variant:"errorList"})})))))}),(0,dn.injectIntl)(function(e){var t=e.children,n=e.intl;return f.createElement(pn.Alert,{variant:"error",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-2"},f.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0 brui-flex-1"},f.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-5 brui-font-sans brui-text-red "},n.formatMessage({id:"ALERT_ERROR_HEADING_SOME_BALANCE"})),f.createElement("div",null,t),f.createElement(pn.Button,{className:"brui-mt-30",variant:"primary",size:"regular"},n.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))}),tr=(0,dn.injectIntl)(function(e){var t=e.children,n=e.intl;return f.createElement(pn.Alert,{variant:"error",className:"payment-block sm:payment-flex payment-px-0 sm:payment-px-30 payment-py-30 payment-border-x-0 sm:payment-border-x-1 payment-border-y-1 payment-relative payment-rounded-none sm:payment-rounded-20",iconSize:"36",id:"alert-3","aria-labelledby":"error-alert-1 error-alert-2 error-alert-3"},f.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-0"},f.createElement(pn.Heading,{id:"error-alert-1",level:"h2",variant:"xs",className:" sm:payment-mt-7 payment-mb-15 payment-font-sans payment-leading-22"},f.createElement("span",{"aria-hidden":"true"},n.formatMessage({id:"ALERT_ERROR_HEADING"})),f.createElement("span",{className:"payment-sr-only"},n.formatMessage({id:"ALERT_ERROR_HEADING_SR"}))),f.createElement("div",null,t)))}),(0,dn.injectIntl)(function(e){return e.intl,f.createElement(pn.Alert,{variant:"error",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-2"},f.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},f.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-12 brui-text-red brui-font-sans"},f.createElement("strong",null,"Your balance of $195.45 was not paid")," due to an error processing your request."),f.createElement("p",{className:"brui-text-14 brui-my-15 brui-text-gray"},"A separate one-time payment must be made to pay this balance, or risk late fees."),f.createElement(pn.Button,{variant:"primary",size:"regular"},"Make a Payment")))}),(0,dn.injectIntl)(function(e){var t=e.intl;return f.createElement(pn.Alert,{variant:"info",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-3"},f.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},f.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-12 brui-font-sans brui-font-bold brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_HEADING"})),f.createElement("p",{className:"brui-text-14 brui-my-15 brui-text-gray"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_DESC"})),f.createElement(pn.Text,{elementType:"div",className:"sm:brui-flex brui-block"},f.createElement(pn.Text,{elementType:"div",className:"brui-pr-0 sm:brui-pr-10"},f.createElement(pn.Button,{variant:"primary",size:"regular"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))))}),(0,dn.injectIntl)(function(e){var t=e.intl;return f.createElement(pn.Alert,{variant:"success",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-3"},f.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},f.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-15 brui-font-sans brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_HEADING"})),f.createElement(pn.Text,{elementType:"div",role:"list",className:"brui-text-14 brui-mb-30 brui-text-black brui-ml-5"},f.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15"},f.createElement(pn.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),f.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE"}))),f.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15 brui-text-black"},f.createElement(pn.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),f.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})))),f.createElement("p",{className:"brui-text-14 brui-mb-5 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})," ",f.createElement("strong",null,"000011")),f.createElement("p",{className:"brui-text-14 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC"}),f.createElement(pn.Link,{variant:"textBlue",size:"small",href:"https://www.bell.ca/",className:""}," ",t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})))))}),(0,dn.injectIntl)(function(e){var t=e.intl;return f.createElement(pn.Alert,{variant:"success",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-3"},f.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},f.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-15 brui-font-sans brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_HEADING"})),f.createElement(pn.Text,{elementType:"div",role:"list",className:"brui-text-14 brui-mb-30 brui-text-black brui-ml-5"},f.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15"},f.createElement(pn.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),f.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE"})))),f.createElement("p",{className:"brui-text-14 brui-mb-5 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})," ",f.createElement("strong",null,"000011")),f.createElement("p",{className:"brui-text-14 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC"}),f.createElement(pn.Link,{variant:"textBlue",size:"small",href:"https://www.bell.ca/",className:""}," ",t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})))))}),(0,dn.injectIntl)(function(e){var t=e.intl;return f.createElement(pn.Alert,{variant:"success",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-3"},f.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},f.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-15 brui-font-sans brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_HEADING"})),f.createElement(pn.Text,{elementType:"div",role:"list",className:"brui-text-14 brui-mb-30 brui-text-black brui-ml-5"},f.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15"},f.createElement(pn.Icon,{className:"brui-text-12 brui-text-blue brui-mt-3",iconClass:"bi_check_light",iconName:"bi_check_light"}),f.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_CURRENT_BALANCE"}))),f.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15"},f.createElement(pn.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),f.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE"})))),f.createElement("p",{className:"brui-text-14 brui-mb-5 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})," ",f.createElement("strong",null,"000011")),f.createElement("p",{className:"brui-text-14 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC"}),f.createElement(pn.Link,{variant:"textBlue",size:"small",href:"https://www.bell.ca/",className:""}," ",t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})))))}),(0,dn.injectIntl)(function(e){var t=e.intl;return f.createElement(pn.Alert,{variant:"success",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36",id:"alert-3"},f.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},f.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-15 brui-font-sans brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_HEADING"})),f.createElement(pn.Text,{elementType:"div",role:"list",className:"brui-text-14 brui-mb-30 brui-text-black brui-ml-5"},f.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15"},f.createElement(pn.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),f.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE"}))),f.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start brui-mb-15 brui-text-black"},f.createElement(pn.Icon,{className:"brui-text-20 brui-text-blue",iconClass:"bi_check_light",iconName:"icon.bi_check_small_flat_fin"}),f.createElement("span",{className:"brui-text-16 brui-leading-20 brui-ml-10"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})))),f.createElement("p",{className:"brui-text-14 brui-mb-5 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE_AUTOPAY_CREDITS"})," ",f.createElement("strong",null,"000011")),f.createElement("p",{className:"brui-text-14 brui-text-gray brui-leading-18"},t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC"}),f.createElement(pn.Link,{variant:"textBlue",size:"small",href:"https://www.bell.ca/",className:""}," ",t.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})))))}),(0,dn.injectIntl)(function(e){var t=e.intl,n=e.children;return f.createElement(pn.Alert,{variant:"warning",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36"},f.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},f.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-5 brui-font-sans brui-font-bold"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_HEADING"})),f.createElement("div",null,n),f.createElement(pn.Button,{className:"brui-mt-30",variant:"primary",size:"regular"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))}),(0,dn.injectIntl)(function(e){var t=e.intl;return f.createElement(pn.Alert,{variant:"warning",className:"brui-border brui-relative sm:brui-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 brui-py-30 sm:brui-flex brui-block",iconSize:"36"},f.createElement(pn.Text,{elementType:"div",className:"brui-pl-0 brui-pt-15 sm:brui-pl-16 sm:brui-pt-0"},f.createElement(pn.Heading,{level:"h2",variant:"xs",className:"brui-mb-0 sm:brui-mb-12 brui-font-sans brui-font-bold brui-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_HEADING"})),f.createElement("p",{className:"brui-leading-18 brui-text-14 brui-mt-5 brui-mb-15 sm:brui-mt-0 sm:brui-mb-0 sm:brui-my-15 brui-text-gray"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_DESC_1"}),f.createElement(pn.Price,{language:"en",showZeroDecimalPart:!0,price:195.45,variant:"defaultPrice",className:"!brui-text-14 brui-leading-14 brui-m-5 brui-font-normal brui-inline-block"}),f.createElement("span",{className:"brui-sr-only"},"195 point 45 dollars")," ",t.formatMessage({id:"ALERT_CONFIRMATION_INFO_DESC_2"})),f.createElement(pn.Text,{elementType:"div",className:"sm:brui-flex brui-block"},f.createElement(pn.Text,{elementType:"div",className:"brui-pr-0 sm:brui-pr-10"},f.createElement(pn.Button,{variant:"primary",size:"regular"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))))}),(0,dn.injectIntl)(function(e){var t=e.intl;return f.createElement(pn.Alert,{variant:"warning",className:"payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-20 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block",iconSize:"36"},f.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0"},f.createElement(pn.Heading,{level:"h2",variant:"xs",className:"payment-mb-15 payment-font-sans payment-font-bold"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_HEADING"})),f.createElement("p",{className:"payment-text-14 payment-mb-10 payment-text-gray"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_DESC"})),f.createElement(pn.Text,{elementType:"div",className:"payment-mb-30 payment-mt-15"},f.createElement(pn.Text,{elementType:"div",className:"payment-flex payment-justify-between sm:payment-justify-normal"},f.createElement("label",{className:"payment-text-14 sm:payment-basis-1/4"},f.createElement("strong",null,"**********")),f.createElement(pn.Price,{language:"en",showZeroDecimalPart:!0,price:206.98,variant:"defaultPrice",className:"!payment-text-14 payment-leading-14 payment-m-5 payment-font-normal"}))),f.createElement(pn.Button,{variant:"primary",size:"regular"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))}),nr=function(e){var t,n=e.intl,r=e.submitMultiOrderPayment,a=e.accountInputValue,i=e.isBankPayment,o=e.checkedBillItems,l=e.language,u=e.paymentItem,s=e.creditCardAutopayOffers,c=e.debitCardAutopayOffers,m=n.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_MESSAGE"}),d=i?"fr"===l?"ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD_FR":"ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PAD":"ALERT_CONFIRMATION_OTP_SUCCESS_MESSAGE_PACC",p={label:n.formatMessage({id:"PAYMENT_METHOD"}),debits:i?u&&u.length>1?(t=[],c&&(null==c||c.map(function(e){o&&o.map(function(n){e.Ban===n.Ban&&t.push(e)})})),t):c:null,credits:i?null:u&&u.length>1?function(){var e=[];return s&&(null==s||s.map(function(t){o&&o.map(function(n){t.Ban===n.Ban&&e.push(t)})})),e}():s},b=p&&p.credits&&p.credits.length>0&&p.credits[0].AutopayEligibleSubscribers&&p.credits[0].AutopayEligibleSubscribers.length>0&&p.credits[0].AutopayEligibleSubscribers[0].autopayOffers&&p.credits[0].AutopayEligibleSubscribers[0].autopayOffers.length>0||p&&p.debits&&p.debits.length>0&&p.debits[0].AutopayEligibleSubscribers&&p.debits[0].AutopayEligibleSubscribers.length>0&&p.debits[0].AutopayEligibleSubscribers[0].autopayOffers&&p.debits[0].AutopayEligibleSubscribers[0].autopayOffers.length>0;return f.createElement(pn.Alert,{variant:"success",className:"brui-border brui-relative sm:payment-py-30 sm:brui-px-30 brui-rounded-20 brui-p-16 brui-px-15 payment-py-30 sm:payment-flex brui-block",iconSize:"36",id:"alert-3",role:""},f.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 sm:payment-pl-16 payment-mt-15 sm:payment-mt-0 sm:brui-pt-0"},f.createElement(pn.Heading,{level:"h3",variant:"xs",className:"brui-mb-15 brui-font-sans brui-leading-22"},n.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_HEADING"})),f.createElement(pn.Text,{elementType:"div",role:"list",className:"brui-text-14 brui-mb-30 brui-text-black brui-ml-5"},r.map(function(e,t){var r,i,l=null===(r=a.find(function(t){return t.transactionID===e.OrderFormId}))||void 0===r?void 0:r.accountNumber,u=(null===(i=o.find(function(e){return e.Ban===l}))||void 0===i?void 0:i.NickName)||l,s=n.formatMessage({id:d},{account:u});return f.createElement(f.Fragment,null,null!=(null==e?void 0:e.otp)&&(null==e?void 0:e.otp.isSuccess)&&f.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start payment-mb-15"},f.createElement(pn.Icon,{className:"brui-text-12 brui-text-blue payment-mt-3",iconClass:"bi_brui",iconName:"bi_check_light"}),f.createElement("span",{className:"brui-text-16 brui-leading-20 payment-ml-10",dangerouslySetInnerHTML:{__html:s}})))}),f.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start payment-mb-15"},f.createElement(pn.Icon,{className:"brui-text-12 brui-text-blue payment-mt-3 ",iconClass:"bi_brui",iconName:"bi_check_light"}),f.createElement("span",{className:"brui-text-16 brui-leading-20 payment-ml-10"},f.createElement("div",{dangerouslySetInnerHTML:{__html:m}}))),b?f.createElement(pn.Text,{elementType:"div",role:"listitem",className:"brui-flex brui-items-start payment-mb-15"},f.createElement(pn.Icon,{className:"brui-text-12 brui-text-blue payment-mt-3",iconClass:"bi_brui",iconName:"bi_check_light"}),f.createElement("span",{className:"brui-text-16 brui-leading-20 payment-ml-10"},n.formatMessage({id:"AUTOPAY_ALERT"}))):""),f.createElement("p",{className:"brui-text-14 brui-mb-5 brui-text-gray brui-leading-18"},n.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_NUMBER"})," ",f.createElement("strong",null,r[0].PaymentConfirmationNumber)),f.createElement("p",{className:"brui-text-14 brui-text-gray brui-leading-18"},f.createElement(dn.FormattedMessage,{id:"ALERT_CONFIRMATION_SUCCESS_DESC",values:{email:f.createElement("strong",null,r[0].ConfirmationEmailAddress)}})," ",f.createElement(pn.Link,{variant:"textBlue",size:"small",href:"/MyProfile/EditProfile?editField=EMAIL_ADDRESS",className:""},n.formatMessage({id:"ALERT_CONFIRMATION_SUCCESS_DESC_LINK"})),".")))},rr=(0,dn.injectIntl)(nr),ar={greatNews:{className:"brui-text-15 brui-text-blue",iconClass:"bi_brui",iconName:"bi_tag_note-big"},notifCardWarning:{className:"brui-text-20 brui-text-yellow",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}},ir=function(e){var t=e.intl,n=e.hasNotifCard,r=void 0!==n&&n,a=e.children,i=e.label,o=e.variant,l=t.formatMessage({id:"ALERT_GREAT_NEWS_NOTE"}),u=t.formatMessage({id:"ALERT_GREAT_NEWS_NOTE_DESC"});return f.createElement(pn.Card,{variant:"gray",radius:!0,className:["brui-flex brui-flex-col sm:brui-flex-row brui-p-15 brui-gap-15 brui-rounded-[16px]",r?"":"brui-hidden"].join(" ").trim()},f.createElement("div",{className:"brui-flex brui-size-20 brui-items-start payment-pb-15 payment-pr-15"},f.createElement(pn.Icon,{className:["",ar[o].className].join(" ").trim(),iconClass:["",ar[o].iconClass].join(" ").trim(),iconName:["",ar[o].iconName].join(" ").trim()})),f.createElement("div",{className:"brui-flex-grow"},f.createElement("p",{className:"brui-text-14 brui-leading-18 brui-text-gray brui-mb-10"},i),a,f.createElement("div",{className:"brui-text-12 brui-text-gray brui-leading-14"},f.createElement("strong",null,l),u)))},(0,dn.injectIntl)(ir),or=function(e){var t=e.intl,n=e.isErrorCardNumber,r=e.isErrorCardName,a=e.isErrorExpiryDate,i=e.isErrorSecurityCode,o=e.isErrorBankAccountHolderName,l=e.isErrorBankAccountNumber,u=e.isErrorBankName,s=e.iserrorBankTransit,c=e.inputRefs;return f.createElement(pn.Alert,{variant:"error",className:"payment-block sm:payment-flex brui-px-0 sm:payment-px-30 payment-py-30 brui-border-x-0 sm:payment-border-x-1 payment-border-y-1 payment-relative brui-rounded-none sm:payment-rounded-20",iconSize:"36",id:"alert-3"},f.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-[7px]"},f.createElement(pn.Heading,{level:"h2",variant:"xs",className:" sm:brui-mt-7 brui-mb-15 brui-font-sans brui-leading-22"},f.createElement("span",{id:"error-1","aria-hidden":"true"},t.formatMessage({id:"ALERT_ERROR_HEADING"})),f.createElement("span",{className:"payment-sr-only"},t.formatMessage({id:"ALERT_ERROR_HEADING_SR"}))),f.createElement("div",null,f.createElement(Qn,null,n&&f.createElement(Jn,{id:"error-2",label:t.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==c?void 0:c.inputCreditCardNumber}),r&&f.createElement(Jn,{id:"error-3",label:t.formatMessage({id:"CREDIT_CARD_NAME_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==c?void 0:c.inputCreditCardHolderName}),a&&f.createElement(Jn,{id:"error-4",label:t.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==c?void 0:c.inputCreditCardExpiryMonth}),i&&f.createElement(Jn,{id:"error-5",label:t.formatMessage({id:"CREDIT_CARD_SECURITY_CODE_INPUT_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==c?void 0:c.inputCreditCardSecurityCode}),u&&f.createElement(Jn,{id:"error-2",label:t.formatMessage({id:"BANK_NAME_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==c?void 0:c.inputBankName}),o&&f.createElement(Jn,{id:"error-3",label:t.formatMessage({id:"BANK_HOLDER_NAME_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==c?void 0:c.inputBankAccountHolder}),s&&f.createElement(Jn,{id:"error-4",label:t.formatMessage({id:"BANK_TRANSIT_NUMBER_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==c?void 0:c.inputTransitNumber}),l&&f.createElement(Jn,{id:"error-5",label:t.formatMessage({id:"BANK_ACCOUNT_NUMBER_LABEL"}),labelDescription:t.formatMessage({id:"ALERT_ERROR_GENERAL_DESC"}),variant:"errorList",inputRef:null==c?void 0:c.inputBankAccountNumber})))))},lr=(0,dn.injectIntl)(or),ur=function(e){var t=e.intl,n=e.interact;return f.createElement(pn.Alert,{variant:"error",className:"payment-block sm:payment-flex brui-px-0 sm:payment-px-30 payment-py-30 brui-border-x-0 sm:payment-border-x-1 payment-border-y-1 payment-relative brui-rounded-none sm:payment-rounded-20",iconSize:"36",id:"alert-3"},f.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-10 sm:payment-pl-15 sm:payment-pt-0 md:payment-pt-7"},f.createElement(pn.Heading,{level:"h2",variant:"xs",className:" sm:brui-mt-7 brui-mb-15 md:payment-mb-7 brui-font-sans brui-leading-22"},f.createElement("span",{"aria-hidden":"true"},(n.includes(Ln),t.formatMessage({id:"ALERT_ERROR_HEADING_INTERAC"}))),f.createElement("span",{className:"payment-sr-only"},(n.includes(Ln),t.formatMessage({id:"ALERT_ERROR_HEADING_INTERAC_SR"})))),f.createElement("div",null,f.createElement("p",{className:"brui-text-14 brui-my-15 brui-text-gray"},(n.includes(Ln),t.formatMessage({id:"ALERT_ERROR_HEADING_INTERAC_DESC"}))))))},sr=(0,dn.injectIntl)(ur),cr=function(e){var t=e.intl,n=e.checkedCurrentBalanceItems,r=e.submitMultiOrderPayment,a=e.accountInputValue,i=e.language,o=e.notOptedBalanceItems,l=e.setOmnitureOnOneTimePaymentFailure,u=t.formatMessage({id:"ALERT_ERROR_OTP_ALL_BALANCE"}),s=t.formatMessage({id:"ALERT_ERROR_HEADING_SOME_BALANCE"}),c=t.formatMessage({id:"ALERT_ERROR_OTP_BALANCE_DESC"}),m=t.formatMessage({id:"ALERT_ERROR_OPTED_NOT_TO_PAY_SINGULAR"}),d=t.formatMessage({id:"ALERT_ERROR_OPTED_NOT_TO_PAY_PLURAL"}),p=r.filter(function(e){return(null==e?void 0:e.otp)&&!(null==e?void 0:e.otp.isSuccess)}),b=p.map(function(e){return null==e?void 0:e.OrderFormId}),E=a.filter(function(e){return b.includes(e.transactionID)}).map(function(e){return e.accountNumber}),_=null==n?void 0:n.filter(function(e){return E.includes(e.Ban)}),y=n,N=y.length>_.length,g=y.length===_.length&&p.length>1,A=1===y.length?t.formatMessage({id:"ALERT_ERROR_OTP_BALANCE"},{balance:y[0].DueStr}):"",C=1===y.length?t.formatMessage({id:"ALERT_ERROR_OTP_BALANCE_SR"},{balance:y[0].DueStr}):"";return f.useEffect(function(){setTimeout(function(){(g||N)&&l({s_oPYM:"",s_oCCDT:""})},1e3)},[N,g]),f.createElement(pn.Alert,{variant:"error",className:"payment-block payment-border payment-rounded-20 sm:payment-flex payment-px-15 sm:payment-px-30 payment-py-30 sm:payment-pb-45 payment-border-y-1 brui-relative brui-rounded-none sm:payment-rounded-20",iconSize:"36",id:"alert-otp-fail"},f.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-15 sm:payment-pl-15 sm:payment-pt-0"},f.createElement(pn.Heading,{level:"h3",variant:"xs",className:"payment-mb-5 payment-font-sans brui-text-red"},1===y.length&&f.createElement(f.Fragment,null,f.createElement("span",{"aria-hidden":"true",dangerouslySetInnerHTML:{__html:A}}),f.createElement("span",{className:"payment-sr-only"},C)),N&&f.createElement("span",{dangerouslySetInnerHTML:{__html:s}}),g&&f.createElement("span",{dangerouslySetInnerHTML:{__html:u}})),f.createElement(pn.Text,{elementType:"div"},f.createElement("span",{className:"payment-text-14 payment-leading-18 payment-text-gray",dangerouslySetInnerHTML:{__html:c}})),(N||g)&&f.createElement("div",{className:"!payment-border-none payment-mt-15"},f.createElement(Qn,{label:""},_.map(function(e){return f.createElement(Jn,{label:e.NickName,labelDescription:e.Due,variant:"priceList",priceSettings:{language:i,showZeroDecimalPart:!0}})})),N&&o.length>0&&f.createElement("div",{className:"payment-border-t-gray-4 payment-mt-15"},f.createElement(Qn,{label:1===o.length?m:d},o.map(function(e){return f.createElement(Jn,{label:e.NickName,labelDescription:e.Due,variant:"priceList",priceSettings:{language:i,showZeroDecimalPart:!0}})})))),f.createElement(pn.Button,{className:"payment-mt-30",variant:"primary",size:"regular",onClick:function(){location.href="".concat(t.formatMessage({id:"CTA_MAKE_PAYMENT_LINK"}))}},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))},mr=(0,dn.injectIntl)(cr),dr=function(e){var t,n=e.intl,r=e.className,a=e.paymentItem,i=e.isPreauth,o=e.inputValue,l=e.inputBankValue,s=e.isBankPaymentSelected,c=e.isNewbank,m=e.onEditClick,d=e.showHeading,p=(e.isSingleClickEnable,e.bankList),b=e.debitCardAutopayOffers,E=e.creditCardAutopayOffers,_=e.checkedBillItems,y=e.bankitems,N=e.isConfirmation,g=e.IsAutopayCreditEnabled,A=e.isShow,C=a.filter(function(e){return!0===e.IsOnPreauthorizedPayments&&e.CreditCardDetails}),v=C.length>0?C[0].CreditCardDetails:null,T=function(){var e;if(c||!1===i)return!0===s?[{label:n.formatMessage({id:"PAYMENT_METHOD"}),value:null==l?void 0:l.PaymentMethod},{label:n.formatMessage({id:"ACCOUNT_HOLDER"}),value:null==l?void 0:l.AccountHolder},{label:n.formatMessage({id:"BANK_NAME"}),value:null===(e=p.filter(function(e){return e.Value===(null==l?void 0:l.BankName)})[0])||void 0===e?void 0:e.Text},{label:n.formatMessage({id:"TRANSIT_NUMER"}),value:null==l?void 0:l.TransitNumber},{label:n.formatMessage({id:"ACCOUNT_NUMBER"}),value:(null==l?void 0:l.AccountNumber)?"*******".concat(String(l.AccountNumber).slice(-3)):null==l?void 0:l.AccountNumber}]:[{label:n.formatMessage({id:"CREDIT_CARD_TYPE_LABEL"}),value:o.cardType},{label:n.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL_V2"}),value:c&&o.cardNumber?"*******".concat(String(o.cardNumber).slice(-4)):o.cardNumber},{label:n.formatMessage({id:"CREDIT_CARD_NAME_LABEL_V2"}),value:o.cardName},{label:n.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"}),value:o.expiryDate}];if(!1===c||!0===i){if(!0===s)return[{label:n.formatMessage({id:"PAYMENT_METHOD"}),value:null==l?void 0:l.PaymentMethod},{label:n.formatMessage({id:"ACCOUNT_HOLDER"}),value:null==l?void 0:l.AccountHolder},{label:n.formatMessage({id:"BANK_NAME"}),value:null==l?void 0:l.BankName},{label:n.formatMessage({id:"TRANSIT_NUMER"}),value:null==l?void 0:l.TransitNumber},{label:n.formatMessage({id:"ACCOUNT_NUMBER"}),value:(null==l?void 0:l.AccountNumber)?"*******".concat(String(l.AccountNumber).slice(-3)):null==l?void 0:l.AccountNumber}];if(o)return[{label:n.formatMessage({id:"CREDIT_CARD_TYPE_LABEL"}),value:o.cardType},{label:n.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL_V2"}),value:c&&o.cardNumber?"*******".concat(String(o.cardNumber).slice(-4)):o.cardNumber},{label:n.formatMessage({id:"CREDIT_CARD_NAME_LABEL_V2"}),value:o.cardName},{label:n.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"}),value:o.expiryDate}];if(v)return[{label:n.formatMessage({id:"CREDIT_CARD_TYPE_LABEL"}),value:u(v.CreditCardType)},{label:n.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL_V2"}),value:null==v?void 0:v.CreditCardNumberMasked},{label:n.formatMessage({id:"CREDIT_CARD_NAME_LABEL_V2"}),value:null==v?void 0:v.CardholderName},{label:n.formatMessage({id:"CREDIT_CARD_EXPIRY_LABEL"}),value:null==v?void 0:v.ExpirationDateDisplayViewA}]}return[]};return f.createElement("div",{className:r},f.createElement("div",{className:N?"payment-border-gray-4":"payment-border-b payment-border-gray-4"},f.createElement("div",{className:d?"payment-flex payment-items-center payment-justify-between payment-mt-0":"payment-hidden"},f.createElement(pn.HeadingStep,{disableSrOnlyText:!0,status:"complete",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:n.formatMessage({id:"PAYMENT_SUMMARY_TITLE"}),id:A?"payment-setup-heading":void 0,"aria-hidden":A?"true":void 0}),f.createElement("div",{className:"payment-pt-45"},f.createElement(pn.IconLink,{icon:f.createElement(pn.Icon,{iconClass:"bi_brui",iconName:"bi_edit_pencil",className:"brui-text-16"}),text:n.formatMessage({id:"CTA_EDIT"}),variant:"textBlue",size:"regular",href:"javascript:void(0);",position:"right",className:["payment-flex payment-items-center !payment-text-14 !payment-leading-18"].join(" ").trim(),"aria-describedby":"pre-auth-payment-summary",onClick:m}))),!N&&g&&(y&&y.length>1?(t=[],s?b&&(null==b||b.map(function(e){_&&_.map(function(n){e.Ban===n.Ban&&t.push(e)})})):E&&(null==E||E.map(function(e){_&&(null==_||_.map(function(n){e.Ban===n.Ban&&t.push(e)}))})),t).reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):s?b&&b.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):E&&E.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0))>0?f.createElement(pn.Text,{elementType:"div",className:"payment-flex payment-items-center payment-mt-5"},f.createElement(pn.Icon,{className:"paymnet-text-15 payment-text-blue payment-mr-5",iconClass:"bi_brui",iconName:"bi_tag_note-big"}),f.createElement("p",{className:"payment-text-gray payment-text-14"},n.formatMessage({id:"REVIEW_PAGE_AUTOPAY_CREDIT"})," ")):null,s?f.createElement("div",{className:N?"payment-block payment-relative":"payment-block payment-relative payment-pb-45"},f.createElement("div",{className:"brui-mt-15"},T().map(function(e,t){return f.createElement(qn,{className:"",label:e.label,value:e.value,"data-index":t,srText:e.label===n.formatMessage({id:"ACCOUNT_NUMBER"})?n.formatMessage({id:"BANK_ACCOUNT_SR_TEXT"},{Account:String(e.value).slice(-3)}):"",needSRText:e.label===n.formatMessage({id:"ACCOUNT_NUMBER"})})}))):f.createElement("div",{className:N?"payment-block payment-relative":"payment-block payment-relative payment-pb-45"},f.createElement("div",{className:"brui-mt-15"},T().map(function(e,t){return f.createElement(qn,{className:"",label:e.label,value:e.label===n.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL_V2"})?"*******".concat(String(e.value).slice(-4)):e.value,"data-index":t,srText:e.label===n.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL_V2"})?n.formatMessage({id:"CREDIT_CARD_SR_TEXT"},{Account:String(e.value).slice(-4)}):"",needSRText:e.label===n.formatMessage({id:"CREDIT_CARD_NUMBER_LABEL_V2"})})})))))},pr=(0,dn.injectIntl)(dr),e(function(e,t){void 0===e&&(e=""),void 0===t&&(t=br.None);var n=ma.call(this,e)||this;return n.errorType=t,n},ma=N.ApplicationError),function(t){e(function(e,n,r,a,i){void 0===i&&(i=br.WebAPIError);var o=t.call(this,n)||this;return o.statusCode=e,o.message=n,o.serverMessage=r,o.apiName=a,o.errorType=i,o},t)}(N.ApplicationError),function(e){e[e.None=0]="None",e[e.NoDataError=1]="NoDataError",e[e.WebAPIError=2]="WebAPIError",e[e.GetPreviousBillAPIError=3]="GetPreviousBillAPIError",e[e.Exception=4]="Exception",e[e.ApplicationError=5]="ApplicationError",e[e.NoTooltipDataError=6]="NoTooltipDataError",e[e.GetOverageAPIError=7]="GetOverageAPIError"}(br||(br={})),function(e){e.Empty="EMPTY",e.CreditCardExpireDate="CREDIT_CARD_EXPIRY",e.Invalid="INVALID"}(Er||(Er={})),fr=function(){},_r=function(e){var t,n,r,i,o,s,c,m,d,p,b,E,_,y,N,g,A,C,v,T,h,I=e.intl,R=e.isHeadingStepActive,S=e.paymentItem,O=e.creditcardDetails,x=e.onCreditCardNumberChange,M=e.onCardHolderNameChange,L=e.onCreditCardExpiryDateChange,D=e.onSecurityCodeChange,B=e.resetValidationErrors,P=e.validationErrors,k=e.isSingleClickEnableForPAD,U=e.isSingleClickEnableForPACC,F=e.setHeadingSteps,H=e.setCurrentSection,G=e.currentSection,V=e.setInputValue,z=e.inputValue,q=e.setInputBankValue,K=e.inputBankValue,X=e.setIsBankSelected,W=e.validateFormOrder,Q=e.checkedBillItems,Z=e.tokenizeAndPropagateFormValues,$=e.bankList,J=e.validatBankDetails,ee=e.cardTokenizationSuccess,te=e.redirectUrl,ne=e.interacBankInfo,re=e.accountInputValues,ae=e.interactBankFailureInfo,ie=e.creditCardAutopayOffers,oe=e.debitCardAutopayOffers,le=e.language,ue=e.setOmnitureOnPaymentSelect,se=e.isInteractEnabled,ce=e.IsAutopayCreditEnabled,me=e.InteracCode,de=e.setOmnitureOnInteracFailure,pe=a(f.useState(null==O?void 0:O.CreditCardNumber),2),be=pe[0],Ee=pe[1],fe=a(f.useState("default"),2),_e=fe[0],ye=fe[1],Ne=a(f.useState(""),2),ge=Ne[0],Ae=Ne[1],Ce=a(f.useState(!1),2),ve=Ce[0],Te=Ce[1],he=a(f.useState(!1),2),Ie=he[0],Re=he[1],Se=a(f.useState(!1),2),Oe=Se[0],xe=Se[1],Me=a(f.useState(!1),2),Le=Me[0],De=Me[1],Be=a(f.useState(!1),2),Pe=Be[0],ke=Be[1],we=a(f.useState(!1),2),Ue=we[0],Fe=we[1],He=a(f.useState(!1),2),Ye=He[0],je=He[1],Ge=a(f.useState(!1),2),Ve=Ge[0],ze=Ge[1],qe=a(f.useState(!1),2),Ke=qe[0],Xe=qe[1],We=a(f.useState(""),2),Qe=We[0],Ze=We[1],$e=a(f.useState(!1),2),Je=$e[0],et=$e[1],tt=a(f.useState(!1),2),nt=tt[0],rt=tt[1],at=a(f.useState(!0),2),it=at[0],ot=at[1],lt=a(f.useState(!1),2),ut=lt[0],st=lt[1],ct=a(f.useState(!1),2),mt=ct[0],dt=ct[1],pt=a(f.useState({SelectedPaymentMethod:"",CardholderName:"",CreditCardToken:"",CreditCardType:"",ExpiryYear:"",ExpiryMonth:"",SecurityCode:""}),2),bt=pt[0],Et=pt[1],ft=a(f.useState({SelectedPaymentMethod:"",BankName:"",HolderName:"",TransitCode:"",AccountNumber:"",BankCode:""}),2),_t=ft[0],yt=ft[1],Nt=a(f.useState(I.formatMessage({id:"BANK_ACCOUNT_LABEL"})),2),gt=Nt[0],At=Nt[1],Ct=a(f.useState(!0),2),vt=Ct[0],Tt=Ct[1],ht=f.useRef(null),It={interac:f.useRef(null),manualDetails:f.useRef(null)},Rt={inputCreditCardNumber:f.useRef(null),inputCreditCardHolderName:f.useRef(null),inputCreditCardSecurityCode:f.useRef(null),inputCreditCardExpiryMonth:f.useRef(null),inputCreditCardExpiryYear:f.useRef(null),inputBankName:f.useRef(null),inputBankAccountHolder:f.useRef(null),inputTransitNumber:f.useRef(null),inputBankAccountNumber:f.useRef(null)},St=I.formatMessage({id:"InteracSupportedFinancialInstitutions"}).split(","),Ot=function(e){Ze(e.target.value)};return f.useEffect(function(){Qe===I.formatMessage({id:"NEW_CREDIT_ACCOUNT_LABEL"})||Qe===I.formatMessage({id:"BANK_NEW_BANK_ACCOUNT_LABEL"})?Xe(!0):Xe(!1)},[Qe]),t=function(e){Ae(e.target.value)},n={VISA:I.formatMessage({id:"VISA_CC_PNG"}),MASTERCARD:I.formatMessage({id:"MASTER_CC_PNG"}),AMEX:I.formatMessage({id:"AMEX_CC_PNG"})},r=function(){Te(!1),Re(!1),De(!1),xe(!1),ze(!1),je(!1),ke(!1),Fe(!1)},i=I.formatMessage({id:"PAYMENT_METHOD_DEBIT"}),o=function(e){var t,n,a,o,u,c,m,d,p,b,E,f,_,y,g,C,v,T,h,I,R,S,O,B,P,k,w,U,F,H,Y,j,G,V;e.preventDefault(),r(),vt?(yt({SelectedPaymentMethod:i,BankName:null===(T=$.filter(function(e){var t;return e.Value===(null===(t=Rt.inputBankName.current)||void 0===t?void 0:t.value)})[0])||void 0===T?void 0:T.Text,HolderName:(null===(h=Rt.inputBankAccountHolder.current)||void 0===h?void 0:h.value)?null===(I=Rt.inputBankAccountHolder.current)||void 0===I?void 0:I.value:(null==A?void 0:A.CardHolder)||"",TransitCode:(null===(R=Rt.inputTransitNumber.current)||void 0===R?void 0:R.value)?null===(S=Rt.inputTransitNumber.current)||void 0===S?void 0:S.value:(null==A?void 0:A.TransitCode)||"",AccountNumber:(null===(O=Rt.inputBankAccountNumber.current)||void 0===O?void 0:O.value)?null===(B=Rt.inputBankAccountNumber.current)||void 0===B?void 0:B.value:(null==A?void 0:A.AccountNumber)||"",BankCode:(null===(P=Rt.inputBankName.current)||void 0===P?void 0:P.value)?null===(k=Rt.inputBankName.current)||void 0===k?void 0:k.value.slice(-3):""}),q({PaymentMethod:i,AccountHolder:(null===(w=Rt.inputBankAccountHolder.current)||void 0===w?void 0:w.value)?null===(U=Rt.inputBankAccountHolder.current)||void 0===U?void 0:U.value:(null==A?void 0:A.CardHolder)||"",BankName:(null===(F=Rt.inputBankName.current)||void 0===F?void 0:F.value)?null===(H=Rt.inputBankName.current)||void 0===H?void 0:H.value:(null==A?void 0:A.BankName)||"",TransitNumber:(null===(Y=Rt.inputTransitNumber.current)||void 0===Y?void 0:Y.value)?null===(j=Rt.inputTransitNumber.current)||void 0===j?void 0:j.value:(null==A?void 0:A.TransitCode)||"",AccountNumber:(null===(G=Rt.inputBankAccountNumber.current)||void 0===G?void 0:G.value)?null===(V=Rt.inputBankAccountNumber.current)||void 0===V?void 0:V.value:(null==A?void 0:A.AccountNumber)||""}),J(s())):(Et({SelectedPaymentMethod:"CreditCard",CardholderName:(null===(t=Rt.inputCreditCardHolderName.current)||void 0===t?void 0:t.value)?null===(n=Rt.inputCreditCardHolderName.current)||void 0===n?void 0:n.value:(null==N?void 0:N.CardholderName)||"",CreditCardToken:(null===(a=Rt.inputCreditCardNumber.current)||void 0===a?void 0:a.value)?null===(o=Rt.inputCreditCardNumber.current)||void 0===o?void 0:o.value:(null==N?void 0:N.CreditCardNumber)||"",CreditCardType:(null===(u=Rt.inputCreditCardNumber.current)||void 0===u?void 0:u.value)?Rn(null===(c=Rt.inputCreditCardNumber.current)||void 0===c?void 0:c.value):((null==N?void 0:N.CreditCardType)?l(N.CreditCardType):"")||"",ExpiryYear:(null===(m=Rt.inputCreditCardExpiryYear.current)||void 0===m?void 0:m.value)?null===(d=Rt.inputCreditCardExpiryYear.current)||void 0===d?void 0:d.value:(null==N?void 0:N.ExpireYear)||"",ExpiryMonth:(null===(p=Rt.inputCreditCardExpiryMonth.current)||void 0===p?void 0:p.value)?null===(b=Rt.inputCreditCardExpiryMonth.current)||void 0===b?void 0:b.value:(null==N?void 0:N.ExpireMonth)||"",SecurityCode:(null===(E=Rt.inputCreditCardSecurityCode.current)||void 0===E?void 0:E.value)?null===(f=Rt.inputCreditCardSecurityCode.current)||void 0===f?void 0:f.value:(null==N?void 0:N.SecurityCode)||""}),x(null===(_=Rt.inputCreditCardNumber.current)||void 0===_?void 0:_.value),M(null===(y=Rt.inputCreditCardHolderName.current)||void 0===y?void 0:y.value),L(null===(g=Rt.inputCreditCardExpiryMonth.current)||void 0===g?void 0:g.value,null===(C=Rt.inputCreditCardExpiryYear.current)||void 0===C?void 0:C.value),D(null===(v=Rt.inputCreditCardSecurityCode.current)||void 0===v?void 0:v.value)),ot(!1),et(!0)},f.useEffect(function(){var e,t,n,r,a,i,o,l,s,m;Je&&(null==P||P.errors.map(function(e){switch(e.field){case Y.CardNumber:Te(!0);break;case Y.CardHolderName:Re(!0);break;case Y.ExpirationDate:De(!0);break;case Y.SecurityCode:xe(!0);break;case Y.BankAccountHolderName:ze(!0);break;case Y.BankName:ke(!0);break;case Y.BankTransitCode:Fe(!0);break;case Y.BankAccountNumber:je(!0)}}),(o=!!(null===(e=null==P?void 0:P.errors)||void 0===e?void 0:e.length)&&P.errors.length>0)&&dt(!0),o?!o&&d&&!Ke&&E&&(o||c()):(V({cardNumber:(null===(t=Rt.inputCreditCardNumber.current)||void 0===t?void 0:t.value)||"",cardType:xn((null===(n=Rt.inputCreditCardNumber.current)||void 0===n?void 0:n.value)||""),cardName:(null===(r=Rt.inputCreditCardHolderName.current)||void 0===r?void 0:r.value)||"",expiryDate:"".concat((null===(a=Rt.inputCreditCardExpiryMonth.current)||void 0===a?void 0:a.value)||"","/").concat((null===(i=Rt.inputCreditCardExpiryYear.current)||void 0===i?void 0:i.value)||"")}),c()),o||(l=function(){var e,t,n,r,a,i,o,l,s,c,m,d,p,b;return{cardHolderName:(null===(e=Rt.inputCreditCardHolderName.current)||void 0===e?void 0:e.value)?null===(t=Rt.inputCreditCardHolderName.current)||void 0===t?void 0:t.value:(null==N?void 0:N.CardholderName)||"",creditCardNumber:(null===(n=Rt.inputCreditCardNumber.current)||void 0===n?void 0:n.value)?null===(r=Rt.inputCreditCardNumber.current)||void 0===r?void 0:r.value:(null==N?void 0:N.CreditCardNumber)||"",creditCardToken:(null===(a=Rt.inputCreditCardNumber.current)||void 0===a?void 0:a.value)?null===(i=Rt.inputCreditCardNumber.current)||void 0===i?void 0:i.value:(null==N?void 0:N.CreditCardNumber)||"",expirationMonth:(null===(o=Rt.inputCreditCardExpiryMonth.current)||void 0===o?void 0:o.value)?null===(l=Rt.inputCreditCardExpiryMonth.current)||void 0===l?void 0:l.value:(null==N?void 0:N.ExpireMonth)||"",expirationYear:(null===(s=Rt.inputCreditCardExpiryYear.current)||void 0===s?void 0:s.value)?null===(c=Rt.inputCreditCardExpiryYear.current)||void 0===c?void 0:c.value:(null==N?void 0:N.ExpireYear)||"",securityCode:(null===(m=Rt.inputCreditCardSecurityCode.current)||void 0===m?void 0:m.value)?null===(d=Rt.inputCreditCardSecurityCode.current)||void 0===d?void 0:d.value:(null==N?void 0:N.SecurityCode)||"",cardType:(null===(p=Rt.inputCreditCardHolderName.current)||void 0===p?void 0:p.value)?Rn(null===(b=Rt.inputCreditCardHolderName.current)||void 0===b?void 0:b.value):((null==N?void 0:N.CreditCardType)?u(N.CreditCardType):"")||""}}(),1===p.length&&(s=p[0],vt?W(s.Ban,s.AccountType===w.OneBill,_t,re,vt,s.subscriberId):Z(l,s.Ban,s.AccountType===w.OneBill,bt,vt,s.subscriberId)),p.length>1&&Q&&Q.length>0&&(vt?W(Q[0].Ban,Q[0].AccountType===w.OneBill,_t,re,vt,Q[0].subscriberId):Z(l,Q[0].Ban,Q[0].AccountType===w.OneBill,bt,vt,Q[0].subscriberId))),m=new fr,B(m),et(!1))},[Je]),f.useEffect(function(){ee&&Q&&Q.length>0&&W(Q[0].Ban,Q[0].AccountType===w.OneBill,bt,re,vt,Q[0].subscriberId)},[ee]),s=function(){var e,t,n,r,a,i,o,l,u,s={isValid:!0,validationForm:{bankNameError:{isEmpty:!1,isInvalid:!1},bankAccountHolderError:{isEmpty:!1,isInvalid:!1},transitNumberError:{isEmpty:!1,isInvalid:!1},bankAccountNumberError:{isEmpty:!1,isInvalid:!1}}};return(null===(e=Rt.inputBankName.current)||void 0===e?void 0:e.value)||(s.isValid=!1,s.validationForm.bankNameError.isEmpty=!0),(null===(t=Rt.inputBankAccountHolder.current)||void 0===t?void 0:t.value)?(null===(n=Rt.inputBankAccountHolder.current)||void 0===n?void 0:n.value)&&(On.test(null===(r=Rt.inputBankAccountHolder.current)||void 0===r?void 0:r.value.trim())&&(null===(a=Rt.inputBankAccountHolder.current)||void 0===a?void 0:a.value.trim().length)<=70||(s.isValid=!1,s.validationForm.bankAccountHolderError.isInvalid=!0)):(s.isValid=!1,s.validationForm.bankAccountHolderError.isEmpty=!0),(null===(i=Rt.inputTransitNumber.current)||void 0===i?void 0:i.value)?(null===(o=Rt.inputTransitNumber.current)||void 0===o?void 0:o.value.length)<5&&(s.isValid=!1,s.validationForm.transitNumberError.isInvalid=!0):(s.isValid=!1,s.validationForm.transitNumberError.isEmpty=!0),(null===(l=Rt.inputBankAccountNumber.current)||void 0===l?void 0:l.value)?(null===(u=Rt.inputBankAccountNumber.current)||void 0===u?void 0:u.value.length)<7&&(s.isValid=!1,s.validationForm.bankAccountNumberError.isInvalid=!0):(s.isValid=!1,s.validationForm.bankAccountNumberError.isEmpty=!0),s},c=function(){U||k?H(j.CurrentBalance):(H(j.TermsAndCondition),F(!1))},m=function(){H(j.PaymentMethod)},d=null==S?void 0:S.some(function(e){var t;return null!==(t=e.IsOnPreauthorizedPayments)&&void 0!==t&&t}),p=S,b=S.find(function(e){return e.BankAccountDetails}),E=S.find(function(e){return e.CreditCardDetails}),_=ae&&ae.data?ae.data:null,y=S.filter(function(e){return!0===e.IsOnPreauthorizedPayments&&e.CreditCardDetails}),N=y.length>0?y[0].CreditCardDetails:null,g=S.filter(function(e){return!0===e.IsOnPreauthorizedPayments&&e.BankAccountDetails}),A=g.length>0?g[0].BankAccountDetails:null,C=function(e){Ee(e.target.value);var t=xn(e.target.value);ye(t)},v=function(){window.location.href=te.externalRedirectUrl},T=function(e){var t,n;(null===(n=null===(t=It.manualDetails)||void 0===t?void 0:t.current)||void 0===n?void 0:n.checked)?st(!0):st(!1)},h=function(e){At(e.target.value)},f.useEffect(function(){gt===I.formatMessage({id:"BANK_ACCOUNT_LABEL"})?(Tt(!0),X(!0)):(Tt(!1),X(!1))},[gt]),f.useEffect(function(){null!=ne&&"SUCCESS"===ne.status?rt(!0):rt(!1)},[ne]),f.useEffect(function(){G!==j.PaymentMethod||it&&_||""===me&&ue({error:"",s_oILI:""})},[G]),f.useEffect(function(){Q.length>0&&G===j.PaymentMethod&&(!me||""===me||it&&_?it&&_&&de():ue({error:"",s_oILI:me}))},[me,G,Q]),f.useEffect(function(){G===j.PaymentMethod&&mt&&(vt?(ue({error:"BANKERROR",s_oILI:me}),dt(!1)):(ue({error:"CREDITERROR",s_oILI:me}),dt(!1)))},[mt]),f.createElement(f.Fragment,null,f.createElement(f.Fragment,null,f.createElement("div",{className:["payment-border-b payment-border-gray-4",G>j.PaymentMethod?"payment-hidden":""].join(" ").trim()},f.createElement("div",null,f.createElement(pn.HeadingStep,{disableSrOnlyText:!0,status:R,subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:I.formatMessage({id:"SELECT_PAYMENT_METHOD_HEADING"}),"aria-hidden":G===j.PaymentMethod?"true":void 0,id:G===j.PaymentMethod?"payment-setup-heading":void 0})),ve||Ie||Le||Oe||Ve||Ye||Pe||Ue?f.createElement("div",{className:"payment-pt-30"},f.createElement(lr,{isErrorCardNumber:ve,isErrorCardName:Ie,isErrorSecurityCode:Oe,isErrorExpiryDate:Le,isErrorBankAccountHolderName:Ve,isErrorBankAccountNumber:Ye,isErrorBankName:Pe,iserrorBankTransit:Ue,inputRefs:Rt})):f.createElement(f.Fragment,null),it&&_&&f.createElement("div",{className:"".concat("active"===R?"":"payment-hidden"," payment-pt-30")},f.createElement(sr,{interact:_})),f.createElement("div",{role:"radiogroup","aria-labelledby":"payment-method"},f.createElement("div",{className:"".concat("active"===R?"":"payment-hidden"," payment-pt-30")},f.createElement(Gn,{Checked:!0,isInteracSelected:nt,checkedBillItems:Q,radioCardRef:It,handleBankRadioManualDetailsChange:T,isBankManualEnterDetails:ut,isPreauth:d,hasBankAccountDetails:b,bankitems:p,handleBankRadioChange:Ot,bankListInterac:St,handleInteracSubmit:v,isBankChecked:Ke,inputRefs:Rt,errorBankName:Pe,errorBankTransit:Ue,errorBankAccountNumber:Ye,errorBankAccountHolderName:Ve,radioRef:ht,bankList:$,onChange:h,creditCardAutopayOffers:ie,debitCardAutopayOffers:oe,language:le,isInteractEnabled:se,IsAutopayCreditEnabled:ce}),f.createElement(zn,{isPreauth:d,hasCreditCardDetails:E,bankitems:p,radioRef:ht,handleBankRadioChange:Ot,isBankChecked:Ke,cardNumber:be,handleCreditCardChange:C,inputRefs:Rt,cardIcons:n,cardType:_e,errorCardNumber:ve,errorCardName:Ie,errorExpiryDate:Le,errorSecurityCode:Oe,handleMaskCVV:t,CVV:ge,onChange:h,checkedBillItems:Q,creditCardAutopayOffers:ie,debitCardAutopayOffers:oe,language:le,IsAutopayCreditEnabled:ce}),f.createElement("div",{className:"payment-pt-15 payment-pb-45 sm:payment-pb-60"},f.createElement(pn.Button,{variant:"primary",onClick:o,disabled:!1},I.formatMessage({id:"CTA_NEXT"})))))),f.createElement(Xn,{className:"payment-hidden",onEditClick:m}),f.createElement(pr,{paymentItem:p,className:G>j.PaymentMethod?"":"payment-hidden",isPreauth:d,inputValue:z,inputBankValue:K,isNewbank:null!=Ke&&Ke,onEditClick:m,showHeading:!0,isBankPaymentSelected:vt,isSingleClickEnable:U||k,bankList:$,debitCardAutopayOffers:oe,creditCardAutopayOffers:ie,checkedBillItems:Q,bankitems:p,isConfirmation:!1,IsAutopayCreditEnabled:ce,isShow:G>j.PaymentMethod})))},yr=function(e){return{creditcardDetails:e.creditCardDetails,validationErrors:e.validationErrors,cardTokenizationSuccess:e.cardTokenizationSuccess,redirectUrl:e.redirectUrl,interacBankInfo:e.interacBankInfo,interactBankFailureInfo:e.interactBankFailureInfo}},Nr=function(e){return{onCreditCardNumberChange:function(t){var n,r,a,i,o=new V;o.CreditCardNumber=t,t||(r=t,(i=new fr).errors=new Array,a=new Array,r||a.push(Er.Empty),n=a&&a.length>0?(i.errors.push({valErrors:a,field:Y.CardNumber}),i):[],e(ae(n))),(!n||n.length<=0||!n.errors||n.errors.length<=0)&&e(ee(o))},onCardHolderNameChange:function(t){var n,r,a,i,o,l=new V;l.CardholderName=t,a=t,(o=new fr).errors=new Array,i=new Array,a&&/^(?:[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{2,}\.?\s)+(?:[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{1}\.?\s)*[A-Za-zÀÂÄÈÉÊËÎÏÔŒÙÛÜŸÇàâäèéêëîïôœùûüÿç'-]{2,}\.?$/.test(a.trim())&&a.trim().length<=70||i.push(Er.Empty),(null===(n=null==(r=i&&i.length>0?(o.errors.push({valErrors:i,field:Y.CardHolderName}),o):[])?void 0:r.errors)||void 0===n?void 0:n.length)>0&&e(ae(r)),(!r||r.length<=0||!r.errors||r.errors.length<=0)&&e(te(l))},onCreditCardExpiryDateChange:function(t,n){var r,a=new V;a.ExpireMonth=t,a.ExpireYear=n,r=function(e,t){var n,r,a,i,o=new fr;return o.errors=new Array,n=new Array,r=""===t?t:parseInt(t),(a=""===e?e:parseInt(e))>=12&&(a=0,r+=1),i=new Date(r<=49?2e3+r:1900+r,a,0),""===r.toString()||""===a.toString()?n.push(Er.Empty):i<new Date&&n.push(Er.CreditCardExpireDate),n&&n.length>0?(o.errors.push({valErrors:n,field:Y.ExpirationDate}),o):[]}(t,n),(r.length>0||r.errors)&&e(ae(r)),(!r||r.length<=0||!r.errors||r.errors.length<=0)&&e(re(a))},onSecurityCodeChange:function(t){var n,r,a,i,o=new V;o.SecurityCode=t,t||(r=t,(i=new fr).errors=new Array,a=new Array,r||a.push(Er.Empty),n=a&&a.length>0?(i.errors.push({valErrors:a,field:Y.SecurityCode}),i):[],e(ae(n))),(!n||n.length<=0||!n.errors||n.errors.length<=0)&&e(ne(o))},validatBankDetails:function(t){var n=function(e){var t,n=new fr;return n.errors=new Array,(t=e.validationForm).bankNameError&&(t.bankNameError.isEmpty&&n.errors.push({valErrors:[Er.Empty],field:Y.BankName}),t.bankNameError.isInvalid&&n.errors.push({valErrors:[Er.Invalid],field:Y.BankName})),t.bankAccountHolderError&&(t.bankAccountHolderError.isEmpty&&n.errors.push({valErrors:[Er.Empty],field:Y.BankAccountHolderName}),t.bankAccountHolderError.isInvalid&&n.errors.push({valErrors:[Er.Invalid],field:Y.BankAccountHolderName})),t.transitNumberError&&(t.transitNumberError.isEmpty&&n.errors.push({valErrors:[Er.Empty],field:Y.BankTransitCode}),t.transitNumberError.isInvalid&&n.errors.push({valErrors:[Er.Invalid],field:Y.BankTransitCode})),t.bankAccountNumberError&&(t.bankAccountNumberError.isEmpty&&n.errors.push({valErrors:[Er.Empty],field:Y.BankAccountNumber}),t.bankAccountNumberError.isInvalid&&n.errors.push({valErrors:[Er.Invalid],field:Y.BankAccountNumber})),e.isValid?[]:n}(t);t.isValid?e(ie({errors:[]})):e(ae(n))},resetValidationErrors:function(t){e(ie(t))},validateFormOrder:function(t,n,r,a,i,o){e(ye({ban:t,type:n,details:r,accountInputValue:a,isBankPaymentSelected:i,sub:o}))},tokenizeAndPropagateFormValues:function(t,n,r,a,i,o){e(Te({form:t,ban:n,type:r,details:a,isBankPaymentSelected:i,sub:o}))},setOmnitureOnPaymentSelect:function(t){e(He({data:t}))},setOmnitureOnInteracFailure:function(t){e(Qe(t))}}},gr=(0,y.connect)(yr,Nr)((0,dn.injectIntl)(_r)),Ar=function(e){var t=e.collapseHeightDynamic,n=e.expandHeightDynamic,r=e.intl,a=e.onSubmitClick,i=e.onCancelClick,o=(e.province,e.language),l=e.userProfileProv,u=(0,pn.useWindowResize)(100).width,s=function(e){return"height"in e?e.height:""},c=s(t?(0,pn.useResponsiveHeight)(u,t):{height:"90px"}),m=s(n?(0,pn.useResponsiveHeight)(u,n):{height:"460px"}),d=r.formatMessage({id:"TERMS_AND_CONDITION_DISCLAIMER"}),p=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1"}),b=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2"}),E=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2_ITEM1"}),_=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2_ITEM2"}),y=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM3"}),N=r.formatMessage({id:"TERMS_AND_CON_DESC_2"}),g=r.formatMessage({id:"TERMS_AND_CON_DESC_3"}),A=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_QC"}),C=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2_QC"}),v=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2_ITEM1_QC"}),T=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_2_ITEM2_QC"}),h=r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM3_QC"}),I=r.formatMessage({id:"TERMS_AND_CON_DESC_2_QC"}),R=r.formatMessage({id:"TERMS_AND_CON_DESC_3_QC"});return f.createElement("div",null,f.createElement("div",null,f.createElement(pn.Accordion,{mode:"single"},f.createElement(pn.AccordionItem,{key:1,index:1},f.createElement(pn.AccordionContent,{"aria-labelledby":"terms-and-condition-trigger",id:"terms-and-condition-content",collapseHeight:c,expandHeight:m,className:"brui-text-14 brui-text-gray brui-leading-18 brui-pr-20 payment-overflow-y-scroll payment-scrollbar"},"QC"===l&&"en"===o?f.createElement("div",null,f.createElement("p",null,f.createElement("p",{className:"payment-mb-15"},r.formatMessage({id:"TERMS_AND_CON_DESC_1_QC"})),f.createElement("p",{dangerouslySetInnerHTML:{__html:A}}),f.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10 payment-ml-10"},f.createElement("li",null,f.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM1_QC"}))),f.createElement("li",null,f.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM2_QC"}))),f.createElement("li",null,f.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:h}}))),f.createElement("p",{dangerouslySetInnerHTML:{__html:C}}),f.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10"},f.createElement("li",null,f.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:v}})),f.createElement("li",null,f.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:T}}))),f.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:I}}),f.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:R}}),f.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_DESC_4_QC"})),f.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_ZIP_CODE_QC"})),f.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION_QC"})),f.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION_2_QC"})),f.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_TEL_QC"}))),f.createElement("p",{className:"payment-mt-10"},f.createElement("p",{className:"payment-mb-15"},r.formatMessage({id:"TERMS_AND_CON_DESC_1"})),f.createElement("p",{dangerouslySetInnerHTML:{__html:p}}),f.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10 payment-ml-10"},f.createElement("li",null,f.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM1"}))),f.createElement("li",null,f.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM2"}))),f.createElement("li",null,f.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:y}}))),f.createElement("p",{dangerouslySetInnerHTML:{__html:b}}),f.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10 payment-ml-10"},f.createElement("li",null,f.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:E}})),f.createElement("li",null,f.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:_}}))),f.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:N}}),f.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:g}}),f.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_DESC_4"})),f.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_ZIP_CODE"})),f.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION"})),f.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION_2"})),f.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_TEL"})))):f.createElement("div",null,f.createElement("p",null,f.createElement("p",{className:"payment-mb-15"},r.formatMessage({id:"TERMS_AND_CON_DESC_1"})),f.createElement("p",{dangerouslySetInnerHTML:{__html:p}}),f.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10 payment-ml-10"},f.createElement("li",null,f.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM1"}))),f.createElement("li",null,f.createElement("span",{className:"payment-ml-[-6px]"},r.formatMessage({id:"TERMS_AND_CON_DESC_LIST_1_ITEM2"}))),f.createElement("li",null,f.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:y}}))),f.createElement("p",{dangerouslySetInnerHTML:{__html:b}}),f.createElement("ul",{className:"payment-list-disc payment-list-inside payment-mb-10 payment-ml-10"},f.createElement("li",null,f.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:E}})),f.createElement("li",null,f.createElement("span",{className:"payment-ml-[-6px]",dangerouslySetInnerHTML:{__html:_}}))),f.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:N}}),f.createElement("p",{className:"payment-mb-15",dangerouslySetInnerHTML:{__html:g}}),f.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_DESC_4"})),f.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_ZIP_CODE"})),f.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION"})),f.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_REGION_2"})),f.createElement("p",{className:""},r.formatMessage({id:"TERMS_AND_CON_TEL"}))))),f.createElement("div",{className:"payment-pt-15"},f.createElement(pn.AccordionTrigger,{id:"terms-and-condition-trigger","aria-controls":"terms-and-condition-content",className:"brui-text-14 brui-text-blue brui-underline brui-leading-18 hover:brui-text-blue-1 hover:brui-no-underline"},f.createElement(pn.AccordionToggleTitle,{titleExpand:r.formatMessage({id:"CTA_EXPAND_TERMS"}),titleCollapse:r.formatMessage({id:"CTA_COLLAPSE_TERMS"})})))))),f.createElement("div",{className:"payment-bg-gray-3 payment-mx-[-15px] sm:payment-mx-[-30px] md:payment-mx-[-15px] payment-mt-30"},f.createElement(pn.Divider,{width:1,direction:"horizontal"}),f.createElement("div",{className:"payment-px-15 sm:payment-px-30 md:payment-px-15 payment-pt-30 payment-pb-45"},f.createElement("div",{className:"brui-text-gray brui-text-14 brui-leading-18 payment-max-w-[500px]"},f.createElement("div",{dangerouslySetInnerHTML:{__html:d}})),f.createElement("div",{className:"brui-inline-flex brui-flex-wrap brui-items-center"},f.createElement("div",{className:"payment-pr-30 payment-pt-30"},f.createElement(pn.Button,{variant:"primary",size:"regular",onClick:a},r.formatMessage({id:"CTA_CONFIRM"}))),f.createElement("div",{className:"payment-pt-30"},f.createElement(pn.Button,{variant:"textBlue",size:"regular",className:"!brui-text-14 brui-leading-18",onClick:i},r.formatMessage({id:"CTA_CANCEL"})))))))},Cr=(0,dn.injectIntl)(Ar),vr=function(e){var t=e.intl,n=e.variant,r=void 0===n?"default":n;return f.createElement("div",{className:"payment-bg-black payment-bg-opacity-60 payment-fixed payment-w-full payment-h-full  payment-z-20 payment-left-0 payment-top-0 payment-inline-flex payment-items-center payment-justify-center"},f.createElement("div",{id:"brf-page-loader",role:"alert","aria-busy":"true","aria-live":"assertive",className:"payment-inline-flex payment-items-center payment-py-15 payment-px-30 payment-shadow-md payment-bg-white"},f.createElement("svg",{className:"payment-animate-spin payment-size-[36px] payment-mr-10",xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",viewBox:"3 3 42 42"},f.createElement("defs",null,f.createElement("linearGradient",{id:"loadingIndicatorGradient1",x1:"0",x2:"0",y1:"10%",y2:"90%"},f.createElement("stop",{offset:"0","stop-color":"#04569b"}),f.createElement("stop",{offset:"1","stop-color":"#97b6d2"})),f.createElement("linearGradient",{id:"loadingIndicatorGradient2",x1:"0",x2:"0",y1:"90%",y2:"10%"},f.createElement("stop",{offset:"0","stop-color":"#97b6d2"}),f.createElement("stop",{offset:"1","stop-color":"#fff"}))),f.createElement("path",{fill:"url(#loadingIndicatorGradient1)",d:"M24,3C12,3,3,12,3,24s9,21,21,21l-0.1-2.5c-5.3,0-10.1-2.2-13.5-6C7.4,33.1,5.5,28.3,5.5,24\r\n        c0-4.4,1.9-9.5,5.3-12.9c3.5-3.6,8.6-5.6,13.2-5.6L24,3z"}),f.createElement("path",{fill:"url(#loadingIndicatorGradient2)",d:"M24,3l0,2.4c5.5,0,10.8,2.8,14.3,6.8c2.8,3.4,4.2,7.6,4.2,11.7c0,4.7-2,9.7-5.7,13.3c-3.3,3.3-8.1,5.3-12.9,5.3\r\n        l0,2.5c12,0,21-10,21-21S36,3,24,3z"})),"default"===r&&f.createElement(f.Fragment,null,t.formatMessage({id:"LOADER"})),"submitOrder"===r&&f.createElement("div",{className:"payment-text-12 payment-leading-14"},f.createElement("p",{className:"payment-font-bold"},t.formatMessage({id:"LOADER_SUBMIT"})),f.createElement("p",null,t.formatMessage({id:"LOADER_SUBMIT_DESC"})))))},Tr=(0,dn.injectIntl)(vr),hr=function(e){var t,n,r,i=e.isActive,o=e.intl,l=(e.onCurrentSteps,e.setCurrentSection),u=e.currentSection,s=e.checkedBillItems,c=e.submitFormOrder,m=e.paymentItem,d=e.province,p=e.language,b=e.accountInputValues,E=e.setOmnitureOnReview,_=e.isBankSelected,y=e.validateMultiOrderFormStatus,N=e.tokenizeAndPropagateFormValuesStatus,g=e.setApiSatusIsFailed,A=e.userProfileProv,C=e.setOmnitureOnValidationFailure,v=e.creditCardAutopayOffers,T=e.debitCardAutopayOffers,h=e.bankitems,I=e.sorryCredit,R=e.sorryDebit,S=a(f.useState(Ze.IDLE),2),O=S[0],x=S[1],M=a(f.useState(Ze.IDLE),2),L=M[0],D=M[1],B=function(){var e=[];return T&&(null==T||T.map(function(t){s&&s.map(function(n){t.Ban===n.Ban&&e.push(t)})})),e},P=function(){return h&&h.length>1?B().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):T&&T.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0)},k=function(){return h&&h.length>1?B().reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0):v&&v.reduce(function(e,t){return e+t.AutopayEligibleSubscribers.reduce(function(e,t){return e+t.autopayOffers.length},0)},0)};return I=v&&v.length>0&&0===k()&&P()>0,R=T&&T.length>0&&0===P()&&k()>0,t=function(){m.length>1&&s&&s.length>0&&c(s[0].Ban,s[0].AccountType===w.OneBill,_,I,R,b,s[0].subscriberId),m&&1===m.length&&c(s[0].Ban,s[0].AccountType===w.OneBill,_,I,R,b,s[0].subscriberId),l(j.Confirmation)},n=function(){l(j.PaymentMethod)},r=f.useRef(null),f.useEffect(function(){var e=setTimeout(function(){if(r.current&&i){var e=r.current.querySelector("h2");e&&(e.scrollIntoView({behavior:"smooth"}),e.focus())}},500);return function(){return clearTimeout(e)}},[i]),f.useEffect(function(){u!==j.TermsAndCondition||O!==Ze.COMPLETED&&L!==Ze.COMPLETED||E()},[u,O,L]),f.useEffect(function(){_||(N!==Ze.FAILED?D(Ze.PENDING):D(N))},[N]),f.useEffect(function(){_?(x(y),y===Ze.FAILED&&(g(!0),C())):D(y)},[y]),f.useEffect(function(){L===Ze.FAILED&&(g(!0),C())},[L]),f.createElement(f.Fragment,null,(O===Ze.PENDING||L===Ze.PENDING)&&f.createElement(Tr,null),f.createElement(f.Fragment,null,f.createElement("div",{ref:r,className:"sm:payment-mb-[90px] payment-border-b payment-border-gray-4"},f.createElement("div",{id:"termsAndCondDivID",className:i?"focus-visible:payment-outline-none":""},f.createElement(pn.HeadingStep,{disableSrOnlyText:!0,tabIndex:-1,className:"focus-visible:payment-outline-none",status:i?"active":"inactive",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:"QC"===d&&"en"===p?o.formatMessage({id:"TERMS_AND_CONDITION_HEADING_QC"}):o.formatMessage({id:"TERMS_AND_CONDITION_HEADING"})})),f.createElement("div",{className:["payment-pt-15 sm:payment-pt-5",i?"":"payment-hidden"].join(" ").trim()},f.createElement(Cr,{onSubmitClick:t,onCancelClick:n,collapseHeightDynamic:{mobile:{height:"234px"},tablet:{height:"90px"},desktop:{height:"90px"}},expandHeightDynamic:{mobile:{height:"415px"},tablet:{height:"460px"},desktop:{height:"460px"}},province:d,language:p,userProfileProv:A})))))},Ir=function(e){return{submitMultiOrderPayment:e.submitMultiOrderPayment,validateMultiOrderFormStatus:e.validateMultiOrderFormStatus,tokenizeAndPropagateFormValuesStatus:e.tokenizeAndPropagateFormValuesStatus}},Rr=function(e){return{submitFormOrder:function(t,n,r,a,i,o,l){e(Ae({ban:t,type:n,isbankSelected:r,sorryCredit:a,sorryDebit:i,details:o,sub:l}))},setOmnitureOnReview:function(){e(je({}))},setOmnitureOnValidationFailure:function(t){return e(We({data:t}))}}},Sr=(0,y.connect)(Ir,Rr)((0,dn.injectIntl)(hr)),Or=(0,f.forwardRef)(function(e,t){var n,r,o=e.className,l=e.isDisabled,u=e.isChecked,s=e.label,c=e.id,m=e.billType,d=e.billAccountNumber,p=e.idIndex,E=e.text,f=e.priceSettings,y=e.item,N=e.isCheckedItems,g=e.setIsCheckedItems,A=(e.onChange,e.isShowLabel),C=e.paymentItems,v=e.intl,T=a(_().useState([]),2),h=T[0],I=T[1],R=y.Ban;return _().useEffect(function(){var e,t=document.querySelectorAll("input[type='checkbox']"),n=[];t.forEach(function(e){var t,r,a;e.checked&&(t=e.getAttribute("data-bandetail"),a=(r=t&&JSON.parse(t))&&r.ban&&C.filter(function(e){return e.Ban===r.ban}),a&&a.length>0&&n.push(a[0]))}),n&&n.length>0&&n.map(function(e){return null!==e})&&(e=n.filter(function(e){return null!==e}),I(function(t){return t.concat(i([],a(e),!1))}))},[]),_().useEffect(function(){if(null!==h&&h.length>0){var e=h.reduce(function(e,t){var n=e.find(function(e){return t.Ban===e.Ban});return n||e.push(b({},t)),e},[]);g(e)}},[h]),n=f?v.formatMessage({id:"CHECKBOX_BALANCE"},{balance:f.price}):void 0,r=f?v.formatMessage({id:"CHECKBOX_BALANCE_SR"},{balance:f.price}):void 0,_().createElement(pn.CheckboxCard,{ref:t,id:c,"aria-labelledby":s,disabled:l,defaultChecked:u,className:["group-has-[:disabled]/inputcheckbox:payment-bg-gray-3 payment-chekcbox-disabled payment-group/checkboxcard payment-pt-30 payment-pl-30 brui-pr-30 payment-pb-40 sm:payment-pb-30 sm:payment-p30 payment-basis-unset sm:payment-basis-p48 md:payment-basis-p35 lg:payment-basis-1/3 brui-w-full payment-mr-15 brui-mb-15",o].join(" ").trim(),defaultPadding:!1,checkboxPlacement:"topLeft","data-banDetail":JSON.stringify({id:c,billAccountNumber:d,ban:R,billType:m,price:null==f?void 0:f.price}),onChange:function(e){return function(e,t){e.target.checked?g(i(i([],a(N),!1),[t],!1)):g(function(e){return e.filter(function(e){return e.BillName!==t.BillName})})}(e,y)}},_().createElement("div",{className:"sm:brui-flex payment-pl-[25px] sm:payment-pl-[35px] payment-pr-[15px] sm:payment-pr-[30px] payment-flex-col payment-relative payment-top-[5px] sm:payment-top-0 sm:payment-min-h-[48px]"},_().createElement("div",{className:"brui-flex brui-w-max brui-items-center",id:"checkboxBill".concat(p,"-label-").concat(p)},_().createElement(pn.Text,{elementType:"span",className:"brui-font-bold"},m," ",_().createElement(pn.Text,{elementType:"span",className:"brui-text-14 brui-text-gray !payment-font-normal"},d))),A&&_().createElement("div",{className:"brui-flex brui-w-fit brui-items-center payment-mr-[15px]",id:"checkboxBillBalance-".concat(p,"-label-").concat(p)},f?_().createElement(_().Fragment,null,_().createElement("span",{"aria-hidden":"true",className:"brui-text-14 brui-text-gray brui-break-words brui-flex brui-items-center brui-leading-18"},E),_().createElement("div",{className:"brui-font-bold brui-text-18 brui-text-blue brui-text-darkblue !payment-text-14 brui-leading-14 payment-m-5","aria-hidden":"true"},_().createElement("span",{className:""},n)),r&&_().createElement("span",{className:"brui-sr-only"},r)):_().createElement(pn.Text,{elementType:"span",className:"brui-text-14 brui-text-gray brui-break-words brui-flex brui-items-center brui-leading-18"},E)),_().createElement("div",{className:"payment-flex payment-w-fit payment-items-top",id:"checkboxBill".concat(p,"-label-").concat(p)},!A&&_().createElement(_().Fragment,null,_().createElement(pn.Icon,{className:"payment-text-yellow payment-mr-5",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),_().createElement(pn.Text,{elementType:"span",className:"payment-text-14 payment-text-gray payment-break-words payment-flex payment-items-center payment-leading-18"},v.formatMessage({id:"NOT_ON_PREAUTH"}))))))}),xr=function(e){var t=e.intl,n=e.isActive,r=e.onIconLinkClick,a=(e.banDetails,e.isCheckedItems),i=e.isShow,l={MyBill:t.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:t.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:t.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:t.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:t.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:t.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:t.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:t.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})},u=a.map(function(e){return{label:_().createElement(dn.FormattedMessage,{id:"SELECT_BILLS_ACCOUNT_TITLE",values:{accounttype:o(e.AccountType,e.IsNM1Account,l)}}),value:e.NickName}});return _().createElement("div",{className:n?"payment-mb-45 payment-block":"sm:payment-mb-15 payment-border-b payment-border-gray-4 payment-hidden"},_().createElement("div",{className:"payment-flex payment-items-center payment-justify-between"},_().createElement(pn.HeadingStep,{disableSrOnlyText:!0,status:n?"complete":"inactive",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:u.length>1?t.formatMessage({id:"SELECT_BILLS_HEADING"}):t.formatMessage({id:"SELECT_BILLS_HEADING_SINGULAR"}),id:i?"payment-setup-heading":void 0,"aria-hidden":i?"true":void 0}),_().createElement("div",{className:"payment-pt-45"},_().createElement(pn.IconLink,{icon:_().createElement(pn.Icon,{iconClass:"bi_brui",iconName:"bi_edit_pencil",className:"brui-text-16"}),text:t.formatMessage({id:"CTA_EDIT"}),variant:"textBlue",size:"regular",href:"javascript:void(0);",position:"right","aria-label":t.formatMessage({id:"ARIA_EDIT_PAYMENT_METHOD"}),role:"button",className:["payment-flex payment-items-center !payment-text-14 !payment-leading-18",n?"":"payment-hidden"].join(" ").trim(),onClick:function(){return r&&r(1)}}))),_().createElement("div",{className:["brui-pb-45",n?"":"brui-hidden"].join(" ").trim()},_().createElement("div",{className:"brui-mt-15"},u.map(function(e,t){return _().createElement(qn,{className:t>0?"brui-mt-5":"",label:e.label,value:e.value})}))))},Mr=(0,dn.injectIntl)(xr),Lr=(0,f.forwardRef)(function(e,t){var n=e.className,r=e.isDisabled,o=e.isChecked,l=e.label,u=e.id,s=e.billType,c=e.billAccountNumber,m=e.idIndex,d=e.text,p=e.priceSettings,b=e.currentItem,E=e.isCheckedBalanceItems,f=e.setIsCheckedBalanceItems,y=e.intl,N=p?y.formatMessage({id:"PAY_MY_BALANCE_SR"},{balance:p.price}):void 0;return _().createElement(pn.CheckboxCard,{ref:t,id:u,"aria-labelledby":l,disabled:r,defaultChecked:o,className:["group-has-[:disabled]/inputcheckbox:payment-bg-gray-3 payment-chekcbox-disabled payment-group/checkboxcard payment-pt-30 payment-pl-30 brui-pr-30 payment-pb-30 sm:payment-p30 payment-basis-unset sm:payment-basis-p48 md:payment-basis-p35 brui-w-full payment-mr-15 brui-mb-15",n].join(" ").trim(),defaultPadding:!1,checkboxPlacement:"topLeft","data-banDetail":JSON.stringify({id:u,billAccountNumber:c,billType:s,price:null==p?void 0:p.price}),onChange:function(e){return t=b,void(e.target.checked?f(i(i([],a(E),!1),[t],!1)):f(function(e){return e.filter(function(e){return e.BillName!==t.BillName})}));var t}},_().createElement("div",{className:"sm:payment-flex sm:payment-pl-[33px] payment-pl-[24px]"},_().createElement("div",{className:"payment-mt-2 sm:payment-mt-1 payment-flex-1 sm:payment-text-left",id:"checkboxBalance-".concat(m,"-label-").concat(m)},_().createElement(pn.Text,{"aria-hidden":"true",elementType:"span",className:"sm:payment-pl-5 payment-text-16 sm:payment-text-18 payment-leading-20 sm:payment-leading-22 brui payment-font-bold payment-flex payment-flex-row payment-items-center payment-gap-5"},d,p&&_().createElement(pn.Price,{language:p.language?p.language:"en",negativeIndicator:p.negativeIndicator?p.negativeIndicator:"CR",price:p.price?p.price:0,variant:"defaultPrice",className:"!payment-text-[16px] !sm:payment-text-[18px] payment-leading-20 sm:payment-leading-22"})),N&&_().createElement("span",{className:"brui-sr-only"},N)),_().createElement("div",{className:"payment-mt-2 sm:payment-mt-1 payment-flex-2 sm:payment-text-right payment-leading-18",id:"checkboxBalance-".concat(m,"-label-").concat(m,"-info")},_().createElement(pn.Text,{elementType:"span",className:"payment-text-gray payment-text-14 sm:payment-text-14"}," ","on",_().createElement(pn.Text,{elementType:"span",className:"payment-font-bold payment-text-black"}," ",s," "),_().createElement("span",{"aria-hidden":"true"},c),_().createElement("span",{className:"payment-sr-only"},null==c?void 0:c.split("").join(" "))))))}),Dr=function(e){var t=e.accountinfo,n=e.className,r=e.role,a=e.childrole,i=e.children,o=e.isLabelOnError;return f.createElement("div",{className:n,role:r},f.createElement(pn.Text,{elementType:"div",className:o?"brui-font-bold brui-text-red brui-text-14 brui-leading-18 brui-mb-5":"brui-font-bold brui-text-black brui-text-14 brui-leading-18 brui-mb-5"},t),f.createElement("div",{role:a},i))},Br=function(e){var t=e.title,n=e.role,r=e.children;return f.createElement(f.Fragment,null,f.createElement("div",{className:"payment-border-b-1 payment-border-b-lightgray payment-pb-15 payment-mb-15"},f.createElement(pn.Heading,{level:"h4",variant:"default",className:"payment-block payment-font-sans payment-text-black payment-text-18 payment-leading-22"},t)),f.createElement("div",{role:n},r))},kr=function(e){var t,n=e.intl,r=e.isActive,a=e.onIconLinkClick,i=e.isCheckedBalanceItems,o=e.checkedBillItems,l=e.paymentItem,u=e.isBankPaymentSelected,s=e.currentSection,c=e.language,m=void 0===c?"en":c,d=u?n.formatMessage({id:"PAY_CURRENT_BALANCE_OPTED_IN"},{balance:new Intl.NumberFormat(m,{style:"currency",currency:"USD",currencyDisplay:"narrowSymbol"}).format(0)}):s===j.Confirmation?"":n.formatMessage({id:"PAY_CURRENT_BALANCE_OPTED_IN_PACC"}),p=n.formatMessage({id:"PAY_CURRENT_BALANCE_OPTED_OUT_SINGULAR"}),b=n.formatMessage({id:"PAY_CURRENT_BALANCE_OPTED_OUT_PLURAL"});return o.length>0&&(Pr=o.filter(function(e){return!i.some(function(t){return t.BanID===e.BanID})})),t=Pr.filter(function(e){return e.Due>0}),_().createElement("div",null,_().createElement("div",{className:r?"payment-block payment-border-gray-4 payment-border-b":"sm:payment-mb-15 payment-border-b payment-border-gray-4 payment-hidden"},_().createElement("div",null,_().createElement("div",{className:"payment-flex payment-items-center payment-justify-between payment-mt-0"},_().createElement(pn.HeadingStep,{disableSrOnlyText:!0,status:"complete",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:n.formatMessage({id:"PAY_CURRENT_BALANCE_HEADING"}),id:"pre-auth-pay_curr_bal"}),_().createElement("div",{className:"payment-pt-45"},_().createElement(pn.IconLink,{icon:_().createElement(pn.Icon,{iconClass:"bi_brui",iconName:"bi_edit_pencil",className:"brui-text-16"}),text:n.formatMessage({id:"CTA_EDIT"}),variant:"textBlue",size:"regular",href:"javascript:void(0);",position:"right",className:"payment-flex payment-items-center !payment-text-14 !payment-leading-18",onClick:function(){return a&&a(1)},"aria-describedby":"pre-auth-pay_curr_bal"}))),_().createElement("div",{className:"payment-pb-45"},_().createElement("div",{className:"payment-text-gray payment-text-14 payment-mt-5"},_().createElement("p",{className:"payment-leading-18",dangerouslySetInnerHTML:{__html:0===i.length?o.length-i.length>1?b:p:i.every(function(e){return o.includes(e)})||i.some(function(e){return o.includes(e)})?d:void 0}})),i.length>0&&_().createElement("div",{className:"payment-pt-15"},l.length>1?_().createElement(_().Fragment,null,i.map(function(e){return _().createElement(Dr,{accountinfo:e.NickName,role:"list",childrole:"listitem",className:"payment-mb-15 last:payment-mb-0"},_().createElement(qn,{label:n.formatMessage({id:"PAYMENT_AMOUNT"}),value:_().createElement(pn.Price,{language:m,price:e.Due,variant:"ordinaryPrice",className:"!brui-text-14 brui-leading-18"}),needSRText:!0,srText:"".concat(e.Due," dollars"),className:"payment-text-black",isMultiBan:!0}))}),Pr.length>0&&o.length!==Pr.length&&_().createElement(_().Fragment,null,_().createElement("div",{className:"payment-text-gray payment-text-14 payment-mt-5"},_().createElement("p",{className:"payment-leading-18"},Pr.some(function(e){return e.Due>0})&&_().createElement(_().Fragment,null,t.length>1&&n.formatMessage({id:"PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_PLURAL"}),1===t.length&&n.formatMessage({id:"PAY_CURRENT_BALANCE_SOME_BANS_OPTED_OUT_SINGULAR"})))))):_().createElement(_().Fragment,null,i.map(function(e){return _().createElement(qn,{label:n.formatMessage({id:"PAYMENT_AMOUNT"}),value:_().createElement(pn.Price,{language:m,price:e.Due,variant:"ordinaryPrice",className:"!brui-text-14 brui-leading-18"}),needSRText:!0,srText:"".concat(e.Due," dollars"),className:"payment-text-black"})})))))))},wr=(0,dn.injectIntl)(kr),Ur=function(e){var t,n,r,i,l,s=e.intl,c=e.paymentItem,d=e.isShow,p=(e.onCurrentSteps,e.setCurrentSection),b=e.currentSection,E=e.setCheckedBillItems,y=e.paymentItems,N=e.createMultiPaymentData,g=e.accountInputValues,A=e.setAccountValues,C=e.transactionIds,v=e.createOmnitureOnLoad,T=a((0,f.useState)(!1),2),h=T[0],I=T[1],R=a((0,f.useState)(),2),S=R[0],O=R[1],x=a((0,f.useState)([]),2),M=x[0],L=x[1],D=a((0,f.useState)([]),2),B=D[0],P=D[1],k=a((0,f.useState)(!1),2),U=k[0],F=k[1],H=a((0,f.useState)(!1),2),Y=H[0],G=H[1],V=(0,f.useRef)([]),z=function(){var e,t,n;null!==M&&M.length>0&&N(M[0].Ban,M[0].AccountType===w.OneBill,g,M[0].subscriberId),!1===(n=!1,B&&B.length>0&&(t=(e=V.current.filter(function(e){return null==e?void 0:e.checked})).map(function(e){return null==e?void 0:e.getAttribute("data-banDetail")}).filter(function(e){return null!=e}),O(t),n=!(e.length<=0)),n)?I(!0):(I(!1),p(j.PaymentMethod))};return _().useEffect(function(){var e,t=sessionStorage.getItem("itemsChecked"),n=t&&JSON.parse(t);null!==n&&n.length>0?(F(!0),y.map(function(e){n.map(function(t){e.Ban===t&&(e.IsChecked=!0)})}),P(y),e=r(y),N(y[0].Ban,y[0].AccountType===w.OneBill,e,y[0].subscriberId)):P(y)},[]),_().useEffect(function(){U&&(null==B?void 0:B.length)>0&&(z(),sessionStorage.removeItem("itemsChecked"),F(!1))},[U]),t=function(e){return e.IsOnPreauthorizedPayments&&e.CreditCardDetails?_().createElement(_().Fragment,null,_().createElement(dn.FormattedMessage,{id:"SELECT_BILLS_CC_DESC",values:{CreditCardType:u(e.CreditCardDetails.CreditCardType),CCFourDigits:e.CreditCardDetails.CreditCardNumber.slice(-4),ExpiryDate:e.CreditCardDetails.ExpireMonth+"/"+e.CreditCardDetails.ExpireYear}})):e.IsOnPreauthorizedPayments&&e.BankAccountDetails?_().createElement(_().Fragment,null,_().createElement(dn.FormattedMessage,{id:"SELECT_BILLS_BANK_DESC",values:{BankName:e.BankAccountDetails.BankName,Code:e.BankAccountDetails.TransitCode,BankMaskedDigits:e.BankAccountDetails.AccountNumberMaskedDisplayView}})):_().createElement(_().Fragment,null,s.formatMessage({id:"ACCOUNT_BALANCE"}))},n=(null==c?void 0:c.filter(function(e){return e.IsOnPreauthorizedPayments}).length)===c.length,r=function(e){return e.map(function(e){return{accountNumber:e.Ban,subNumber:e.subscriberId,transactionID:m(e.Ban,C),payBalanceAmnt:0}})},_().useEffect(function(){if(c.length>1){E(M);var e=r(M);A(e),0===M.length&&(E([]),A([])),c.length===M.length?G(!0):G(!1)}},[M]),i={MyBill:s.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:s.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:s.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:s.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:s.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:s.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:s.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:s.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})},l=function(e){return!(!e.IsNM1Account&&e.AccountType===w.OneBill&&!e.IsOnPreauthorizedPayments||!(0!==e.Due&&void 0!==e.Due||e.IsOnPreauthorizedPayments))},_().useEffect(function(){var e=sessionStorage.getItem("itemsChecked"),t=e&&JSON.parse(e);b===j.SelectBills&&(null!==t&&t.length>0||v())},[b]),_().createElement("div",{className:["payment-border-b payment-border-gray-4",d?"":"payment-hidden"].join(" ").trim()},_().createElement("div",{className:["payment-flex payment-flex-col",b===j.SelectBills?"":"payment-hidden"].join(" ").trim()},_().createElement(pn.HeadingStep,{disableSrOnlyText:!0,status:"active",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:c.filter(function(e){return!(null==e?void 0:e.IsOnPreauthorizedPayments)}).length>1?s.formatMessage({id:"SELECT_BILLS_HEADING"}):s.formatMessage({id:"SELECT_BILLS_HEADING_SINGULAR"}),id:b===j.SelectBills?"payment-setup-heading":void 0,"aria-hidden":b===j.SelectBills||void 0}),_().createElement("p",{className:"payment-text-gray payment-text-14 payment-mt-5","aria-hidden":"true",id:"payment-label"},s.formatMessage({id:"SELECT_BILLS_HEADING_DESC"}))),h&&_().createElement(_().Fragment,null,_().createElement("div",{className:"payment-mt-30"}),_().createElement(tr,null,_().createElement(Wn,null,_().createElement($n,{label:s.formatMessage({id:"ALERT_ERROR_SELECT_BILL_INFO"}),labelDescription:s.formatMessage({id:"ALERT_ERROR_SELECT_BILL_DESC"}),variant:"errorList",id:"error-alert-2"})))),_().createElement("div",{className:["payment-pt-0 checkbox-main",b===j.SelectBills?"":"payment-hidden"].join(" ").trim()},_().createElement("div",{className:"payment-mt-30"},_().createElement(pn.Checkbox,{id:"chxbx1",name:"checkboxname",value:"select all",variant:"default",checked:Y,onChange:function(e){e.target.checked?(G(!0),B&&B.length>0&&(L(B),V.current.forEach(function(e){e&&(e.checked=!0)}))):(G(!1),B.map(function(e,t){L(function(t){return t.filter(function(t){return t.BillName!==e.BillName})})}),B&&B.length>0&&V.current.forEach(function(e){e&&(e.checked=!1)}))}},_().createElement("label",null,s.formatMessage({id:"SELECT_ALL_BAN"})))),_().createElement("div",{className:"payment-mt-30 payment-flex payment-flex-col sm:payment-flex-row payment-flex-wrap payment-mb-15",role:"group","aria-labelledby":"chekcboxgroup-label"},B&&B.length>0&&B.sort(function(e,t){return e.NickName>t.NickName?1:-1}).map(function(e,n){var r;return _().createElement(Or,{className:["checkboxitem payment-group/checkboxcard  payment-basis-0 sm:payment-basis-[47%] md:payment-basis-[35%] lg:payment-basis-1/3 payment-w-full sm:payment-w-auto payment-mr-15 payment-mb-15",e.IsOnPreauthorizedPayments?"payment-bg-gray-3":""].join(" ").trim(),id:"checkboxbill-".concat(n),idIndex:n,label:"checkboxBill".concat(n,"-label-").concat(n," checkboxBillBalance-").concat(n,"-label-").concat(n),isChecked:e.IsChecked,isDisabled:e.IsOnPreauthorizedPayments,billType:o(e.AccountType,e.IsNM1Account,i),billAccountNumber:null!==(r=e.NickName)&&void 0!==r?r:e.BillName,text:t(e),priceSettings:e.IsOnPreauthorizedPayments?void 0:{price:e.Due},ref:function(e){V.current[n]=e},item:e,isCheckedItems:M,setIsCheckedItems:L,isShowLabel:l(e),paymentItems:B,intl:s})})),h&&_().createElement(pn.Text,{elementType:"div",className:"payment-pb-15 payment-flex payment-items-center",id:"error-alert-3"},_().createElement(pn.Icon,{className:"payment-text-15 payment-text-red payment-mr-10",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),_().createElement(pn.Text,{elementType:"div",className:"payment-text-red payment-text-12"},s.formatMessage({id:"ALERT_ERROR_ONE_SELECT_BILL"}))),_().createElement("div",{className:"payment-pt-15 payment-pb-45 sm:payment-pb-60"},_().createElement(pn.Button,{variant:"primary",onClick:z,disabled:n},s.formatMessage({id:"CTA_NEXT"})))),_().createElement(Mr,{isActive:b>j.SelectBills,onIconLinkClick:function(e){p(j.SelectBills)},banDetails:S||[],isCheckedItems:M,isShow:d}))},Fr=function(e){return{createPayment:e.createPayment}},Hr=function(e){return{createMultiPaymentData:function(t,n,r,a){return e(Ee({ban:t,type:n,details:r,sub:a}))},createOmnitureOnLoad:function(){return e(Fe({payload:"Banselected"}))}}},Yr=(0,y.connect)(Fr,Hr)((0,dn.injectIntl)(Ur)),jr=function(e){var t,n=e.intl,r=e.paymentItem,l=e.checkedBillItems,u=e.setCheckedCurrentBalanceItems,s=e.setCurrentSection,c=e.currentSection,m=e.language,d=e.accountInputValues,p=e.setAccountValues,b=(e.transactionIds,e.isBankPaymentSelected),E=e.setNotOptedBalanceItems,y=(e.checkedCurrentBalanceItems,e.createOmnitureOnCurrentBalance),N=a((0,f.useState)([]),2),g=N[0],A=N[1],C={MyBill:n.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:n.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:n.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:n.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:n.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:n.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:n.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:n.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})};return _().useEffect(function(){var e,t=i([],a(d),!1);g.length>0?(u(g),e=t.map(function(e){var t=g.find(function(t){return t.Ban===e.accountNumber&&t.subscriberId===e.subNumber});return e.payBalanceAmnt=t?t.Due:0,e}),p(e),E(l.filter(function(e){return e.Due>0&&e.AccountType!==w.OneBill&&!g.includes(e)}))):(u([]),p(t),E(l.filter(function(e){return e.Due>0&&e.AccountType!==w.OneBill})))},[g]),_().useEffect(function(){c===j.CurrentBalance&&y()},[c]),t=n.formatMessage({id:"PAY_CURRENT_BALANCE_DESC"}),_().createElement(_().Fragment,null,_().createElement("div",{className:[(j.CurrentBalance,"payment-border-b payment-border-gray-4"),c>j.CurrentBalance?"payment-hidden":""].join(" ").trim()},_().createElement("div",null,_().createElement(pn.HeadingStep,{disableSrOnlyText:!0,status:c===j.CurrentBalance?"active":"inactive",subtitle:"",hideSubtitle:!0,variant:"leftAlignNoStep",title:(l.filter(function(e){return!(null==e?void 0:e.IsOnPreauthorizedPayments)}).length,n.formatMessage({id:"PAY_CURRENT_BALANCE_HEADING"})),id:c===j.CurrentBalance?"payment-setup-heading":"pre-auth-pay_curr_bal","aria-hidden":c===j.CurrentBalance?"true":void 0}),c===j.CurrentBalance&&_().createElement("div",{className:"payment-flex sm:payment-items-center"},_().createElement(pn.Icon,{className:"brui-text-blue payment-mt-10",iconClass:"bi_brui",iconName:"bi_info_notif_small"}),_().createElement("p",{className:"payment-text-gray payment-text-14 payment-leading-18 payment-mt-10 payment-ml-10",dangerouslySetInnerHTML:{__html:t}}))),_().createElement("div",{className:[c===j.CurrentBalance?"":"payment-hidden"].join(" ").trim()},_().createElement("div",{className:"payment-mt-30 payment-flex payment-flex-col sm:payment-flex-row payment-flex-wrap",role:"group","aria-labelledby":"chekcboxgroup-label"},l.map(function(e,t){var r;return e.Due>0&&e.AccountType!==w.OneBill?_().createElement(Lr,{className:"sm:payment-px-30 payment-mb-15",id:"checkboxBill-".concat(t),idIndex:t,label:"checkboxBalance-".concat(t,"-label-").concat(t," checkboxBalance-").concat(t,"-label-").concat(t,"-info"),isDisabled:e.IsOnPreauthorizedPayments,billType:o(e.AccountType,e.IsNM1Account,C),billAccountNumber:null!==(r=e.NickName)&&void 0!==r?r:e.BillName,text:n.formatMessage({id:"PAY_MY_BALANCE"}),priceSettings:e.IsOnPreauthorizedPayments?void 0:{price:e.Due},currentItem:e,isCheckedBalanceItems:g,setIsCheckedBalanceItems:A,intl:n}):null})),_().createElement("div",{className:"payment-text-gray payment-text-12 payment-mt-20"},_().createElement("p",null,n.formatMessage({id:"PAY_CURRENT_BALANCE_NOTE_1"})),_().createElement("p",null,n.formatMessage({id:"PAY_CURRENT_BALANCE_NOTE_2"}))),_().createElement("div",{className:"payment-pt-15 payment-pb-45 sm:payment-pb-60"},_().createElement(pn.Button,{variant:"primary",onClick:function(){s(j.TermsAndCondition)}},n.formatMessage({id:"CTA_NEXT"}))))),_().createElement(wr,{isActive:c>j.CurrentBalance,onIconLinkClick:function(e){s(j.CurrentBalance)},paymentItem:r,isCheckedBalanceItems:g,checkedBillItems:l,isBankPaymentSelected:b,currentSection:c,language:m}))},Gr=function(e){return{createOmnitureOnCurrentBalance:function(t){return e(Ye({data:t}))}}},Vr=(0,y.connect)(null,Gr)((0,dn.injectIntl)(jr)),zr=(0,dn.injectIntl)(function(e){var t,n,r=e.intl,a=e.multiban,i=e.submitMultiOrderPayment,o=e.accountInputValue,l=e.paymentItem,u=e.language,s=0;return a||(t=o.filter(function(e){var t;return e.transactionID===(null===(t=i[0])||void 0===t?void 0:t.OrderFormId)}).map(function(e){return e.accountNumber})[0],t&&(n=l.filter(function(e){return e.Ban===t}).map(function(e){return e.Due})[0])&&(s=n)),f.createElement(pn.Alert,{variant:"warning",className:"payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-20 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block",iconSize:"36"},f.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0"},a&&f.createElement(f.Fragment,null,f.createElement(pn.Heading,{level:"h2",variant:"xs",className:"payment-mb-15 payment-font-sans payment-font-bold"},r.formatMessage({id:"ALERT_CONFIRMATION_OPTED_SOME_BALANCE_MULTI"})),f.createElement("p",{className:"payment-text-14 payment-mb-10 payment-text-gray"},r.formatMessage({id:"ALERT_CONFIRMATION_OPTED_SOME_BALANCES_DESC_MULTI"})),f.createElement(pn.Text,{elementType:"div",className:"payment-mb-30 payment-mt-15"},i.map(function(e,t){var n=o.filter(function(t){return t.transactionID===e.OrderFormId&&0===t.payBalanceAmnt}).map(function(e){return e.accountNumber})[0],r=l.filter(function(e){return e.Ban===n}).map(function(e){return e.Due})[0];if(0!==r)return f.createElement(f.Fragment,null,f.createElement(pn.Text,{elementType:"div",className:"payment-flex payment-justify-between sm:payment-justify-normal"},f.createElement("label",{className:"payment-text-14 sm:payment-basis-1/4"},f.createElement("strong",null,n)),f.createElement(pn.Price,{language:u,showZeroDecimalPart:!0,price:r,variant:"defaultPrice",className:"!payment-text-14 payment-leading-14 payment-m-5 payment-font-normal"})))}))),!a&&f.createElement(f.Fragment,null,f.createElement(pn.Heading,{level:"h2",variant:"xs",className:"payment-mb-15 payment-font-sans payment-font-bold"},r.formatMessage({id:"ALERT_CONFIRMATION_OPTED_SOME_BALANCE_SINGLE"})),f.createElement("p",{className:"payment-text-14 payment-mb-10 payment-text-gray"},r.formatMessage({id:"ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_1"}),f.createElement(pn.Price,{language:u,price:Number(s),variant:"defaultPrice",className:"!payment-text-14 payment-leading-14 brui-m-5 payment-font-normal brui-inline-block"}),r.formatMessage({id:"ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_SINGLE_2"}))),f.createElement(pn.Button,{variant:"primary",size:"regular",onClick:function(){location.href="".concat(r.formatMessage({id:"CTA_MAKE_PAYMENT_LINK"}))}},r.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))}),qr=function(e){var t=e.intl;return f.createElement(pn.Alert,{variant:"info",className:"payment-border payment-relative sm:payment-py-30 sm:payment-px-30 payment-rounded-20 payment-p-16 payment-px-15 payment-py-30 sm:payment-flex payment-block",iconSize:"36",id:"alert-3"},f.createElement(pn.Text,{elementType:"div",className:"payment-pl-0 payment-pt-15 sm:payment-pl-16 sm:payment-pt-0"},f.createElement(pn.Heading,{level:"h2",variant:"xs",className:"payment-mb-12 payment-font-sans payment-font-bold payment-leading-22"},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_HEADING"})),f.createElement("p",{className:"payment-text-14 payment-my-15 payment-text-gray"},t.formatMessage({id:"ALERT_CONFIRMATION_OPTED_SOME_BALANCE_DESC_MULTI"})),f.createElement(pn.Text,{elementType:"div",className:"sm:payment-flex payment-block"},f.createElement(pn.Text,{elementType:"div",className:"payment-pr-0 sm:payment-pr-10"},f.createElement(pn.Button,{variant:"primary",size:"regular",onClick:function(){location.href="".concat(t.formatMessage({id:"CTA_MAKE_PAYMENT_LINK"}))}},t.formatMessage({id:"ALERT_CONFIRMATION_INFO_BUTTON_DESC"}))))))},Kr=(0,dn.injectIntl)(qr),Xr=function(e,t,n,r,a,i,o){if(e){if(!t)return{s_oPYM:"Bank payment",s_OPID:i[0].PaymentConfirmationNumber};if(n.length>0)return{s_oPYM:"Bank payment:optin",s_OPID:i[0].PaymentConfirmationNumber};if(0===n.length)return{s_oPYM:"Bank payment:optout",s_OPID:i[0].PaymentConfirmationNumber}}else if(!e){if(!t)return{s_oPYM:"Credit card",s_oCCDT:{CreditCardType:o},s_OPID:i[0].PaymentConfirmationNumber};if(n.length>0)return{s_oPYM:"Credit card:optin",s_oCCDT:{CreditCardType:o},s_OPID:i[0].PaymentConfirmationNumber};if(0===n.length)return{s_oPYM:"Credit card:optout",s_oCCDT:{CreditCardType:o},s_OPID:i[0].PaymentConfirmationNumber}}return{s_oPYM:"",s_oCCDT:""}},Wr=function(e,t,n,r){if(e){if(!t)return{s_oPYM:"Bank payment",s_OPID:""};if(n.length>0)return{s_oPYM:"Bank payment:optin",s_OPID:""};if(0===n.length)return{s_oPYM:"Bank payment:optout",s_OPID:""}}else if(!e){if(!t)return{s_oPYM:"Credit card",s_oCCDT:{CreditCardType:r},s_OPID:""};if(n.length>0)return{s_oPYM:"Credit card:optin",s_oCCDT:{CreditCardType:r},s_OPID:""};if(0===n.length)return{s_oPYM:"Credit card:optout",s_oCCDT:{CreditCardType:r},s_OPID:""}}return{s_oPYM:"",s_oCCDT:"",s_OPID:""}},Qr=function(e){var t,n,r,i,l,u,s,c,m,d,p,b=e.intl,E=e.paymentItem,_=e.checkedBillItems,y=e.checkedCurrentBalanceItems,N=e.showPaymentSummary,g=e.isPreauth,A=e.isNewbank,C=e.inputValue,v=e.inputBankValue,T=e.isShow,h=e.isBankPaymentSelected,I=e.submitMultiOrderPayment,R=e.submitMultiOrderFormStatus,S=e.accountInputValues,O=e.BankList,x=e.showCurrentBalance,M=e.currentSection,L=e.language,D=e.notOptedBalanceItems,B=e.setApiSatusIsFailed,P=e.creditCardAutopayOffers,k=e.debitCardAutopayOffers,U=e.bankitems,F=e.setOmnitureOnConfirmation,H=e.apiSatusIsFailed,Y=e.backCTAURL,G=e.setOmnitureOnConfirmationFailure,V=e.setOmnitureOnOneTimePaymentFailure,z={MyBill:b.formatMessage({id:"ACCOUNT_TYPENAME_MYBILL"}),Mobility:b.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY"}),OneBill:b.formatMessage({id:"ACCOUNT_TYPENAME_ONEBILL"}),TV:b.formatMessage({id:"ACCOUNT_TYPENAME_TV"}),Internet:b.formatMessage({id:"ACCOUNT_TYPENAME_INTERNET"}),HomePhone:b.formatMessage({id:"ACCOUNT_TYPENAME_HOMEPHONE"}),MobilityAndOneBill:b.formatMessage({id:"ACCOUNT_TYPENAME_MOBILITY_ONEBILL"}),SingleBan:b.formatMessage({id:"ACCOUNT_TYPENAME_SINGLEBAN"})},q={label:b.formatMessage({id:"PAYMENT_METHOD"}),debits:h?E&&E.length>1?(p=[],k&&(null==k||k.map(function(e){_&&_.map(function(t){e.Ban===t.Ban&&p.push(e)})})),p):k:null,credits:h?null:E&&E.length>1?function(){var e=[];return P&&(null==P||P.map(function(t){_&&_.map(function(n){t.Ban===n.Ban&&e.push(t)})})),e}():P},K={label:b.formatMessage({id:"AUTOPAY_TRANSACTION_NOTE_DESC"})},X={label:b.formatMessage({id:"AUTOPAY_TRANSACTION_NOTE_HEADING"})},W=q&&q.credits&&q.credits.length>0&&q.credits[0].AutopayEligibleSubscribers&&q.credits[0].AutopayEligibleSubscribers.length>0&&q.credits[0].AutopayEligibleSubscribers[0].autopayOffers&&q.credits[0].AutopayEligibleSubscribers[0].autopayOffers.length>0||q&&q.debits&&q.debits.length>0&&q.debits[0].AutopayEligibleSubscribers&&q.debits[0].AutopayEligibleSubscribers.length>0&&q.debits[0].AutopayEligibleSubscribers[0].autopayOffers&&q.debits[0].AutopayEligibleSubscribers[0].autopayOffers.length>0,Q=_.map(function(e){return{label:f.createElement(dn.FormattedMessage,{id:"SELECT_BILLS_ACCOUNT_TITLE",values:{accounttype:o(e.AccountType,e.IsNM1Account,z)}}),value:e.NickName,key:o(e.AccountType,e.IsNM1Account,z)}}),Z=_.filter(function(e){return!y.includes(e)&&e.Due>0}),$=f.useRef(null),J=h?b.formatMessage({id:"PAY_CURRENT_BALANCE_OPTED_IN"},{balance:new Intl.NumberFormat(L,{style:"currency",currency:"USD",currencyDisplay:"narrowSymbol"}).format(0)}):M===j.Confirmation?"":b.formatMessage({id:"PAY_CURRENT_BALANCE_OPTED_IN_PACC"}),ee=b.formatMessage({id:"PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_SINGULAR"}),te=b.formatMessage({id:"PAY_CURRENT_BALANCE_CONFIRMATION_OPTED_OUT_PLURAL"}),ne=a(f.useState(!1),2),re=ne[0],ae=ne[1],ie=function(){M===j.Confirmation&&G(h?Wr(!0,x,y):Wr(!1,x,y,C.cardType))};return f.useEffect(function(){var e,t,n,r,a;if(R===Ze.FAILED)B(!0),ie();else if(1===E.length&&R===Ze.COMPLETED&&Object.values(I).length>0)try{for(r=(n=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(Object.values(I))).next();!r.done;r=n.next())if(""!==r.value.ErrorCodeID.trim()){B(!0),ie();break}}catch(i){e={error:i}}finally{try{r&&!r.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}R===Ze.COMPLETED&&Object.values(I).length>0&&T&&!H&&F(h?Xr(h,x,y,0,0,Object.values(I)):Xr(h,x,y,0,0,Object.values(I),C.cardType)),R===Ze.COMPLETED&&$.current&&((a=$.current.querySelector(".payment-focus-sr"))&&(a.scrollIntoView({behavior:"smooth"}),a.focus()),setTimeout(function(){var e,t=null===(e=null==$?void 0:$.current)||void 0===e?void 0:e.querySelector(".payment-focus-heading");t&&(t.scrollIntoView({behavior:"smooth"}),t.focus())},100),setTimeout(function(){var e,t=null===(e=null==$?void 0:$.current)||void 0===e?void 0:e.querySelector(".payment-focus-sr");t&&(t.style.display="none")},1e3),setTimeout(function(){ae(!0)},200))},[R]),r=function(){return Object.values(I).some(function(e){return(null==e?void 0:e.otp)&&!(null==e?void 0:e.otp.isSuccess)})?b.formatMessage({id:"TRANSACTION_SUBMITTED_HEADING"}):b.formatMessage({id:"CONFIRMATION_HEADING"})},i=function(){return Object.values(I).some(function(e){return(null==e?void 0:e.otp)&&!(null==e?void 0:e.otp.isSuccess)})?b.formatMessage({id:"UNPAID_BALANCE"}):b.formatMessage({id:"PAYMENT_AMOUNT"})},l=a(f.useState(!1),2),u=l[0],s=l[1],c=a(f.useState([]),2),m=c[0],d=c[1],f.useEffect(function(){var e,t,n,r;0===Object.values(m).length&&(e=Object.values(I).filter(function(e){return(null==e?void 0:e.otp)&&!(null==e?void 0:e.otp.isSuccess)}),t=e.map(function(e){return null==e?void 0:e.OrderFormId}),n=S.filter(function(e){return t.includes(e.transactionID)}).map(function(e){return e.accountNumber}),r=y.filter(function(e){return n.includes(e.Ban)}),y.length>r.length&&1!==y.length?s(!0):s(!1),d(r))},[m]),f.createElement(f.Fragment,null,R===Ze.PENDING&&f.createElement(Tr,{variant:"submitOrder"}),Object.values(I).length>0&&R===Ze.COMPLETED&&f.createElement("div",{className:"brui-border-gray-4",ref:$},f.createElement("div",{className:"payment-mb-15 brui-flex brui-items-center brui-justify-between payment-mt-30 sm:payment-mt-45",id:"ConfirmationDivID"},f.createElement("span",{className:"payment-focus-sr payment-sr-only",id:"pageConfirmationId",tabIndex:-1},b.formatMessage({id:"PAGE_TITLE_CONFIRMATON"})),f.createElement(pn.Heading,{className:"payment-focus-heading",level:"h2",variant:"lg",tabIndex:-1,id:T?"payment-setup-heading":void 0,"aria-hidden":T?"true":void 0},r())),!h&&Object.values(I).length>0&&Object.values(I).some(function(e){return(null==e?void 0:e.otp)&&!(null==e?void 0:e.otp.isSuccess)})&&re&&f.createElement("div",{id:"OtpFailure",className:"payment-pt-15 payment-pb-0"},f.createElement(mr,{checkedCurrentBalanceItems:y,submitMultiOrderPayment:Object.values(I),accountInputValue:S,language:L,notOptedBalanceItems:D,setOmnitureOnOneTimePaymentFailure:V})),f.createElement("div",{id:"ConfirmationSuccess",className:"payment-pt-15 payment-pb-15"},f.createElement(rr,{submitMultiOrderPayment:Object.values(I),accountInputValue:S,isBankPayment:h,checkedBillItems:_,language:L,paymentItem:E,creditCardAutopayOffers:P,debitCardAutopayOffers:k})),E.find(function(e){return e.AccountType!==w.OneBill})&&Z.length>0&&!1===Object.values(I).some(function(e){return(null==e?void 0:e.otp)&&!(null==e?void 0:e.otp.isSuccess)})&&f.createElement("div",{id:"AvoidLatePayment",className:"payment-pb-15"},f.createElement(zr,{multiban:E.length>1,submitMultiOrderPayment:Object.values(I),accountInputValue:S,paymentItem:E,language:L})),E.find(function(e){return e.AccountType===w.OneBill})&&f.createElement("div",{id:"AvoidLatePayment",className:"payment-pb-15"},f.createElement(Kr,null)),f.createElement("div",{className:"payment-block payment-border payment-rounded-20 payment-relative payment-px-15 sm:payment-px-30 payment-py-30 sm:payment-pb-45"},f.createElement("div",null,f.createElement(pn.Heading,{level:"h3",variant:"md"},b.formatMessage({id:"PAYMENT_SUMMARY_TITLE"}))),f.createElement("div",{className:"payment-mt-30"},f.createElement(Br,{title:b.formatMessage({id:"BILL_INFORMATION_TITLE"})},Q.map(function(e,t){return f.createElement(qn,{className:t>0?"payment-mt-5":"",label:e.label,value:e.value})}))),f.createElement("div",{className:"payment-mt-45"},f.createElement(Br,{title:b.formatMessage({id:"PAYMENT_INFORMATION_TITLE"})},f.createElement(pr,{paymentItem:E,className:N?"":"payment-hidden",inputValue:C,isNewbank:A,isPreauth:g,inputBankValue:v,showHeading:!1,isBankPaymentSelected:h,bankList:O,creditCardAutopayOffers:P,debitCardAutopayOffers:k,checkedBillItems:_,bankitems:U,isConfirmation:!0}))),W?h?f.createElement("div",{className:"payment-mt-45"},f.createElement(Br,{title:b.formatMessage({id:"AUTOPAY_CREDITS_TITLE"}),role:"list"},null===(t=null==q?void 0:q.debits)||void 0===t?void 0:t.map(function(e,t){var n;return f.createElement(f.Fragment,{key:e.Ban||t},_&&_.length>1&&f.createElement("p",{className:"payment-text-14 payment-font-bold"},e.banInfo&&e.banInfo.nickName),null===(n=e.AutopayEligibleSubscribers)||void 0===n?void 0:n.map(function(e,t){var n;return null===(n=null==e?void 0:e.autopayOffers)||void 0===n?void 0:n.map(function(t,n){return f.createElement(qn,{key:"".concat(e.subscriberTelephoneNumber,"-").concat(n),label:e.subscriberTelephoneNumber,value:"en"===L?"$"+t.discountAmount.toFixed(2)+"/mo.":t.discountAmount.toFixed(2)+" $/mois",needSRText:!0,srText:"en"===L?t.discountAmount.toFixed(2)+" dollars per month":t.discountAmount.toFixed(2)+" dollars par mois",role:"listitem",isMultiBan:_.length>1,className:_.length>1?"payment-text-gray":"payment-text-black"})})}))}),f.createElement("p",{className:"payment-text-gray payment-text-14"},f.createElement("strong",null,X.label),K.label))):f.createElement("div",{className:"payment-mt-45"},f.createElement(Br,{title:b.formatMessage({id:"AUTOPAY_CREDITS_TITLE"}),role:"list"},null===(n=null==q?void 0:q.credits)||void 0===n?void 0:n.map(function(e,t){var n;return f.createElement(f.Fragment,{key:e.Ban||t},_&&_.length>1&&f.createElement("p",{className:"payment-text-14 payment-font-bold"},e.banInfo&&e.banInfo.nickName),null===(n=e.AutopayEligibleSubscribers)||void 0===n?void 0:n.map(function(e,t){var n;return null===(n=null==e?void 0:e.autopayOffers)||void 0===n?void 0:n.map(function(t,n){return f.createElement(qn,{key:"".concat(e.subscriberTelephoneNumber,"-").concat(n),label:e.subscriberTelephoneNumber,value:"en"===L?"$"+t.discountAmount.toFixed(2)+"/mo.":t.discountAmount.toFixed(2)+" $/mois",needSRText:!0,srText:"en"===L?t.discountAmount.toFixed(2)+" dollars per month":t.discountAmount.toFixed(2)+" dollars par mois",role:"listitem",isMultiBan:_.length>1,className:_.length>1?"payment-text-gray":"payment-text-black"})})}))}),f.createElement("p",{className:"payment-text-gray payment-text-14"},f.createElement("strong",null,X.label),K.label))):"",x&&f.createElement("div",{className:"payment-mt-45"},f.createElement(Br,{title:b.formatMessage({id:"CURRENT_BALANCE_TITLE"})},!h&&Object.values(I).length>0&&Object.values(I).some(function(e){return(null==e?void 0:e.otp)&&!(null==e?void 0:e.otp.isSuccess)})&&!u&&f.createElement("div",{className:"payment-flex payment-items-start payment-mb-15"},f.createElement(pn.Icon,{className:"brui-text-15 brui-text-red brui-mr-10 payment-mt-2",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),f.createElement("p",{className:"brui-text-red brui-text-14"},b.formatMessage({id:"SUMMARY_CURRENT_BAL_OTP_FAILURE"}))),f.createElement("div",{className:"payment-text-gray payment-text-14 payment-mt-5"},f.createElement("p",{className:"payment-leading-18",dangerouslySetInnerHTML:{__html:0===y.length?_.length-y.length>1?te:ee:y.every(function(e){return _.includes(e)})||y.some(function(e){return _.includes(e)})?J:void 0}})),f.createElement("div",null,E.length>1?f.createElement(f.Fragment,null,y.map(function(e){return f.createElement(Dr,{accountinfo:e.NickName,role:"list",childrole:"listitem",className:"first:payment-mt-15 payment-mb-15 last:payment-mb-0",isLabelOnError:!!(u&&m.filter(function(t){return t.Ban===e.Ban}).length>0)},f.createElement(f.Fragment,null,u&&m.filter(function(t){return t.Ban===e.Ban}).length>0&&f.createElement("div",{className:"payment-flex payment-items-start payment-mb-5"},f.createElement(pn.Icon,{className:"brui-text-15 brui-text-red brui-mr-10 payment-mt-2",iconClass:"bi_brui",iconName:"bi_error_bl_bg_cf"}),f.createElement("p",{className:"brui-text-red brui-text-14"},b.formatMessage({id:"SUMMARY_CURRENT_BAL_OTP_FAILURE"}))),f.createElement(qn,{label:m.filter(function(t){return t.Ban===e.Ban}).length>0?b.formatMessage({id:"UNPAID_BALANCE"}):b.formatMessage({id:"PAYMENT_AMOUNT"}),value:f.createElement(pn.Price,{language:L,price:e.Due,variant:"ordinaryPrice",className:"!brui-text-14 brui-leading-18"}),needSRText:!0,srText:"".concat(e.Due," dollars"),className:"payment-text-black",isMultiBan:!0})))})):f.createElement(f.Fragment,null,y.map(function(e){return f.createElement(qn,{label:i(),value:f.createElement(pn.Price,{language:L,price:e.Due,variant:"ordinaryPrice",className:"!brui-text-14 brui-leading-18"}),needSRText:!0,srText:"".concat(e.Due," dollars"),className:"payment-text-black first:payment-mt-15 payment-mb-15 last:payment-mb-0"})})))))),f.createElement("div",{className:"payment-mt-30 payment-mb-45 sm:payment-mb-60"},f.createElement(pn.Button,{size:"regular",variant:"secondary",onClick:function(){window.location.href=Y+"#billinginformation"}},b.formatMessage({id:"BACK_TO_MY_BELL"})))))},Zr=function(e){return{submitMultiOrderPayment:e.submitMultiOrderPayment,submitMultiOrderFormStatus:e.submitMultiOrderFormStatus}},$r=function(e){return{setOmnitureOnConfirmation:function(t){return e(Ge({data:t}))},setOmnitureOnConfirmationFailure:function(t){return e(Ke({data:t}))},setOmnitureOnOneTimePaymentFailure:function(t){return e(Xe({data:t}))}}},Jr=(0,y.connect)(Zr,$r)((0,dn.injectIntl)(Qr)),ea={error:"payment-text-red payment-text-[60px] payment-box-border payment-static payment-text-center",warning:"payment-text-yellow payment-text-[60px] payment-box-border payment-static payment-text-center"},ta=function(e){var t=e.iconVariant,n=e.errorHeaderText,r=e.errorHeaderTextSR,a=e.errorHeaderStyle,i=e.errorTextStyle,o=e.errorText,l=e.buttonText,u=e.onButtonClick,s=e.suggestionList;return _().createElement("div",{className:"payment-text-center payment-mt-60 sm:payment-max-w-[543px] md:payment-max-w-[548px] payment-mx-auto"},_().createElement("span",{"aria-label":"Warning"},_().createElement("span",{role:"img","aria-hidden":"true"},_().createElement(pn.Icon,{iconClass:"bi_brui",iconName:"bi_exclamation_c",className:ea[t]}))),_().createElement(pn.Heading,{level:"h2",variant:"default",className:["payment-text-32 payment-leading-38 payment-text-black payment-mb-5 payment-mt-30",a].join(" ").trim()},_().createElement("span",{"aria-hidden":"true"},n),_().createElement("span",{"aria-hidden":"false",className:"payment-sr-only"},r)),_().createElement("p",{className:["payment-text-gray payment-overflow-x-hidden payment-font-sans payment-text-[16px] payment-leading-[25px]",i].join(" ").trim()},o),s&&_().createElement(_().Fragment,null,_().createElement(pn.Divider,{direction:"horizontal",width:1,className:"payment-my-45 payment-bg-gray-4"}),_().createElement("div",{className:"payment-mb-45"},_().createElement(pn.Text,{className:"brui-text-14 payment-text-black payment-font-bold"},s.label),_().createElement("div",{className:"payment-mt-15 payment-text-left sm:payment-text-center"},s.items.map(function(e){return _().createElement("div",{className:"payment-flex sm:payment-justify-center payment-mb-5 last:payment-mb-0 payment-gap-5"},_().createElement("span",{className:"payment-mr-3",role:"img","aria-hidden":"true"},_().createElement(pn.Icon,{iconClass:"bi_brui",iconName:"bi_check_small_flat_fin",className:"payment-text-24 payment-text-blue"})),_().createElement(pn.Text,{className:"payment-leading-18 brui-text-14 payment-mt-3 payment-text-gray"},e))})))),_().createElement("div",{className:"payment-rounded-lg payment-m-20 payment-[focus-within:ring-4]"},_().createElement(pn.Button,{variant:"primary",onClick:u},l)))},na=ta,ra=function(e){var t=e.intl;return f.createElement(na,{iconVariant:"error",errorHeaderText:t.formatMessage({id:"FAILURE_API_BAN_HEADING"}),errorHeaderTextSR:t.formatMessage({id:"FAILURE_API_BAN_HEADING_SR"}),errorText:f.createElement(f.Fragment,null,f.createElement("p",null,t.formatMessage({id:"FAILURE_API_BAN_MAIN_DESC"}),"  "),f.createElement("p",{className:"payment-mt-8"},t.formatMessage({id:"FAILURE_API_BAN_MAIN_DESC_2"}),"  ")),buttonText:t.formatMessage({id:"FAILURE_API_BAN_BUTTON"}),suggestionList:{label:t.formatMessage({id:"FAILURE_API_BAN_SUB_DESC"}),items:[t.formatMessage({id:"FAILURE_API_BAN_SUB_DESC_LISTITEM_1"}),t.formatMessage({id:"FAILURE_API_BAN_SUB_DESC_LISTITEM_2"})]},onButtonClick:function(){window.location.reload()}})},aa=(0,dn.injectIntl)(ra),ia=function(t){function n(e){var n,r,a,i,o,l,u,s,c,d,p,b=t.call(this,e)||this;return b.setCreditCardInputValue=function(e){b.setState({creditCardInputValue:e})},b.setBankInputValue=function(e){b.setState({BankInputValue:e})},b.setCurrentSection=function(e){b.setState({currentSection:e})},b.setCheckedBillItems=function(e){b.setState({checkedBillItems:e})},b.setApiSatusIsFailed=function(e){b.setState({apiSatusIsFailed:e})},b.getQueryParameter=function(e){return new URLSearchParams(document.location.search.substring(1)).get(e)},b.setCheckedCurrentBalanceItems=function(e){b.setState({checkedCurrentBalanceItems:e})},b.setAccountValues=function(e){b.setState({accountInputValues:e})},b.setNotOptedBalanceItems=function(e){b.setState({notOptedBalanceItems:e})},b.parseDOMString=function(e){return(new DOMParser).parseFromString(e,"text/html").documentElement.textContent},p=(null===(r=null===(n=e.Config)||void 0===n?void 0:n.getPaymentItem)||void 0===r?void 0:r.length)>1,b.state={localizationLoaded:!0,paymentHeadingStepState:p?"inactive":"active",currentSteps:":",currentSection:p?j.SelectBills:j.PaymentMethod,creditCardInputValue:X,BankInputValue:K,isBankSelected:!1,checkedBillItems:!1===p?null===(a=e.Config)||void 0===a?void 0:a.getPaymentItem:[],accountInputValues:!1===p?[{accountNumber:null===(o=null===(i=e.Config)||void 0===i?void 0:i.getPaymentItem[0])||void 0===o?void 0:o.Ban,subNumber:null===(u=null===(l=e.Config)||void 0===l?void 0:l.getPaymentItem[0])||void 0===u?void 0:u.subscriberId,transactionID:m(null===(c=null===(s=e.Config)||void 0===s?void 0:s.getPaymentItem[0])||void 0===c?void 0:c.Ban,null===(d=e.Config)||void 0===d?void 0:d.transactionIdArray),payBalanceAmnt:0}]:[],checkedCurrentBalanceItems:[],notOptedBalanceItems:[],apiSatusIsFailed:!1,InteracCode:""},b.onCurrentSteps=b.onCurrentSteps.bind(b),b.setIsHeadingStepActive=b.setIsHeadingStepActive.bind(b),b.setCreditCardInputValue=b.setCreditCardInputValue.bind(b),b.setBankInputValue=b.setBankInputValue.bind(b),b.setCurrentSection=b.setCurrentSection.bind(b),b.setCheckedBillItems=b.setCheckedBillItems.bind(b),b.setAccountValues=b.setAccountValues.bind(b),b.setCheckedCurrentBalanceItems=b.setCheckedCurrentBalanceItems.bind(b),b.setIsBankSelected=b.setIsBankSelected.bind(b),b.setNotOptedBalanceItems=b.setNotOptedBalanceItems.bind(b),b.setApiSatusIsFailed=b.setApiSatusIsFailed.bind(b),b.setInteracCode=b.setInteracCode.bind(b),b}return e(n,t),n.prototype.setInteracCode=function(e){this.setState({InteracCode:e})},n.prototype.onCurrentSteps=function(e){this.state.currentSteps!==e&&this.setState({currentSteps:e})},n.prototype.setIsHeadingStepActive=function(e){this.setState({paymentHeadingStepState:e})},n.prototype.setIsBankSelected=function(e){this.setState({isBankSelected:e})},n.prototype.removeSessionStorageCheckedItems=function(){var e=sessionStorage.getItem("itemsChecked");e&&e.length>0&&sessionStorage.removeItem("itemsChecked")},n.prototype.componentDidMount=function(){var e,t,n,r,a=this.getQueryParameter("code");a&&null!=a?(r=(null===(e=this.props.Config)||void 0===e?void 0:e.currentUrl)||"",window.history.pushState({path:r},"",r),this.setInteracCode(a),this.props.getInteracBankInfoAction(a),1===(null===(n=null===(t=this.props.Config)||void 0===t?void 0:t.getPaymentItem)||void 0===n?void 0:n.length)&&this.removeSessionStorageCheckedItems()):this.removeSessionStorageCheckedItems(),this.props.redirectUrlAction({})},n.prototype.render=function(){var e,t,n,r,a,i,o,l=this.props,u=l.localization,s=l.Config,c=(null===(e=null==s?void 0:s.getPaymentItem)||void 0===e?void 0:e.length)>1,m="ON"===(null==s?void 0:s.IsSingleClickEnabled),d="ON"===(null==s?void 0:s.IsAutopayCreditEnabled),p=null!==(n=null===(t=null==s?void 0:s.getPaymentItem)||void 0===t?void 0:t.some(function(e){return e.Due>0&&e.isOneTimePaymentEligible&&e.AccountType!==w.OneBill&&!e.isLastBillOnPreauth}))&&void 0!==n&&n,b=null!==(a=null===(r=null==s?void 0:s.getPaymentItem)||void 0===r?void 0:r.some(function(e){return e.Due>0&&e.isOneTimeCreditCardPaymentEnabled&&e.AccountType!==w.OneBill&&!e.isLastBillOnPreauth}))&&void 0!==a&&a,E=null===(i=null==s?void 0:s.getPaymentItem)||void 0===i?void 0:i.some(function(e){var t;return null!==(t=e.IsOnPreauthorizedPayments)&&void 0!==t&&t}),f=this.state,y=f.currentSection,N=f.creditCardInputValue,g=f.BankInputValue,A=f.isBankSelected,C=f.checkedBillItems,v=f.accountInputValues,T=f.checkedCurrentBalanceItems,h=f.notOptedBalanceItems,I=f.apiSatusIsFailed,R=f.InteracCode,S=null!==(o=null==C?void 0:C.some(function(e){return e.Due>0&&e.AccountType!==w.OneBill&&!e.isLastBillOnPreauth}))&&void 0!==o&&o;return p=p&&S&&m,b=b&&S&&m,_().createElement(dn.IntlProvider,{locale:u.locale,messages:u.messages},this.props.isLoading?_().createElement(Tr,null):_().createElement(pn.Container,null,!I&&_().createElement(_().Fragment,null,y!==j.Confirmation&&_().createElement(_().Fragment,null,_().createElement(Yr,{paymentItem:s.getPaymentItem,isShow:c,onCurrentSteps:this.onCurrentSteps,setCurrentSection:this.setCurrentSection,currentSection:y,setCheckedBillItems:this.setCheckedBillItems,paymentItems:s.getPaymentItem,setAccountValues:this.setAccountValues,accountInputValues:v,transactionIds:s.transactionIdArray}),_().createElement(gr,{paymentItem:s.getPaymentItem,isHeadingStepActive:y===j.PaymentMethod?"active":"inactive",isSingleClickEnableForPACC:p,isSingleClickEnableForPAD:b,onCurrentSteps:this.onCurrentSteps,setHeadingSteps:this.setIsHeadingStepActive,setInputValue:this.setCreditCardInputValue,inputValue:N,setInputBankValue:this.setBankInputValue,inputBankValue:g,setIsBankSelected:this.setIsBankSelected,setCurrentSection:this.setCurrentSection,currentSection:y,checkedBillItems:C,bankList:s.getBankList,accountInputValues:v,creditCardAutopayOffers:s.creditCardAutopayOffers,debitCardAutopayOffers:s.debitCardAutopayOffers,language:s.language,isInteractEnabled:s.IsInteracEnabled,IsAutopayCreditEnabled:d,InteracCode:R}),(p||b)&&_().createElement(Vr,{paymentItem:s.getPaymentItem,checkedBillItems:C,checkedCurrentBalanceItems:T,setCheckedCurrentBalanceItems:this.setCheckedCurrentBalanceItems,isShow:y===j.CurrentBalance,onCurrentSteps:this.onCurrentSteps,setCurrentSection:this.setCurrentSection,currentSection:y,language:s.language,setAccountValues:this.setAccountValues,accountInputValues:v,transactionIds:s.transactionIdArray,isBankPaymentSelected:A,setNotOptedBalanceItems:this.setNotOptedBalanceItems}),_().createElement(Sr,{isActive:y===j.TermsAndCondition,onCurrentSteps:this.onCurrentSteps,setCurrentSection:this.setCurrentSection,currentSection:y,checkedBillItems:C,paymentItem:s.getPaymentItem,province:s.province,language:s.language,userProfileProv:s.userProfileProvince,accountInputValues:v,isBankSelected:A,setApiSatusIsFailed:this.setApiSatusIsFailed,creditCardAutopayOffers:s.creditCardAutopayOffers,debitCardAutopayOffers:s.debitCardAutopayOffers})),y===j.Confirmation&&_().createElement(Jr,{paymentItem:s.getPaymentItem,checkedBillItems:C,checkedCurrentBalanceItems:T,showPaymentSummary:!0,isNewbank:!1,isPreauth:E,inputValue:N,isShow:y===j.Confirmation,inputBankValue:g,isBankPaymentSelected:A,BankList:s.getBankList,showCurrentBalance:p||b,language:s.language,accountInputValues:v,currentSection:y,notOptedBalanceItems:h,setApiSatusIsFailed:this.setApiSatusIsFailed,creditCardAutopayOffers:s.creditCardAutopayOffers,debitCardAutopayOffers:s.debitCardAutopayOffers,bankitems:[],apiSatusIsFailed:I,backCTAURL:s.flowInitUrl})),I&&_().createElement(aa,null)))},n.prototype.componentDidUpdate=function(e,t){var n,r,o=this.props.Config.pagetitle||"",l=o.replace(/Step/g,"");t.currentSteps!==this.state.currentSteps&&(o="".concat(o," ").concat(this.state.currentSteps),document.title=this.parseDOMString(o)||""),this.state.currentSection===j.Confirmation&&(n="".concat(G.Confirmation," ").concat(l),document.title=this.parseDOMString(n)||""),this.state.apiSatusIsFailed&&(r=document.getElementById("container"))&&r.childElementCount>0&&r.replaceWith.apply(r,i([],a(Array.from(r.childNodes)),!1))},n.displayName="App",n}(_().Component),oa=function(e,t){return{localization:e.localization,Config:t.Config,isLoading:e.isLoading}},la=function(e){return{redirectUrlAction:function(){e(Me({}))},getInteracBankInfoAction:function(t){e(Be({code:t}))}}},ua=(0,y.connect)(oa,la)(ia),sa=function(t){function a(e,n,r){var a=t.call(this)||this;return a.store=e,a.config=n,a}return e(a,t),a.prototype.init=function(){var e,t;this.config.setConfig(N.LoggerConfigKeys.SeverityLevel,this.config.logLevel),this.store.dispatch(he({ban:this.config.getPaymentItem[0].Ban,sub:this.config.getPaymentItem[0].subscriberId})),null!=this.config.getPaymentItem&&1===this.config.getPaymentItem.length&&(t=[{accountNumber:(e=this.config.getPaymentItem[0]).Ban,subNumber:e.subscriberId,transactionID:m(e.Ban,this.config.transactionIdArray),payBalanceAmnt:0}],this.store.dispatch(Ee({ban:e.Ban,type:e.AccountType===w.OneBill,details:t,sub:e.subscriberId})))},a.prototype.destroy=function(){this.store.destroy()},a.prototype.render=function(e){var t=this.store,n=this.config;e.render(f.createElement(y.Provider,{store:t},f.createElement(ua,{Config:n})))},n([(0,N.Widget)({namespace:"Preauth/Setup"}),r("design:paramtypes",[mn,R,N.Logger])],a)}(N.ViewWidget),ca=sa}(),c}()});
//# sourceMappingURL=widget.js.map