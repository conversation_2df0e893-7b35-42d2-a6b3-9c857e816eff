import * as React from "react";
import { Text } from "@bell/bell-ui-library";

export interface MultiBanInformationProps {
  accountinfo: string,
  className?: string,
  role?: string,
  childrole?: string,
  children: React.ReactNode,
  isLabelOnError?: boolean,
}

export const MultiBanInformation = ({ accountinfo, className, role, childrole, children, isLabelOnError }: MultiBanInformationProps) => (
  <div className={className} role={role}>
    <Text elementType="div"
      className={isLabelOnError ? "brui-font-bold brui-text-red brui-text-14 brui-leading-18 brui-mb-5" : "brui-font-bold brui-text-black brui-text-14 brui-leading-18 brui-mb-5"}
    >
      {accountinfo}
    </Text>
    <div role={childrole}>
      {children}
    </div>
  </div>
);

export default MultiBanInformation;
