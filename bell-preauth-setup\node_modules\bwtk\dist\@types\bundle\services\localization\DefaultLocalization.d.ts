import { LocalizationServices, Name, Namespace, Store, Logger, Localization, LocalizationState, ReduxError, EventStreamServices } from "../../../@types";
import { Action } from "redux-actions";
import { DefaultLocalizationServices } from "./DefaultLocalizationServices";
export declare class DefaultLocalization extends Localization {
    private widgetStore;
    private localizationServices;
    private logger;
    private stream;
    static create(localizationServices: LocalizationServices, widgetStore: Store, name: Name, namespace: Namespace): Localization;
    private widgetId;
    constructor(path: string, namespace: string, widgetStore: Store, localizationServices: DefaultLocalizationServices, logger: Logger, stream: EventStreamServices);
    private readonly fullPath;
    private skipLoadMessages;
    private networkDisabled;
    private get messagesDictionary();
    dispatchLocaleChanged(): void;
    dispatchMessagesLoaded(error?: ReduxError): void;
    createReducer(): (state: LocalizationState | undefined, action: Action<any>) => LocalizationState;
    disableNetwork(disable?: boolean): void;
    loadMessagesOnce(): void;
    loadMessages(bypassCheck?: boolean, triggerDelayedLocaleChange?: boolean): void;
    getState(): LocalizationState;
    destroy(): void;
}
