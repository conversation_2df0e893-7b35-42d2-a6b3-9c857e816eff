import { IModuleDefinition } from "../StaticWidgetLoaderServices";
export declare class ModuleLoader {
    private moduleId;
    private resolver;
    private communicator;
    private registry;
    constructor(moduleId: string, resolver: any, communicator: any, registry: {
        [key: string]: IModuleDefinition;
    });
    load(): Promise<unknown>;
    private get resolvedUrl();
    private get moduleDefinitionUrl();
}
