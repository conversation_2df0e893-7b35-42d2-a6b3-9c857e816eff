import * as React from "react";
import {HeadingStep, IconLink, Icon } from "@bell/bell-ui-library";
import { injectIntl } from "react-intl";
import {SingleRowInformation} from "../SingleRowInformation";

interface PaymentMethodSelectedProps {
  intl: any;
  isActive?: boolean;
  className?: any;
  onEditClick?: () => void;
}


const PaymentMethodSelectedComponent = ({ intl, isActive, className, onEditClick}:PaymentMethodSelectedProps) => {

  const PaymentInformationItems = [
    {
      label: intl.formatMessage({ id: "CREDIT_CARD_TYPE_LABEL" }),
      value: "Visa"
    },
    {
      label: intl.formatMessage({ id: "CREDIT_CARD_NUMBER_LABEL" }),
      value: "************1234"
    },
    {
      label: intl.formatMessage({ id: "CREDIT_CARD_NAME_LABEL" }),
      value: "<PERSON>"
    },
    {
      label: intl.formatMessage({ id: "CREDIT_CARD_EXPIRY_LABEL" }),
      value: "00/0000"
    },
  ];

  return (
  
    <div className={isActive ? "sm:payment-mb-60 payment-block" : "sm:payment-mb-15 payment-border-b payment-border-gray-4 payment-hidden"}>
      <div className="payment-flex payment-items-center payment-justify-between">
        <HeadingStep
          disableSrOnlyText={true}
          status={isActive ? "complete" : "inactive"}
          subtitle=""
          hideSubtitle
          variant="leftAlignNoStep"
          title={intl.formatMessage({id: "SELECT_PAYMENT_METHOD_HEADING"})}
          id="pre-auth-select-payment_method"
        />
        {/* show IconLink component on Review */}
        <div className="payment-pt-45">
          <IconLink
            icon={<Icon iconClass="bi_brui" iconName="bi_edit_pencil" className="brui-text-16"></Icon>}
            text={intl.formatMessage({id: "CTA_EDIT"})}
            variant="textBlue"
            size="regular"
            href=""
            position="right"
            className={["payment-flex payment-items-center !payment-text-14 !payment-leading-18", isActive ? "" : "payment-hidden"].join(" ").trim()}
            aria-describedby="pre-auth-select-payment_method"
            onClick={onEditClick}
            data-test="test"
          >
                          
          </IconLink>
        </div>
      </div>

      {/* Review section */}
      <div className={["brui-pb-45",isActive ? "" : "brui-hidden"].join(" ").trim()}>
        <div className="brui-mt-15">
          {PaymentInformationItems.map((item, index) => (
            <SingleRowInformation className={index > 0 ? "brui-mt-5" : ""} label={item.label} value={item.value} />
          ))}
        </div>
      </div>
    </div>

  );
};

export const PaymentMethodSelected = injectIntl(PaymentMethodSelectedComponent);
