{"version": 3, "file": "not.js", "sourceRoot": "", "sources": ["../../src/util/not.ts"], "names": [], "mappings": ";AAAA,aAAoB,IAAc,EAAE,OAAY;IAC9C;QACE,MAAM,CAAC,CAAC,CAAQ,OAAQ,CAAC,IAAI,CAAC,KAAK,CAAQ,OAAQ,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;IACM,OAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;IACrB,OAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;IAClC,MAAM,CAAC,OAAO,CAAC;AACjB,CAAC;AAPe,WAAG,MAOlB,CAAA", "sourcesContent": ["export function not(pred: Function, thisArg: any): Function {\n  function notPred(): any {\n    return !((<any> notPred).pred.apply((<any> notPred).thisArg, arguments));\n  }\n  (<any> notPred).pred = pred;\n  (<any> notPred).thisArg = thisArg;\n  return notPred;\n}"]}