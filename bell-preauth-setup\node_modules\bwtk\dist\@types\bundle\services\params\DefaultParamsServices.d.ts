import { ParamsServices, ParamsProvider } from "../../../@types";
export declare class DefaultParamsServices extends ParamsServices {
    createParamsProvider<TProps, TEvents>(instanceProvider: () => (TProps & TEvents)): ParamsProvider<TProps, TEvents>;
}
export declare class DefaultParamsProvider<TProps, TEvents> extends ParamsProvider<TProps, TEvents> {
    private instanceProvider;
    constructor(instanceProvider: () => (TProps & TEvents));
    get props(): TProps;
    get events(): TEvents;
}
