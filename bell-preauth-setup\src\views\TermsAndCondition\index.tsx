import * as React from "react";
import {TermsAndConditions} from "./TermsAndCondition";
import { HeadingStep } from "@bell/bell-ui-library";
import { injectIntl } from "react-intl";
import { AccountInputValues, CurrentSection, PaymentItem, SubscriberOffersWithBan , PaymentItemAccountType } from "../../models";
import { submitMultiOrderPaymentAction, OmnitureOnReview, OmnitureOnApiFailure } from "../../store/Actions";
import { connect } from "react-redux";
import { State } from "../../store";

import { IRequestStatus } from "../../models/App";
import Loader from "../Loader/Loader";

export interface TermsAndCondition {
  isActive: boolean;
  intl: any;
  onCurrentSteps: (step: any) => void;
  setCurrentSection: (section: CurrentSection) => void;
  currentSection: CurrentSection;
  checkedBillItems: PaymentItem[];
  paymentItem: PaymentItem[];
  submitFormOrder: Function;
  province: string;
  language: string;
  accountInputValues: AccountInputValues[];
  submitMultiOrderPayment: any;
  setOmnitureOnReview: Function;
  isBankSelected: boolean;
  validateMultiOrderFormStatus: IRequestStatus;
  tokenizeAndPropagateFormValuesStatus: IRequestStatus;
  setApiSatusIsFailed: Function;
  userProfileProv?: string;
  setOmnitureOnValidationFailure: Function;
  sorryDebit: boolean;
  sorryCredit: boolean;
  creditCardAutopayOffers: SubscriberOffersWithBan[];
  debitCardAutopayOffers: SubscriberOffersWithBan[];
  bankitems: any[];
}

const TermsAndConditionComponent = ({ 
  isActive, 
  intl, 
  onCurrentSteps,
  setCurrentSection,
  currentSection,
  checkedBillItems,
  submitFormOrder,
  paymentItem,
  province,
  language,
  accountInputValues,
  setOmnitureOnReview,
  isBankSelected,
  validateMultiOrderFormStatus,
  tokenizeAndPropagateFormValuesStatus,
  setApiSatusIsFailed,
  userProfileProv,
  setOmnitureOnValidationFailure,
  creditCardAutopayOffers,
  debitCardAutopayOffers,
  bankitems,
  sorryCredit,
  sorryDebit
}: TermsAndCondition) => {

  const [PADValidateStatus,setPADValidateStatus] = React.useState(IRequestStatus.IDLE);
  const [PACCValidateStatus,setPACCValidateStatus] = React.useState(IRequestStatus.IDLE);
  const checkedBanOffers = () => {
    const filteredOffer: any = [];
    debitCardAutopayOffers && debitCardAutopayOffers?.map((item) =>{
      checkedBillItems && checkedBillItems.map((billItem)=>{
        if(item.Ban === billItem.Ban){
          filteredOffer.push(item);
        }
      });
    });
           
    return filteredOffer;   
  };
  const getTotalDebitOffers = () => bankitems && bankitems.length > 1 ? checkedBanOffers().reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
             
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0): debitCardAutopayOffers && debitCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
             
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0);
  const getTotalCreditOffers = () => bankitems && bankitems.length > 1 ? checkedBanOffers().reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
             
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0): creditCardAutopayOffers && creditCardAutopayOffers.reduce((total: any, ban: { AutopayEligibleSubscribers: any[]; }) => {
             
    const offersCount = ban.AutopayEligibleSubscribers.reduce(
      (count, subscriber) => count + subscriber.autopayOffers.length,
      0
    );
    return total + offersCount;
  }, 0);
  sorryCredit=creditCardAutopayOffers && creditCardAutopayOffers.length > 0 && getTotalCreditOffers() === 0 && getTotalDebitOffers() > 0 ;
  sorryDebit=debitCardAutopayOffers && debitCardAutopayOffers.length > 0 &&  getTotalDebitOffers() === 0 && getTotalCreditOffers() > 0 ;
  const handleSubmitClick = () => {
    if (paymentItem.length > 1 && checkedBillItems && checkedBillItems.length > 0)
    {
      // checkedBillItems.map((bill) => submitFormOrder(bill.Ban, bill.AccountType === PaymentItemAccountType.OneBill, bill.subscriberId));
      submitFormOrder(checkedBillItems[0].Ban, checkedBillItems[0].AccountType === PaymentItemAccountType.OneBill, isBankSelected,sorryCredit,sorryDebit,accountInputValues,checkedBillItems[0].subscriberId);
    }
    if (paymentItem && paymentItem.length === 1)
    {
      // const item = paymentItem[0];
      // submitFormOrder(item.Ban, item.AccountType === PaymentItemAccountType.OneBill, item.subscriberId);
      submitFormOrder(checkedBillItems[0].Ban, checkedBillItems[0].AccountType === PaymentItemAccountType.OneBill, isBankSelected,sorryCredit,sorryDebit,accountInputValues,checkedBillItems[0].subscriberId);            
    }
    setCurrentSection(CurrentSection.Confirmation);
  };
  const handleCancelClick = () => {
    setCurrentSection(CurrentSection.PaymentMethod);
  };
      
  // set to monitor current steps for dynamic tile (End)
  //     if(isActive){ 
  //     let pageTitle = intl.formatMessage({id:"TERMS_AND_CONDITION_HEADING"})
  //     onCurrentSteps(pageTitle);           
  // }
  // set to monitor current steps for dynamic tile (End)

  const containerRef = React.useRef<HTMLDivElement | null>(null);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (containerRef.current && isActive) {
        const firstFocusableElement = containerRef.current.querySelector('h2');
        if (firstFocusableElement) {
          firstFocusableElement.scrollIntoView({ behavior: 'smooth' });
          (firstFocusableElement as HTMLElement).focus();
        }
      }
    }, 500); 
    
    return () => clearTimeout(timer);  
  }, [isActive]);

  React.useEffect(() => {
    if(currentSection === CurrentSection.TermsAndCondition  && (PADValidateStatus === IRequestStatus.COMPLETED || PACCValidateStatus === IRequestStatus.COMPLETED)){
      setOmnitureOnReview();
    }
  },[currentSection,PADValidateStatus, PACCValidateStatus]);

  React.useEffect(()=>{
    if (!isBankSelected){
      if(tokenizeAndPropagateFormValuesStatus !== IRequestStatus.FAILED) {
        setPACCValidateStatus(IRequestStatus.PENDING);
      } else {
        setPACCValidateStatus(tokenizeAndPropagateFormValuesStatus);
      }
    }
  },[tokenizeAndPropagateFormValuesStatus]);

  React.useEffect(()=>{
    if (isBankSelected) {
      setPADValidateStatus(validateMultiOrderFormStatus);
      if (validateMultiOrderFormStatus === IRequestStatus.FAILED) {
        setApiSatusIsFailed(true);
        setOmnitureOnValidationFailure();
      }
    } else {
      setPACCValidateStatus(validateMultiOrderFormStatus);
    }
  },[validateMultiOrderFormStatus]);
     
  React.useEffect(()=>{
    if (PACCValidateStatus === IRequestStatus.FAILED) {
      setApiSatusIsFailed(true);
      setOmnitureOnValidationFailure();
    }
  },[PACCValidateStatus]);

  return (
    <>

      {((PADValidateStatus === IRequestStatus.PENDING) ||
                (PACCValidateStatus === IRequestStatus.PENDING)) &&
                <Loader></Loader>
      }

      <>
        {/* Terms and condition */}
        {/* NOTE: when Terms and condition section is active, remove border (brui-border-b brui-border-gray-4) */}
        {/* and update margin (brui-mb-15 to sm:brui-mb-60) */}
        <div ref={containerRef} className={"sm:payment-mb-[90px] payment-border-b payment-border-gray-4"}>
          <div id="termsAndCondDivID" className={isActive ? "focus-visible:payment-outline-none" : ""}>
            <HeadingStep
              disableSrOnlyText={true}
              tabIndex={-1}
              className="focus-visible:payment-outline-none"
              status={isActive ? "active" : "inactive"}
              subtitle=""
              hideSubtitle
              variant="leftAlignNoStep"
              title={(province === "QC" && language === "en") ? intl.formatMessage({ id: "TERMS_AND_CONDITION_HEADING_QC" }) : intl.formatMessage({ id: "TERMS_AND_CONDITION_HEADING" })}
            />
          </div>

          {/* Terms and condition content */}
          <div className={["payment-pt-15 sm:payment-pt-5", isActive ? "" : "payment-hidden"].join(" ").trim()}>
            <TermsAndConditions onSubmitClick={handleSubmitClick}
              onCancelClick={handleCancelClick}
              collapseHeightDynamic={{
                mobile: { height: "234px" },
                tablet: { height: "90px" },
                desktop: { height: "90px" }
              }}
              expandHeightDynamic={{
                mobile: { height: "415px" },
                tablet: { height: "460px" },
                desktop: { height: "460px" }
              }}
              province={province}
              language={language}
              userProfileProv={userProfileProv}
            />
          </div>
        </div>
      </>


    </>
  );
};

const mapStateToProps = (state: State) => ({
  submitMultiOrderPayment: state.submitMultiOrderPayment,
  validateMultiOrderFormStatus: state.validateMultiOrderFormStatus,
  tokenizeAndPropagateFormValuesStatus: state.tokenizeAndPropagateFormValuesStatus
});

const mapDispatchToProps = (dispatch: React.Dispatch<any>) => ({
  submitFormOrder: (ban: string, type: boolean,isbankSelected:boolean,sorryCredit:boolean,sorryDebit:boolean, details: any, sub?: string) => {dispatch(submitMultiOrderPaymentAction({ban, type,isbankSelected,sorryCredit,sorryDebit, details,sub}));},

  setOmnitureOnReview: () => {dispatch(OmnitureOnReview({}));},
  setOmnitureOnValidationFailure: (data? : any) => dispatch(OmnitureOnApiFailure({data})),
});


export const TermsAndCondition = connect(mapStateToProps, mapDispatchToProps)(injectIntl(TermsAndConditionComponent));
