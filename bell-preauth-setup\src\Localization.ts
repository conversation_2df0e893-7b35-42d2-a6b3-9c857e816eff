import { Injectable, CommonFeatures, MessagesDictionary } from "bwtk";

const { BaseLocalization } = CommonFeatures;

@Injectable
export class Localization extends BaseLocalization {
  // If localization preloading is used, remove this method
  // Delete this method before going into production.
  // Default messages are only used for development and local testing
  get defaultMessages(): MessagesDictionary {
    return require("./default-messages.json");
  }
}
