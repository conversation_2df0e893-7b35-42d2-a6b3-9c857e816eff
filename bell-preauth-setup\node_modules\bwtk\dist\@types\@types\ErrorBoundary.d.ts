import * as React from "react";
export interface IErrorBoundaryProps {
    errorMessage?: string;
    displayErrorMessage?: boolean;
    [prop: string]: any;
}
export interface IErrorBoundaryState {
    hasError: boolean;
}
export declare class ErrorBoundary extends React.Component<IErrorBoundaryProps, IErrorBoundaryState> {
    static displayName: string;
    static contextTypes: {
        store: ObjectConstructor;
    };
    constructor(props: IErrorBoundaryProps);
    componentDidCatch(error: any, info: any): void;
    render(): any;
}
