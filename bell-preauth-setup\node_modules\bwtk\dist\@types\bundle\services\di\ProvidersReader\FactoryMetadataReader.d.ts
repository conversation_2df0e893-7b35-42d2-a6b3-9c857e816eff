import { FactoryMetadata } from "../../../../@types";
export declare class FactoryMetadataReader implements FactoryMetadata {
    constructor(target: any);
    private readonly _metadata;
    get target(): Function;
    get propertyKey(): string;
    private get metadata();
    static isFactory(constructor: any): boolean;
    private static readFactoryMetadata;
}
export declare class NoFactoryDecoratorError extends Error {
    static Message: string;
    constructor();
}
