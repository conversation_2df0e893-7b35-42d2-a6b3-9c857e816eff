{"version": 3, "file": "isScheduler.js", "sourceRoot": "", "sources": ["../../src/util/isScheduler.ts"], "names": [], "mappings": ";AACA,qBAA4B,KAAU;IACpC,MAAM,CAAC,KAAK,IAAI,OAAa,KAAM,CAAC,QAAQ,KAAK,UAAU,CAAC;AAC9D,CAAC;AAFe,mBAAW,cAE1B,CAAA", "sourcesContent": ["import { Scheduler } from '../Scheduler';\nexport function isScheduler(value: any): value is Scheduler {\n  return value && typeof (<any>value).schedule === 'function';\n}\n"]}