{"version": 3, "file": "HotObservable.js", "sourceRoot": "", "sources": ["../../src/testing/HotObservable.ts"], "names": [], "mappings": ";;;;;;AAAA,wBAAwB,YAAY,CAAC,CAAA;AAErC,6BAA6B,iBAAiB,CAAC,CAAA;AAI/C,qCAAqC,wBAAwB,CAAC,CAAA;AAC9D,4BAA4B,qBAAqB,CAAC,CAAA;AAElD;;;;GAIG;AACH;IAAsC,iCAAU;IAM9C,uBAAmB,QAAuB,EAC9B,SAAoB;QAC9B,iBAAO,CAAC;QAFS,aAAQ,GAAR,QAAQ,CAAe;QALnC,kBAAa,GAAsB,EAAE,CAAC;QAQ3C,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED,oCAAoC,CAAC,kCAAU,GAAV,UAAW,UAA2B;QACzE,IAAM,OAAO,GAAqB,IAAI,CAAC;QACvC,IAAM,KAAK,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAC3C,UAAU,CAAC,GAAG,CAAC,IAAI,2BAAY,CAAC;YAC9B,OAAO,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC;QACJ,MAAM,CAAC,gBAAK,CAAC,UAAU,YAAC,UAAU,CAAC,CAAC;IACtC,CAAC;IAED,6BAAK,GAAL;QACE,IAAM,OAAO,GAAG,IAAI,CAAC;QACrB,IAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;QAC/C,mCAAmC;QACnC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE,CAAC;YACxC,CAAC;gBACC,IAAI,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACvC,mBAAmB;gBACd,OAAO,CAAC,SAAS,CAAC,QAAQ,CACxB,cAAQ,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAChD,OAAO,CAAC,KAAK,CACd,CAAC;YACJ,CAAC,CAAC,EAAE,CAAC;QACP,CAAC;IACH,CAAC;IACH,oBAAC;AAAD,CAAC,AApCD,CAAsC,iBAAO,GAoC5C;AApCY,qBAAa,gBAoCzB,CAAA;AACD,yBAAW,CAAC,aAAa,EAAE,CAAC,2CAAoB,CAAC,CAAC,CAAC", "sourcesContent": ["import { Subject } from '../Subject';\nimport { Subscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { Scheduler } from '../Scheduler';\nimport { TestMessage } from './TestMessage';\nimport { SubscriptionLog } from './SubscriptionLog';\nimport { SubscriptionLoggable } from './SubscriptionLoggable';\nimport { applyMixins } from '../util/applyMixins';\n\n/**\n * We need this JSDoc comment for affecting ESDoc.\n * @ignore\n * @extends {Ignored}\n */\nexport class HotObservable<T> extends Subject<T> implements SubscriptionLoggable {\n  public subscriptions: SubscriptionLog[] = [];\n  scheduler: Scheduler;\n  logSubscribedFrame: () => number;\n  logUnsubscribedFrame: (index: number) => void;\n\n  constructor(public messages: TestMessage[],\n              scheduler: Scheduler) {\n    super();\n    this.scheduler = scheduler;\n  }\n\n  /** @deprecated internal use only */ _subscribe(subscriber: Subscriber<any>): Subscription {\n    const subject: HotObservable<T> = this;\n    const index = subject.logSubscribedFrame();\n    subscriber.add(new Subscription(() => {\n      subject.logUnsubscribedFrame(index);\n    }));\n    return super._subscribe(subscriber);\n  }\n\n  setup() {\n    const subject = this;\n    const messagesLength = subject.messages.length;\n    /* tslint:disable:no-var-keyword */\n    for (var i = 0; i < messagesLength; i++) {\n      (() => {\n        var message = subject.messages[i];\n   /* tslint:enable */\n        subject.scheduler.schedule(\n          () => { message.notification.observe(subject); },\n          message.frame\n        );\n      })();\n    }\n  }\n}\napplyMixins(HotObservable, [SubscriptionLoggable]);\n"]}